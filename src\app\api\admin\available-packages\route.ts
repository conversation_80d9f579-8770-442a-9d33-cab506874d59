import { NextRequest, NextResponse } from 'next/server'
import { supabaseAdmin } from '@/lib/supabase'

export async function GET(request: NextRequest) {
  try {
    // Get all active subscription packages
    const { data: packages, error: packagesError } = await supabaseAdmin
      .from('subscription_packages')
      .select('id, name, price, currency')
      .eq('is_active', true)
      .order('price', { ascending: true })

    if (packagesError) {
      throw new Error(`Failed to fetch packages: ${packagesError.message}`)
    }

    // Get existing unified commission structures
    const { data: existingStructures, error: structuresError } = await supabaseAdmin
      .from('commission_structure')
      .select('package_value')
      .eq('commission_type', 'unified_structure')
      .eq('is_active', true)

    if (structuresError) {
      throw new Error(`Failed to fetch existing structures: ${structuresError.message}`)
    }

    // Get package values that already have structures
    const existingPackageValues = new Set(
      existingStructures?.map(s => parseFloat(s.package_value)) || []
    )

    // Filter packages that don't have commission structures yet
    const availablePackages = (packages || [])
      .filter(pkg => !existingPackageValues.has(parseFloat(pkg.price)))
      .map(pkg => ({
        id: pkg.id,
        name: pkg.name,
        value: parseFloat(pkg.price),
        currency: pkg.currency,
        display_name: `${pkg.name} (${pkg.currency} ${parseFloat(pkg.price).toLocaleString()})`
      }))

    // Also get packages that have structures for reference
    const packagesWithStructures = (packages || [])
      .filter(pkg => existingPackageValues.has(parseFloat(pkg.price)))
      .map(pkg => ({
        id: pkg.id,
        name: pkg.name,
        value: parseFloat(pkg.price),
        currency: pkg.currency,
        display_name: `${pkg.name} (${pkg.currency} ${parseFloat(pkg.price).toLocaleString()})`
      }))

    return NextResponse.json({
      success: true,
      data: {
        available_packages: availablePackages,
        packages_with_structures: packagesWithStructures,
        total_packages: packages?.length || 0,
        available_count: availablePackages.length,
        structured_count: packagesWithStructures.length
      }
    })
  } catch (error) {
    console.error('Error fetching available packages:', error)
    return NextResponse.json(
      { error: 'Failed to fetch available packages' },
      { status: 500 }
    )
  }
}
