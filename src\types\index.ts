export interface Category {
  id: string
  name: string
  slug: string
  description?: string
  icon?: string
  subcategories: Subcategory[]
  adCount?: number
  created_at: string
  updated_at: string
}

export interface Subcategory {
  id: string
  name: string
  slug: string
  category_id: string
  description?: string
  created_at: string
  updated_at: string
}

export interface Ad {
  id: string
  title: string
  description: string
  price: number
  currency: string
  category_id: string
  subcategory_id: string
  user_id: string
  location: string
  condition?: 'new' | 'used' | 'refurbished' | null
  status: 'active' | 'sold' | 'expired' | 'draft' | 'pending' | 'rejected'
  images: string[]
  contact_phone?: string
  contact_email?: string
  negotiable: boolean
  featured: boolean
  views: number
  is_edited?: boolean
  edit_reason?: string
  original_data?: any
  created_at: string
  updated_at: string
  expires_at: string

  // Category-specific fields
  category_fields?: Record<string, any>
  main_category?: 'sell' | 'rent' | 'jobs'

  // Boost system fields
  is_boosted?: boolean
  boost_expires_at?: string

  // Job-specific fields
  job_type?: string
  salary_range_from?: number
  salary_range_to?: number
  application_method?: 'email' | 'phone'
  application_deadline?: string
  employer_name?: string
  employer_website?: string
  employer_logo_url?: string

  // Rental-specific fields
  rental_type?: 'daily' | 'monthly' | 'yearly'
  availability_from?: string
  availability_to?: string
}

export interface User {
  id: string
  user_id: number // Unique OKDOI ID starting from 1000
  email: string
  full_name?: string
  phone?: string
  location?: string
  gender?: string
  religion?: string
  avatar_url?: string
  is_verified?: boolean
  referral_code?: string
  total_referrals?: number
  total_rewards?: number
  banned_until?: string
  // New referral system fields
  user_type?: 'okdoi_head' | 'zonal_manager' | 'rsm' | 'user'
  referred_by_id?: string
  referral_level?: number
  referral_path?: string
  direct_referrals_count?: number
  total_downline_count?: number
  total_commission_earned?: number
  is_referral_active?: boolean
  created_at: string
  updated_at: string
}

// Referral & Commission System Types
export interface ReferralHierarchy {
  id: string
  user_id: string
  ancestor_id: string
  level_difference: number
  created_at: string
}

export interface CommissionStructure {
  id: string
  package_value: number
  commission_type: string
  level_1_rate: number
  level_2_rate: number
  level_3_rate: number
  level_4_rate: number
  level_5_rate: number
  level_6_rate: number
  level_7_rate: number
  level_8_rate: number
  level_9_rate: number
  level_10_rate: number
  is_active: boolean
  created_at: string
  updated_at: string
}

// New extended commission structure with all commission types
export interface ExtendedCommissionStructure {
  id: string
  commission_type: string
  package_value: number
  direct_commission_rate: number
  level_commission_rate: number
  voucher_rate: number
  festival_bonus_rate: number
  saving_rate: number
  gift_center_rate: number
  entertainment_rate: number
  medical_rate: number
  education_rate: number
  credit_rate: number
  zm_bonus_rate: number
  zm_petral_allowance_rate: number
  zm_leasing_facility_rate: number
  zm_phone_bill_rate: number
  rsm_bonus_rate: number
  rsm_petral_allowance_rate: number
  rsm_leasing_facility_rate: number
  rsm_phone_bill_rate: number
  present_user_rate: number
  present_leader_rate: number
  annual_present_user_rate: number
  annual_present_leader_rate: number
  okdoi_head_rate_2000: number
  okdoi_head_rate_5000: number
  okdoi_head_rate_10000: number
  okdoi_head_rate_50000: number
  is_active: boolean
  created_at: string
  updated_at: string
}

export interface CommissionTransaction {
  id: string
  transaction_id: string
  user_id: string
  beneficiary_id: string
  subscription_purchase_id?: string
  commission_type: string
  commission_level: number
  package_value: number
  commission_rate: number
  commission_amount: number
  currency: string
  status: 'pending' | 'processed' | 'failed' | 'cancelled'
  processed_at?: string
  wallet_transaction_id?: string
  metadata: Record<string, any>
  created_at: string
  updated_at: string
}

export interface ReferralPlacement {
  id: string
  parent_id: string
  child_id: string
  position: number
  placement_type: 'direct' | 'spillover'
  created_at: string
}

export interface ZonalManager {
  id: string
  user_id: string
  zone_name: string
  zone_description?: string
  assigned_districts: string[]
  is_active: boolean
  created_by?: string
  created_at: string
  updated_at: string
}

export interface RegionalSalesManager {
  id: string
  user_id: string
  zonal_manager_id?: string
  region_name?: string
  is_active: boolean
  upgraded_by?: string
  upgraded_at: string
  created_at: string
  updated_at: string
}

export interface ChatConversation {
  id: string
  ad_id: string
  buyer_id: string
  seller_id: string
  last_message_at: string
  buyer_unread_count: number
  seller_unread_count: number
  status: 'active' | 'archived' | 'blocked'
  created_at: string
  updated_at: string
  ad?: Ad
  buyer?: User
  seller?: User
  last_message?: ChatMessage
}

export interface ChatMessage {
  id: string
  conversation_id: string
  sender_id: string
  message: string
  message_type: 'text' | 'image' | 'system'
  is_read: boolean
  created_at: string
  updated_at: string
  sender?: User
}

export interface AdImage {
  id: string
  ad_id: string
  image_url: string
  sort_order: number
  created_at: string
}

export interface AdWithDetails extends Ad {
  category: Category
  subcategory: Subcategory
  user: Pick<User, 'id' | 'full_name' | 'phone' | 'email' | 'avatar_url' | 'created_at'>
  ad_images?: AdImage[]
}

export interface SearchFilters {
  category?: string
  category_id?: string
  subcategory?: string
  subcategory_id?: string
  location?: string
  minPrice?: number
  maxPrice?: number
  min_price?: string
  max_price?: string
  condition?: string
  sortBy?: 'newest' | 'oldest' | 'price_low' | 'price_high' | 'most_viewed' | string
  sort_by?: string
  sort_order?: 'asc' | 'desc'
  userId?: string
  status?: 'active' | 'sold' | 'expired' | 'draft' | 'pending' | 'rejected'
}

export interface PaginationMeta {
  page: number
  limit: number
  total: number
  totalPages: number
}

// Subscription System Types
export interface SubscriptionPackage {
  id: string
  name: string
  description?: string
  price: number
  currency: string
  ad_limit: number
  boost_limit: number
  duration_days: number
  is_active: boolean
  sort_order: number
  created_at: string
  updated_at: string
}

export interface UserSubscription {
  id: string
  user_id: string
  package_id: string
  status: 'active' | 'expired' | 'cancelled'
  ads_used: number
  boosts_used: number
  purchased_at: string
  expires_at: string
  commission_processed?: boolean
  present_processed?: boolean
  processed_at?: string
  created_at: string
  updated_at: string
  package?: SubscriptionPackage
}

export interface AdBoost {
  id: string
  ad_id: string
  user_id: string
  subscription_id: string
  boosted_at: string
  expires_at: string
  is_active: boolean
  created_at: string
  updated_at: string
}

export interface BoostPackage {
  id: string
  name: string
  description: string
  duration_days: number
  price: number
  currency: string
  is_active: boolean
  sort_order: number
  created_at: string
  updated_at: string
}

export interface SubscriptionUsage {
  ads_remaining: number
  boosts_remaining: number
  ads_used: number
  boosts_used: number
  subscription_expires_at: string | null
  has_active_subscription: boolean
}

export interface VendorShop {
  id: string
  user_id: string
  name: string
  slug: string
  description?: string
  logo_url?: string
  banner_url?: string
  contact_email?: string
  contact_phone?: string
  address?: string
  website_url?: string
  social_links?: Record<string, string>
  status: 'pending' | 'approved' | 'rejected' | 'suspended'
  is_featured: boolean
  rating: number
  total_reviews: number
  total_products: number
  total_sales: number
  total_views: number
  created_at: string
  updated_at: string
  user?: User
}

export interface ShopCategory {
  id: string
  name: string
  slug: string
  description?: string
  icon?: string
  image_url?: string
  sort_order: number
  is_active: boolean
  created_at: string
  updated_at: string
  subcategories?: ShopSubcategory[]
}

export interface ShopSubcategory {
  id: string
  category_id: string
  name: string
  slug: string
  description?: string
  sort_order: number
  is_active: boolean
  created_at: string
  updated_at: string
  category?: ShopCategory
}

export interface ShopProduct {
  id: string
  shop_id: string
  category_id?: string
  subcategory_id?: string
  title: string
  description: string
  price: number
  currency: string
  condition: 'new' | 'used' | 'refurbished'
  sku?: string
  stock_quantity: number
  min_order_quantity: number
  weight?: number
  dimensions?: {
    length: number
    width: number
    height: number
  }
  shipping_info?: Record<string, any>
  variants?: Array<{
    name: string
    options: string[]
  }>
  is_digital: boolean
  download_url?: string
  contact_phone?: string
  negotiable: boolean
  status: 'active' | 'inactive' | 'out_of_stock' | 'draft'
  views: number
  average_rating: number
  total_reviews: number
  created_at: string
  updated_at: string
  shop?: VendorShop
  category?: ShopCategory
  subcategory?: ShopSubcategory
  images?: ShopProductImage[]
  reviews?: ProductReview[]
}

export interface ShopProductImage {
  id: string
  product_id: string
  image_url: string
  alt_text?: string
  sort_order: number
  is_primary: boolean
  created_at: string
}

export interface ProductReview {
  id: string
  product_id: string
  user_id: string
  rating: number
  review_text?: string
  is_verified: boolean
  helpful_count: number
  created_at: string
  updated_at: string
  user?: User
  product?: ShopProduct
}

export interface ShopReview {
  id: string
  shop_id: string
  user_id: string
  rating: number
  review_text?: string
  is_verified: boolean
  created_at: string
  updated_at: string
  user?: User
}

export interface ShopFollower {
  id: string
  shop_id: string
  user_id: string
  created_at: string
  user?: User
}

// Order Management System Types
export interface CartItem {
  id: string
  user_id: string
  product_id: string
  quantity: number
  selected_variant: Record<string, any>
  created_at: string
  updated_at: string
  product?: ShopProduct
}

export interface ShopOrder {
  id: string
  order_number: string
  buyer_id: string
  shop_id: string
  seller_id: string
  subtotal: number
  shipping_cost: number
  tax_amount: number
  discount_amount: number
  total_amount: number
  currency: string
  status: 'pending' | 'pending_shipment' | 'shipped' | 'delivered' | 'cancelled' | 'refunded'
  payment_status: 'pending' | 'paid' | 'failed' | 'refunded'
  payment_method: 'wallet'
  shipping_address: {
    name: string
    phone: string
    address: string
    city: string
    district: string
    postal_code: string
  }
  billing_address?: {
    name: string
    phone: string
    address: string
    city: string
    district: string
    postal_code: string
  }
  tracking_number?: string
  courier_name?: string
  tracking_url?: string
  estimated_delivery_date?: string
  delivered_at?: string
  buyer_notes?: string
  seller_notes?: string
  admin_notes?: string
  created_at: string
  updated_at: string
  confirmed_at?: string
  shipped_at?: string
  cancelled_at?: string
  wallet_transaction_id?: string
  // Relations
  buyer?: User
  seller?: User
  shop?: VendorShop
  order_items?: OrderItem[]
}

export interface OrderItem {
  id: string
  order_id: string
  product_id: string
  product_title: string
  product_description?: string
  product_sku?: string
  selected_variant: Record<string, any>
  unit_price: number
  quantity: number
  total_price: number
  currency: string
  status: 'pending' | 'confirmed' | 'processing' | 'shipped' | 'delivered' | 'cancelled' | 'refunded'
  created_at: string
  updated_at: string
  product?: ShopProduct
}

export interface OrderStatusHistory {
  id: string
  order_id: string
  status: string
  notes?: string
  changed_by?: string
  changed_at: string
  user?: User
}

// Wallet System Types
export interface UserWallet {
  id: string
  user_id: string
  balance: number
  currency: string
  is_active: boolean
  created_at: string
  updated_at: string
}

export interface WalletTransaction {
  id: string
  wallet_id: string
  transaction_type: 'deposit' | 'withdrawal' | 'transfer_in' | 'transfer_out' | 'purchase' | 'refund' | 'commission'
  amount: number
  currency: string
  balance_before: number
  balance_after: number
  reference_id?: string
  reference_type?: string
  description?: string
  metadata?: Record<string, any>
  status: 'pending' | 'completed' | 'failed' | 'cancelled'
  created_at: string
  updated_at: string
}

export interface P2PTransfer {
  id: string
  sender_id: string
  receiver_id: string
  amount: number
  currency: string
  description?: string
  status: 'pending' | 'completed' | 'failed' | 'cancelled'
  reference_number?: string
  sender_transaction_id?: string
  receiver_transaction_id?: string
  created_at: string
  updated_at: string
  sender?: User
  receiver?: User
}

export interface DepositRequest {
  id: string
  user_id: string
  amount: number
  currency: string
  depositor_name?: string
  bank_name?: string
  account_holder_name?: string
  account_number?: string
  transaction_reference?: string
  deposit_slip_url?: string
  notes?: string
  reference_number?: string
  status: 'pending' | 'approved' | 'rejected'
  admin_notes?: string
  approved_by?: string
  approved_at?: string
  wallet_transaction_id?: string
  created_at: string
  updated_at: string
  user?: User
  approver?: User
}

export interface WithdrawalRequest {
  id: string
  user_id: string
  amount: number
  currency: string
  bank_name: string
  account_number: string
  account_holder_name: string
  branch?: string
  notes?: string
  status: 'pending' | 'approved' | 'rejected' | 'processed'
  admin_notes?: string
  approved_by?: string
  approved_at?: string
  processed_by?: string
  processed_at?: string
  reference_number?: string
  transaction_reference?: string
  wallet_transaction_id?: string
  created_at: string
  updated_at: string
  user?: User
  approver?: User
  processor?: User
}
