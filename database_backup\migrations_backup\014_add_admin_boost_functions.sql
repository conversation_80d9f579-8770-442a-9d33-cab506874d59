-- Add admin boost and expire boost functions
-- These functions allow admins to manually boost ads and expire boosts

-- Function for admin to boost an ad manually
CREATE OR REPLACE FUNCTION admin_boost_ad(p_ad_id uuid, p_days integer)
RETURNS void AS $$
DECLARE
    v_admin_user_id uuid;
    v_boost_expires_at timestamp with time zone;
BEGIN
    -- Get the current admin user ID
    v_admin_user_id := auth.uid();
    
    -- Check if user is admin
    IF NOT is_admin(v_admin_user_id) THEN
        RAISE EXCEPTION 'Access denied. Admin privileges required.';
    END IF;
    
    -- Check if ad exists and is active
    IF NOT EXISTS (SELECT 1 FROM ads WHERE id = p_ad_id AND status = 'active') THEN
        RAISE EXCEPTION 'Ad not found or not active';
    END IF;
    
    -- Check if ad is already boosted
    IF EXISTS (SELECT 1 FROM ads WHERE id = p_ad_id AND is_boosted = true AND boost_expires_at > NOW()) THEN
        RAISE EXCEPTION 'Ad is already boosted';
    END IF;
    
    -- Calculate boost expiration
    v_boost_expires_at := NOW() + (p_days || ' days')::interval;
    
    -- Get the ad owner's user_id for the boost record
    -- Create a dummy subscription record for admin boosts (or use existing active subscription)
    INSERT INTO ad_boosts (
        ad_id,
        user_id,
        subscription_id,
        expires_at,
        boosted_at,
        is_active
    )
    SELECT 
        p_ad_id,
        ads.user_id,
        COALESCE(
            (SELECT id FROM user_subscriptions 
             WHERE user_id = ads.user_id 
             AND status = 'active' 
             AND expires_at > NOW() 
             ORDER BY expires_at DESC 
             LIMIT 1),
            -- If no active subscription, create a special admin boost record
            -- We'll use a NULL subscription_id to indicate admin boost
            NULL
        ),
        v_boost_expires_at,
        NOW(),
        true
    FROM ads 
    WHERE ads.id = p_ad_id;
    
    -- Update the ad to mark as boosted
    UPDATE ads 
    SET 
        is_boosted = true,
        boost_expires_at = v_boost_expires_at,
        updated_at = NOW()
    WHERE id = p_ad_id;
    
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function for admin to expire boost manually
CREATE OR REPLACE FUNCTION admin_expire_boost(p_ad_id uuid)
RETURNS void AS $$
DECLARE
    v_admin_user_id uuid;
BEGIN
    -- Get the current admin user ID
    v_admin_user_id := auth.uid();
    
    -- Check if user is admin
    IF NOT is_admin(v_admin_user_id) THEN
        RAISE EXCEPTION 'Access denied. Admin privileges required.';
    END IF;
    
    -- Check if ad exists
    IF NOT EXISTS (SELECT 1 FROM ads WHERE id = p_ad_id) THEN
        RAISE EXCEPTION 'Ad not found';
    END IF;
    
    -- Check if ad is currently boosted
    IF NOT EXISTS (SELECT 1 FROM ads WHERE id = p_ad_id AND is_boosted = true) THEN
        RAISE EXCEPTION 'Ad is not currently boosted';
    END IF;
    
    -- Expire all active boosts for this ad
    UPDATE ad_boosts 
    SET 
        is_active = false,
        updated_at = NOW()
    WHERE ad_id = p_ad_id 
    AND is_active = true;
    
    -- Update the ad to remove boost status
    UPDATE ads 
    SET 
        is_boosted = false,
        boost_expires_at = NULL,
        updated_at = NOW()
    WHERE id = p_ad_id;
    
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Allow admin boost records with NULL subscription_id
-- Modify the ad_boosts table to allow NULL subscription_id for admin boosts
ALTER TABLE ad_boosts ALTER COLUMN subscription_id DROP NOT NULL;

-- Add a comment to document admin boosts
COMMENT ON COLUMN ad_boosts.subscription_id IS 'Subscription ID for user boosts, NULL for admin boosts';

-- Grant execute permissions to authenticated users (admin check is done within function)
GRANT EXECUTE ON FUNCTION admin_boost_ad(uuid, integer) TO authenticated;
GRANT EXECUTE ON FUNCTION admin_expire_boost(uuid) TO authenticated;
