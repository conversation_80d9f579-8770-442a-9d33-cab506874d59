"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/auth/signin/page",{

/***/ "(app-pages-browser)/./src/lib/auth.ts":
/*!*************************!*\
  !*** ./src/lib/auth.ts ***!
  \*************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthService: function() { return /* binding */ AuthService; }\n/* harmony export */ });\n/* harmony import */ var _supabase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./supabase */ \"(app-pages-browser)/./src/lib/supabase.ts\");\n/* harmony import */ var _services_adminSettings__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./services/adminSettings */ \"(app-pages-browser)/./src/lib/services/adminSettings.ts\");\n/* harmony import */ var _services_referralSystem__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./services/referralSystem */ \"(app-pages-browser)/./src/lib/services/referralSystem.ts\");\n\n\n\nclass AuthService {\n    /**\n   * Sign up a new user with OTP verification and referral processing\n   */ static async signUp(email, password, userData) {\n        // Check if email verification is required (with fallback to false if table doesn't exist)\n        let requireEmailVerification = false;\n        try {\n            requireEmailVerification = await _services_adminSettings__WEBPACK_IMPORTED_MODULE_1__.AdminSettingsService.getSetting(\"require_email_verification\") || false;\n        } catch (error) {\n            // Gracefully handle admin settings access failures\n            console.warn(\"Could not fetch email verification setting, defaulting to false:\", error);\n            requireEmailVerification = false;\n            // If it's a 406 error or RLS policy error, it means the table exists but access is restricted\n            // This is expected during signup when user is not authenticated yet\n            if (error instanceof Error && (error.message.includes(\"406\") || error.message.includes(\"policy\"))) {\n                console.info(\"Admin settings access restricted during signup - using default values\");\n            }\n        }\n        // Validate referral code if provided (check both camelCase and snake_case)\n        let referrer = null;\n        const referralCode = (userData === null || userData === void 0 ? void 0 : userData.referralCode) || (userData === null || userData === void 0 ? void 0 : userData.referral_code);\n        if (referralCode) {\n            try {\n                console.log(\"Validating referral code:\", referralCode);\n                referrer = await _services_referralSystem__WEBPACK_IMPORTED_MODULE_2__.ReferralSystemService.validateReferralCode(referralCode);\n                if (!referrer) {\n                    console.warn(\"Invalid referral code:\", referralCode);\n                    throw new Error(\"Invalid referral code\");\n                }\n                console.log(\"✅ Referral code validated successfully. Referrer:\", referrer.email);\n            } catch (error) {\n                console.warn(\"Could not validate referral code:\", error);\n            // Don't fail signup for referral code issues, just log the warning\n            }\n        }\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.signUp({\n            email,\n            password,\n            options: {\n                data: {\n                    ...userData,\n                    referral_code: referralCode,\n                    full_name: userData === null || userData === void 0 ? void 0 : userData.full_name,\n                    phone: userData === null || userData === void 0 ? void 0 : userData.phone,\n                    location: userData === null || userData === void 0 ? void 0 : userData.location,\n                    gender: userData === null || userData === void 0 ? void 0 : userData.gender,\n                    religion: userData === null || userData === void 0 ? void 0 : userData.religion,\n                    referrer_id: referrer === null || referrer === void 0 ? void 0 : referrer.id,\n                    referrer_email: referrer === null || referrer === void 0 ? void 0 : referrer.email // Store referrer info for debugging\n                }\n            }\n        });\n        if (error) {\n            throw new Error(error.message);\n        }\n        // Always create user profile immediately (regardless of email verification)\n        if (data.user) {\n            try {\n                // Create profile with referral information if available\n                if (referrer) {\n                    await this.createUserProfileWithReferrer(data.user, userData, referrer);\n                } else {\n                    await this.createBasicUserProfile(data.user, userData);\n                }\n            } catch (profileError) {\n                console.error(\"Error creating user profile:\", profileError);\n            // Don't fail the signup, but log the error\n            }\n        }\n        return {\n            data,\n            requireEmailVerification,\n            referrer\n        };\n    }\n    /**\n   * Create user profile with referrer information and hierarchy placement\n   */ static async createUserProfileWithReferrer(authUser, userData, referrer) {\n        console.log(\"Creating user profile with referrer for:\", authUser.email);\n        try {\n            // Calculate referral path for the new user\n            const referrerPath = (referrer === null || referrer === void 0 ? void 0 : referrer.referral_path) || \"\";\n            const newUserPath = referrerPath ? \"\".concat(referrerPath, \"/\").concat(authUser.id) : \"/\".concat(authUser.id);\n            // Use server-side API route for profile creation with referrer data\n            const response = await fetch(\"/api/auth/create-profile\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    userId: authUser.id,\n                    email: authUser.email,\n                    userData: {\n                        full_name: userData === null || userData === void 0 ? void 0 : userData.full_name,\n                        phone: userData === null || userData === void 0 ? void 0 : userData.phone,\n                        location: userData === null || userData === void 0 ? void 0 : userData.location,\n                        referral_level: referrer ? (referrer.referral_level || 0) + 1 : 0,\n                        referred_by_id: (referrer === null || referrer === void 0 ? void 0 : referrer.id) || null,\n                        referral_path: newUserPath\n                    }\n                })\n            });\n            if (!response.ok) {\n                const errorData = await response.json();\n                throw new Error(errorData.error || \"Failed to create user profile\");\n            }\n            // Place user in referral hierarchy immediately after profile creation\n            if (referrer) {\n                try {\n                    console.log(\"\\uD83D\\uDD17 Placing user in referral hierarchy...\");\n                    console.log(\"New user ID:\", authUser.id);\n                    console.log(\"Referrer ID:\", referrer.id);\n                    // Check if user is already placed to prevent duplicates\n                    const { data: existingPlacement } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"referral_placements\").select(\"id\").eq(\"child_id\", authUser.id).single();\n                    if (existingPlacement) {\n                        console.log(\"✅ User already placed in referral hierarchy\");\n                    } else {\n                        await _services_referralSystem__WEBPACK_IMPORTED_MODULE_2__.ReferralSystemService.placeUserInHierarchy(authUser.id, referrer.id);\n                        console.log(\"✅ User placed in referral hierarchy successfully\");\n                    }\n                    // Verify the placement worked\n                    const { data: verifyUser } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"users\").select(\"referred_by_id, referral_level, direct_referrals_count\").eq(\"id\", authUser.id).single();\n                    console.log(\"✅ Referral linking verification:\", {\n                        userId: authUser.id,\n                        referredById: verifyUser === null || verifyUser === void 0 ? void 0 : verifyUser.referred_by_id,\n                        referralLevel: verifyUser === null || verifyUser === void 0 ? void 0 : verifyUser.referral_level,\n                        expectedReferrerId: referrer.id\n                    });\n                } catch (referralError) {\n                    console.error(\"❌ Failed to place user in referral hierarchy:\", referralError);\n                // Don't fail the profile creation, but log the error\n                }\n            } else {\n                console.log(\"ℹ️ No referrer found, user will not be placed in hierarchy\");\n            }\n            console.log(\"✅ User profile with referrer created successfully\");\n        } catch (error) {\n            console.error(\"Error creating user profile with referrer:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Create basic user profile without complex operations (fast)\n   * Uses server-side API route to bypass RLS issues during signup\n   */ static async createBasicUserProfile(authUser, userData) {\n        console.log(\"Creating basic user profile for:\", authUser.email);\n        const maxRetries = 3;\n        let lastError = null;\n        for(let attempt = 1; attempt <= maxRetries; attempt++){\n            try {\n                console.log(\"Profile creation attempt \".concat(attempt, \"/\").concat(maxRetries));\n                // Use server-side API route for profile creation\n                const response = await fetch(\"/api/auth/create-profile\", {\n                    method: \"POST\",\n                    headers: {\n                        \"Content-Type\": \"application/json\"\n                    },\n                    body: JSON.stringify({\n                        userId: authUser.id,\n                        email: authUser.email,\n                        userData: {\n                            full_name: userData === null || userData === void 0 ? void 0 : userData.full_name,\n                            phone: userData === null || userData === void 0 ? void 0 : userData.phone,\n                            location: userData === null || userData === void 0 ? void 0 : userData.location,\n                            referral_level: 0,\n                            referred_by_id: null,\n                            referral_path: \"/\".concat(authUser.id) // Set basic path for user\n                        }\n                    })\n                });\n                if (!response.ok) {\n                    const errorData = await response.json();\n                    const errorMessage = errorData.error || \"Failed to create user profile\";\n                    // Don't retry for client errors (4xx)\n                    if (response.status >= 400 && response.status < 500) {\n                        throw new Error(errorMessage);\n                    }\n                    // Retry for server errors (5xx)\n                    lastError = new Error(errorMessage);\n                    console.warn(\"Profile creation attempt \".concat(attempt, \" failed:\"), errorMessage);\n                    if (attempt < maxRetries) {\n                        // Wait before retry with exponential backoff\n                        const delay = Math.min(1000 * Math.pow(2, attempt - 1), 5000);\n                        console.log(\"Retrying in \".concat(delay, \"ms...\"));\n                        await new Promise((resolve)=>setTimeout(resolve, delay));\n                        continue;\n                    }\n                } else {\n                    const result = await response.json();\n                    console.log(\"✅ Basic user profile created successfully:\", result.message);\n                    return; // Success, exit the retry loop\n                }\n            } catch (error) {\n                lastError = error instanceof Error ? error : new Error(\"Unknown error\");\n                console.error(\"Profile creation attempt \".concat(attempt, \" failed:\"), lastError.message);\n                // Don't retry for network errors that are likely permanent\n                if (lastError.message.includes(\"fetch\") && attempt === 1) {\n                    break;\n                }\n                if (attempt < maxRetries) {\n                    // Wait before retry\n                    const delay = Math.min(1000 * attempt, 3000);\n                    await new Promise((resolve)=>setTimeout(resolve, delay));\n                }\n            }\n        }\n        // If we get here, all attempts failed\n        console.error(\"All profile creation attempts failed\");\n        throw lastError || new Error(\"Failed to create user profile after multiple attempts\");\n    }\n    /**\n   * Schedule referral placement for later processing (non-blocking)\n   */ static async scheduleReferralPlacement(userId, referrerId) {\n        try {\n            // Use a timeout to defer this operation\n            setTimeout(async ()=>{\n                try {\n                    await _services_referralSystem__WEBPACK_IMPORTED_MODULE_2__.ReferralSystemService.placeUserInHierarchy(userId, referrerId);\n                    console.log(\"Referral placement completed for user:\", userId);\n                } catch (error) {\n                    console.error(\"Deferred referral placement failed:\", error);\n                }\n            }, 2000) // 2 second delay\n            ;\n        } catch (error) {\n            console.error(\"Failed to schedule referral placement:\", error);\n        }\n    }\n    /**\n   * Create user profile in public.users table (legacy method with full operations)\n   */ static async createUserProfile(authUser, userData, referrer) {\n        try {\n            // Use server-side API route for profile creation with referrer data\n            const response = await fetch(\"/api/auth/create-profile\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    userId: authUser.id,\n                    email: authUser.email,\n                    userData: {\n                        full_name: userData === null || userData === void 0 ? void 0 : userData.full_name,\n                        phone: userData === null || userData === void 0 ? void 0 : userData.phone,\n                        location: userData === null || userData === void 0 ? void 0 : userData.location,\n                        referral_level: referrer ? (referrer.referral_level || 0) + 1 : 0,\n                        referred_by_id: (referrer === null || referrer === void 0 ? void 0 : referrer.id) || null\n                    }\n                })\n            });\n            if (!response.ok) {\n                const errorData = await response.json();\n                throw new Error(errorData.error || \"Failed to create user profile\");\n            }\n            // If user has a referrer, place them in hierarchy\n            if (referrer) {\n                try {\n                    await _services_referralSystem__WEBPACK_IMPORTED_MODULE_2__.ReferralSystemService.placeUserInHierarchy(authUser.id, referrer.id);\n                } catch (referralError) {\n                    console.error(\"Failed to place user in referral hierarchy:\", referralError);\n                // Don't fail the profile creation, but log the error\n                }\n            }\n        } catch (error) {\n            console.error(\"Error creating user profile via API:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Verify email OTP and process referral placement\n   */ static async verifyEmailOtp(email, token) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.verifyOtp({\n            email,\n            token,\n            type: \"signup\"\n        });\n        if (error) {\n            throw new Error(error.message);\n        }\n        // After successful verification, create user profile if it doesn't exist\n        if (data.user) {\n            try {\n                // Check if user profile already exists\n                const { data: existingUser } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"users\").select(\"id\").eq(\"id\", data.user.id).single();\n                if (!existingUser) {\n                    var _data_user_user_metadata;\n                    // Get referrer if referral code exists in metadata\n                    let referrer = null;\n                    const referralCode = (_data_user_user_metadata = data.user.user_metadata) === null || _data_user_user_metadata === void 0 ? void 0 : _data_user_user_metadata.referral_code;\n                    if (referralCode) {\n                        console.log(\"\\uD83D\\uDD0D Found referral code in metadata during OTP verification:\", referralCode);\n                        try {\n                            referrer = await _services_referralSystem__WEBPACK_IMPORTED_MODULE_2__.ReferralSystemService.validateReferralCode(referralCode);\n                            console.log(\"✅ Referrer validated during OTP verification:\", referrer === null || referrer === void 0 ? void 0 : referrer.email);\n                        } catch (error) {\n                            console.warn(\"❌ Failed to validate referral code during OTP verification:\", error);\n                        }\n                    }\n                    // Create user profile with referral information\n                    if (referrer) {\n                        var _data_user_user_metadata1, _data_user_user_metadata2, _data_user_user_metadata3;\n                        await this.createUserProfileWithReferrer(data.user, {\n                            full_name: (_data_user_user_metadata1 = data.user.user_metadata) === null || _data_user_user_metadata1 === void 0 ? void 0 : _data_user_user_metadata1.full_name,\n                            phone: (_data_user_user_metadata2 = data.user.user_metadata) === null || _data_user_user_metadata2 === void 0 ? void 0 : _data_user_user_metadata2.phone,\n                            location: (_data_user_user_metadata3 = data.user.user_metadata) === null || _data_user_user_metadata3 === void 0 ? void 0 : _data_user_user_metadata3.location,\n                            referral_code: referralCode\n                        }, referrer);\n                    } else {\n                        var _data_user_user_metadata4, _data_user_user_metadata5, _data_user_user_metadata6;\n                        await this.createBasicUserProfile(data.user, {\n                            full_name: (_data_user_user_metadata4 = data.user.user_metadata) === null || _data_user_user_metadata4 === void 0 ? void 0 : _data_user_user_metadata4.full_name,\n                            phone: (_data_user_user_metadata5 = data.user.user_metadata) === null || _data_user_user_metadata5 === void 0 ? void 0 : _data_user_user_metadata5.phone,\n                            location: (_data_user_user_metadata6 = data.user.user_metadata) === null || _data_user_user_metadata6 === void 0 ? void 0 : _data_user_user_metadata6.location,\n                            referral_code: referralCode\n                        });\n                    }\n                }\n            } catch (profileError) {\n                console.error(\"Error creating user profile after verification:\", profileError);\n            // Don't fail the verification, but log the error\n            }\n        }\n        return data;\n    }\n    /**\n   * Resend email OTP\n   */ static async resendEmailOtp(email) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.resend({\n            type: \"signup\",\n            email\n        });\n        if (error) {\n            throw new Error(error.message);\n        }\n        return data;\n    }\n    /**\n   * Sign in with email and password\n   */ static async signIn(email, password) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.signInWithPassword({\n            email,\n            password\n        });\n        if (error) {\n            throw new Error(error.message);\n        }\n        return data;\n    }\n    /**\n   * Sign out the current user\n   */ static async signOut() {\n        const { error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.signOut();\n        if (error) {\n            throw new Error(error.message);\n        }\n    }\n    /**\n   * Get the current user session\n   */ static async getSession() {\n        const { data: { session }, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getSession();\n        if (error) {\n            throw new Error(error.message);\n        }\n        return session;\n    }\n    /**\n   * Get the current user with improved error handling\n   */ static async getCurrentUser() {\n        try {\n            // First check if we have a valid session\n            const { data: { session }, error: sessionError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getSession();\n            if (sessionError) {\n                console.error(\"Session error:\", sessionError);\n                return null;\n            }\n            if (!(session === null || session === void 0 ? void 0 : session.user)) {\n                console.log(\"No active session found\");\n                return null;\n            }\n            const user = session.user;\n            console.log(\"Found active session for user:\", user.email);\n            // Get additional user data from users table with retry logic\n            let retryCount = 0;\n            const maxRetries = 3;\n            while(retryCount < maxRetries){\n                try {\n                    const { data: profile, error: profileError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"users\").select(\"*\").eq(\"id\", user.id).single();\n                    if (profileError) {\n                        if (profileError.code === \"PGRST116\") {\n                            var _user_user_metadata, _user_user_metadata1, _user_user_metadata2;\n                            // User profile doesn't exist, create it\n                            console.log(\"User profile not found, creating...\");\n                            const newProfile = {\n                                id: user.id,\n                                email: user.email,\n                                full_name: ((_user_user_metadata = user.user_metadata) === null || _user_user_metadata === void 0 ? void 0 : _user_user_metadata.full_name) || null,\n                                phone: ((_user_user_metadata1 = user.user_metadata) === null || _user_user_metadata1 === void 0 ? void 0 : _user_user_metadata1.phone) || null,\n                                location: ((_user_user_metadata2 = user.user_metadata) === null || _user_user_metadata2 === void 0 ? void 0 : _user_user_metadata2.location) || null,\n                                role: \"user\",\n                                is_super_admin: false\n                            };\n                            const { data: createdProfile, error: createError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"users\").insert(newProfile).select().single();\n                            if (createError) {\n                                console.error(\"Error creating user profile:\", createError);\n                                return {\n                                    id: user.id,\n                                    email: user.email\n                                };\n                            }\n                            return createdProfile;\n                        } else {\n                            throw profileError;\n                        }\n                    }\n                    return profile;\n                } catch (error) {\n                    retryCount++;\n                    console.error(\"Error fetching user profile (attempt \".concat(retryCount, \"):\"), error);\n                    if (retryCount >= maxRetries) {\n                        console.error(\"Max retries reached, returning basic user info\");\n                        return {\n                            id: user.id,\n                            email: user.email\n                        };\n                    }\n                    // Wait before retry\n                    await new Promise((resolve)=>setTimeout(resolve, 1000 * retryCount));\n                }\n            }\n            return null;\n        } catch (error) {\n            console.error(\"Error in getCurrentUser:\", error);\n            return null;\n        }\n    }\n    /**\n   * Update user profile\n   */ static async updateProfile(userId, updates) {\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"users\").update(updates).eq(\"id\", userId).select().single();\n        if (error) {\n            throw new Error(error.message);\n        }\n        return data;\n    }\n    /**\n   * Reset password\n   */ static async resetPassword(email) {\n        const { error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.resetPasswordForEmail(email, {\n            redirectTo: \"\".concat(window.location.origin, \"/auth/reset-password\")\n        });\n        if (error) {\n            throw new Error(error.message);\n        }\n    }\n    /**\n   * Update password\n   */ static async updatePassword(newPassword) {\n        const { error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.updateUser({\n            password: newPassword\n        });\n        if (error) {\n            throw new Error(error.message);\n        }\n    }\n    /**\n   * Listen to auth state changes with improved handling\n   */ static onAuthStateChange(callback) {\n        return _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.onAuthStateChange(async (event, session)=>{\n            var _session_user;\n            console.log(\"Auth state change detected:\", event, session === null || session === void 0 ? void 0 : (_session_user = session.user) === null || _session_user === void 0 ? void 0 : _session_user.email);\n            // Add a small delay to ensure state consistency\n            setTimeout(()=>{\n                callback(event, session);\n            }, 100);\n        });\n    }\n    /**\n   * Check if current session is valid\n   */ static async isSessionValid() {\n        try {\n            const { data: { session }, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getSession();\n            if (error || !session) {\n                return false;\n            }\n            // Check if token is expired\n            const now = Math.floor(Date.now() / 1000);\n            if (session.expires_at && session.expires_at < now) {\n                console.log(\"Session token expired\");\n                return false;\n            }\n            return true;\n        } catch (error) {\n            console.error(\"Error checking session validity:\", error);\n            return false;\n        }\n    }\n    /**\n   * Refresh session if needed\n   */ static async refreshSession() {\n        try {\n            const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.refreshSession();\n            if (error) {\n                console.error(\"Error refreshing session:\", error);\n                return false;\n            }\n            return !!data.session;\n        } catch (error) {\n            console.error(\"Error in refreshSession:\", error);\n            return false;\n        }\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/auth.ts\n"));

/***/ })

});