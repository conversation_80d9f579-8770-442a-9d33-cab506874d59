-- Favorites System Migration
-- Creates table for user favorites functionality

-- Create user_favorites table
CREATE TABLE user_favorites (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    ad_id UUID NOT NULL REFERENCES ads(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, ad_id)
);

-- Create indexes for better performance
CREATE INDEX idx_user_favorites_user_id ON user_favorites(user_id);
CREATE INDEX idx_user_favorites_ad_id ON user_favorites(ad_id);
CREATE INDEX idx_user_favorites_created_at ON user_favorites(created_at DESC);

-- Enable Row Level Security
ALTER TABLE user_favorites ENABLE ROW LEVEL SECURITY;

-- RLS Policies for user_favorites
-- Users can only see their own favorites
CREATE POLICY "Users can view their own favorites" ON user_favorites
    FOR SELECT USING (auth.uid() = user_id);

-- Users can add favorites
CREATE POLICY "Users can add favorites" ON user_favorites
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Users can remove their own favorites
CREATE POLICY "Users can remove their own favorites" ON user_favorites
    FOR DELETE USING (auth.uid() = user_id);

-- Create function to get user's favorite count
CREATE OR REPLACE FUNCTION get_user_favorites_count(user_uuid UUID)
RETURNS INTEGER AS $$
BEGIN
  RETURN (
    SELECT COUNT(*)::INTEGER
    FROM user_favorites
    WHERE user_id = user_uuid
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to check if ad is favorited by user
CREATE OR REPLACE FUNCTION is_ad_favorited(ad_uuid UUID, user_uuid UUID)
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1
    FROM user_favorites
    WHERE ad_id = ad_uuid AND user_id = user_uuid
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to toggle favorite status
CREATE OR REPLACE FUNCTION toggle_favorite(ad_uuid UUID, user_uuid UUID)
RETURNS BOOLEAN AS $$
DECLARE
  is_favorited BOOLEAN;
BEGIN
  -- Check if already favorited
  SELECT EXISTS (
    SELECT 1 FROM user_favorites 
    WHERE ad_id = ad_uuid AND user_id = user_uuid
  ) INTO is_favorited;
  
  IF is_favorited THEN
    -- Remove from favorites
    DELETE FROM user_favorites 
    WHERE ad_id = ad_uuid AND user_id = user_uuid;
    RETURN FALSE;
  ELSE
    -- Add to favorites
    INSERT INTO user_favorites (ad_id, user_id) 
    VALUES (ad_uuid, user_uuid);
    RETURN TRUE;
  END IF;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
