-- Fix chat system constraints and ensure proper data integrity

-- First, clean up any invalid chat conversations that might exist
DELETE FROM chat_conversations
WHERE seller_id IS NULL OR buyer_id IS NULL OR ad_id IS NULL;

-- Delete any orphaned chat messages
DELETE FROM chat_messages
WHERE conversation_id NOT IN (SELECT id FROM chat_conversations);

-- Drop existing constraint if it exists
ALTER TABLE chat_conversations
DROP CONSTRAINT IF EXISTS check_different_users;

-- Ensure all foreign key constraints are properly set
ALTER TABLE chat_conversations
DROP CONSTRAINT IF EXISTS chat_conversations_ad_id_fkey,
DROP CONSTRAINT IF EXISTS chat_conversations_buyer_id_fkey,
DROP CONSTRAINT IF EXISTS chat_conversations_seller_id_fkey;

-- Re-add foreign key constraints with proper error handling
ALTER TABLE chat_conversations
ADD CONSTRAINT chat_conversations_ad_id_fkey
    FOREIGN KEY (ad_id) REFERENCES ads(id) ON DELETE CASCADE,
ADD CONSTRAINT chat_conversations_buyer_id_fkey
    FOREIGN KEY (buyer_id) REFERENCES users(id) ON DELETE CASCADE,
ADD CONSTRAINT chat_conversations_seller_id_fkey
    FOREIGN KEY (seller_id) REFERENCES users(id) ON DELETE CASCADE;

-- Ensure NOT NULL constraints are in place
ALTER TABLE chat_conversations
ALTER COLUMN ad_id SET NOT NULL,
ALTER COLUMN buyer_id SET NOT NULL,
ALTER COLUMN seller_id SET NOT NULL;

-- Add constraint to prevent users from chatting with themselves (only if no existing data violates it)
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM chat_conversations WHERE buyer_id = seller_id
    ) THEN
        ALTER TABLE chat_conversations
        ADD CONSTRAINT check_different_users
        CHECK (buyer_id != seller_id);
    END IF;
END $$;

-- Update the trigger function to handle edge cases
CREATE OR REPLACE FUNCTION update_conversation_on_message()
RETURNS TRIGGER AS $$
BEGIN
    -- Update last_message_at and unread counts
    UPDATE chat_conversations 
    SET 
        last_message_at = NEW.created_at,
        updated_at = NEW.created_at,
        buyer_unread_count = CASE 
            WHEN NEW.sender_id != buyer_id THEN buyer_unread_count + 1 
            ELSE buyer_unread_count 
        END,
        seller_unread_count = CASE 
            WHEN NEW.sender_id != seller_id THEN seller_unread_count + 1 
            ELSE seller_unread_count 
        END
    WHERE id = NEW.conversation_id;
    
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Recreate the trigger
DROP TRIGGER IF EXISTS update_conversation_on_new_message ON chat_messages;
CREATE TRIGGER update_conversation_on_new_message 
    AFTER INSERT ON chat_messages
    FOR EACH ROW EXECUTE FUNCTION update_conversation_on_message();

-- Add function to mark messages as read
CREATE OR REPLACE FUNCTION mark_messages_as_read(conversation_uuid uuid, user_uuid uuid)
RETURNS void AS $$
BEGIN
    -- Mark messages as read
    UPDATE chat_messages 
    SET is_read = true 
    WHERE conversation_id = conversation_uuid 
    AND sender_id != user_uuid 
    AND is_read = false;
    
    -- Reset unread count for the user
    UPDATE chat_conversations 
    SET 
        buyer_unread_count = CASE 
            WHEN buyer_id = user_uuid THEN 0 
            ELSE buyer_unread_count 
        END,
        seller_unread_count = CASE 
            WHEN seller_id = user_uuid THEN 0 
            ELSE seller_unread_count 
        END
    WHERE id = conversation_uuid;
END;
$$ language 'plpgsql';

-- Grant execute permissions to authenticated users
GRANT EXECUTE ON FUNCTION mark_messages_as_read(uuid, uuid) TO authenticated;
