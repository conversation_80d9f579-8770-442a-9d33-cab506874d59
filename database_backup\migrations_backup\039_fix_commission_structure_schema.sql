-- Fix Commission Structure Schema
-- This migration updates the existing commission_structure table to support the extended commission types

-- First, let's add the new columns to the existing commission_structure table
ALTER TABLE commission_structure 
ADD COLUMN IF NOT EXISTS direct_commission_rate DECIMAL(5,4) DEFAULT 0,
ADD COLUMN IF NOT EXISTS level_commission_rate DECIMAL(5,4) DEFAULT 0,
ADD COLUMN IF NOT EXISTS voucher_rate DECIMAL(5,4) DEFAULT 0,
ADD COLUMN IF NOT EXISTS festival_bonus_rate DECIMAL(5,4) DEFAULT 0,
ADD COLUMN IF NOT EXISTS saving_rate DECIMAL(5,4) DEFAULT 0,
ADD COLUMN IF NOT EXISTS gift_center_rate DECIMAL(5,4) DEFAULT 0,
ADD COLUMN IF NOT EXISTS entertainment_rate DECIMAL(5,4) DEFAULT 0,
ADD COLUMN IF NOT EXISTS medical_rate DECIMAL(5,4) DEFAULT 0,
ADD COLUMN IF NOT EXISTS education_rate DECIMAL(5,4) DEFAULT 0,
ADD COLUMN IF NOT EXISTS credit_rate DECIMAL(5,4) DEFAULT 0,
ADD COLUMN IF NOT EXISTS zm_bonus_rate DECIMAL(5,4) DEFAULT 0,
ADD COLUMN IF NOT EXISTS zm_petral_allowance_rate DECIMAL(5,4) DEFAULT 0,
ADD COLUMN IF NOT EXISTS zm_leasing_facility_rate DECIMAL(5,4) DEFAULT 0,
ADD COLUMN IF NOT EXISTS zm_phone_bill_rate DECIMAL(5,4) DEFAULT 0,
ADD COLUMN IF NOT EXISTS rsm_bonus_rate DECIMAL(5,4) DEFAULT 0,
ADD COLUMN IF NOT EXISTS rsm_petral_allowance_rate DECIMAL(5,4) DEFAULT 0,
ADD COLUMN IF NOT EXISTS rsm_leasing_facility_rate DECIMAL(5,4) DEFAULT 0,
ADD COLUMN IF NOT EXISTS rsm_phone_bill_rate DECIMAL(5,4) DEFAULT 0,
ADD COLUMN IF NOT EXISTS zm_present_leader_rate DECIMAL(5,4) DEFAULT 0,
ADD COLUMN IF NOT EXISTS rsm_present_leader_rate DECIMAL(5,4) DEFAULT 0,
ADD COLUMN IF NOT EXISTS annual_present_leader_rate DECIMAL(5,4) DEFAULT 0,
ADD COLUMN IF NOT EXISTS okdoi_head_rate DECIMAL(5,4) DEFAULT 0;

-- Now let's migrate the existing data to use the new column structure
-- For existing records, we'll map level_1_rate to the appropriate commission type rate
UPDATE commission_structure 
SET 
    direct_commission_rate = CASE 
        WHEN commission_type = 'direct_commission' THEN level_1_rate 
        ELSE 0 
    END,
    level_commission_rate = CASE 
        WHEN commission_type = 'level_commission' THEN level_1_rate 
        ELSE 0 
    END,
    voucher_rate = CASE 
        WHEN commission_type = 'voucher' THEN level_1_rate 
        ELSE 0 
    END,
    festival_bonus_rate = CASE 
        WHEN commission_type = 'festival_bonus' THEN level_1_rate 
        ELSE 0 
    END,
    saving_rate = CASE 
        WHEN commission_type = 'saving' THEN level_1_rate 
        ELSE 0 
    END,
    gift_center_rate = CASE 
        WHEN commission_type = 'gift_center' THEN level_1_rate 
        ELSE 0 
    END,
    entertainment_rate = CASE 
        WHEN commission_type = 'entertainment' THEN level_1_rate 
        ELSE 0 
    END,
    medical_rate = CASE 
        WHEN commission_type = 'medical' THEN level_1_rate 
        ELSE 0 
    END,
    education_rate = CASE 
        WHEN commission_type = 'education' THEN level_1_rate 
        ELSE 0 
    END,
    credit_rate = CASE 
        WHEN commission_type = 'credit' THEN level_1_rate 
        ELSE 0 
    END,
    zm_bonus_rate = CASE 
        WHEN commission_type = 'zm_bonus' THEN level_1_rate 
        ELSE 0 
    END,
    zm_petral_allowance_rate = CASE 
        WHEN commission_type = 'zm_petral_allowance' THEN level_1_rate 
        ELSE 0 
    END,
    zm_leasing_facility_rate = CASE 
        WHEN commission_type = 'zm_leasing_facility' THEN level_1_rate 
        ELSE 0 
    END,
    zm_phone_bill_rate = CASE 
        WHEN commission_type = 'zm_phone_bill' THEN level_1_rate 
        ELSE 0 
    END,
    rsm_bonus_rate = CASE 
        WHEN commission_type = 'rsm_bonus' THEN level_1_rate 
        ELSE 0 
    END,
    rsm_petral_allowance_rate = CASE 
        WHEN commission_type = 'rsm_petral_allowance' THEN level_1_rate 
        ELSE 0 
    END,
    rsm_leasing_facility_rate = CASE 
        WHEN commission_type = 'rsm_leasing_facility' THEN level_1_rate 
        ELSE 0 
    END,
    rsm_phone_bill_rate = CASE 
        WHEN commission_type = 'rsm_phone_bill' THEN level_1_rate 
        ELSE 0 
    END,
    zm_present_leader_rate = CASE 
        WHEN commission_type = 'zm_present_leader' THEN level_1_rate 
        ELSE 0 
    END,
    rsm_present_leader_rate = CASE 
        WHEN commission_type = 'rsm_present_leader' THEN level_1_rate 
        ELSE 0 
    END,
    annual_present_leader_rate = CASE 
        WHEN commission_type = 'annual_present_leader' THEN level_1_rate 
        ELSE 0 
    END,
    okdoi_head_rate = CASE 
        WHEN commission_type = 'okdoi_head' THEN level_1_rate 
        ELSE 0 
    END
WHERE level_1_rate IS NOT NULL;

-- Add indexes for the new columns
CREATE INDEX IF NOT EXISTS idx_commission_structure_direct_rate ON commission_structure(direct_commission_rate);
CREATE INDEX IF NOT EXISTS idx_commission_structure_level_rate ON commission_structure(level_commission_rate);
CREATE INDEX IF NOT EXISTS idx_commission_structure_voucher_rate ON commission_structure(voucher_rate);

-- Add comments to document the new schema
COMMENT ON COLUMN commission_structure.direct_commission_rate IS 'Direct commission rate for this package value';
COMMENT ON COLUMN commission_structure.level_commission_rate IS 'Level commission rate for this package value';
COMMENT ON COLUMN commission_structure.voucher_rate IS 'Voucher commission rate for this package value';
COMMENT ON COLUMN commission_structure.festival_bonus_rate IS 'Festival bonus rate for this package value';
COMMENT ON COLUMN commission_structure.saving_rate IS 'Saving commission rate for this package value';
COMMENT ON COLUMN commission_structure.gift_center_rate IS 'Gift center commission rate for this package value';
COMMENT ON COLUMN commission_structure.entertainment_rate IS 'Entertainment commission rate for this package value';
COMMENT ON COLUMN commission_structure.medical_rate IS 'Medical commission rate for this package value';
COMMENT ON COLUMN commission_structure.education_rate IS 'Education commission rate for this package value';
COMMENT ON COLUMN commission_structure.credit_rate IS 'Credit commission rate for this package value';
COMMENT ON COLUMN commission_structure.zm_bonus_rate IS 'Zonal Manager bonus rate for this package value';
COMMENT ON COLUMN commission_structure.zm_petral_allowance_rate IS 'ZM petral allowance rate for this package value';
COMMENT ON COLUMN commission_structure.zm_leasing_facility_rate IS 'ZM leasing facility rate for this package value';
COMMENT ON COLUMN commission_structure.zm_phone_bill_rate IS 'ZM phone bill rate for this package value';
COMMENT ON COLUMN commission_structure.rsm_bonus_rate IS 'Regional Sales Manager bonus rate for this package value';
COMMENT ON COLUMN commission_structure.rsm_petral_allowance_rate IS 'RSM petral allowance rate for this package value';
COMMENT ON COLUMN commission_structure.rsm_leasing_facility_rate IS 'RSM leasing facility rate for this package value';
COMMENT ON COLUMN commission_structure.rsm_phone_bill_rate IS 'RSM phone bill rate for this package value';
COMMENT ON COLUMN commission_structure.zm_present_leader_rate IS 'ZM present leader rate for this package value';
COMMENT ON COLUMN commission_structure.rsm_present_leader_rate IS 'RSM present leader rate for this package value';
COMMENT ON COLUMN commission_structure.annual_present_leader_rate IS 'Annual present leader rate for this package value';
COMMENT ON COLUMN commission_structure.okdoi_head_rate IS 'OKDOI head commission rate for this package value';

-- Update the updated_at timestamp
UPDATE commission_structure SET updated_at = NOW() WHERE id IS NOT NULL;
