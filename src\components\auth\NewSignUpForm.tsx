'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import Image from 'next/image'
import { Eye, EyeOff, User, Mail, Phone, MapPin, Lock, Plane } from 'lucide-react'
import { useAuth } from '@/contexts/AuthContext'
import { ReferralSystemService } from '@/lib/services/referralSystem'
import type { User } from '@/types'

interface NewSignUpFormProps {
  referralCode?: string
}

export default function NewSignUpForm({ referralCode }: NewSignUpFormProps) {
  const [formData, setFormData] = useState({
    email: '',
    password: '',
    confirmPassword: '',
    fullName: '',
    phone: '',
    location: '',
    referralCode: referralCode || '' // Keep for backend compatibility but hide from UI
  })
  const [showPassword, setShowPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)
  const [error, setError] = useState('')
  const [loading, setLoading] = useState(false)
  const [acceptTerms, setAcceptTerms] = useState(false)
  const [success, setSuccess] = useState('')
  const [referrer, setReferrer] = useState<User | null>(null)
  const [referrerLoading, setReferrerLoading] = useState(false)
  
  const { signUp } = useAuth()
  const router = useRouter()

  useEffect(() => {
    const fetchReferrer = async () => {
      if (referralCode) {
        setReferrerLoading(true)
        try {
          const referrerData = await ReferralSystemService.validateReferralCode(referralCode)
          setReferrer(referrerData)
        } catch (error) {
          console.warn('Could not fetch referrer information:', error)
        } finally {
          setReferrerLoading(false)
        }
      }
    }
    fetchReferrer()
  }, [referralCode])

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData(prev => ({
      ...prev,
      [e.target.name]: e.target.value
    }))
  }

  const validateForm = () => {
    if (!formData.fullName.trim()) {
      setError('Full name is required')
      return false
    }
    if (!formData.email) {
      setError('Email is required')
      return false
    }
    if (!/\S+@\S+\.\S+/.test(formData.email)) {
      setError('Please enter a valid email address')
      return false
    }
    if (!formData.password) {
      setError('Password is required')
      return false
    }
    if (formData.password.length < 6) {
      setError('Password must be at least 6 characters long')
      return false
    }
    if (formData.password !== formData.confirmPassword) {
      setError('Passwords do not match')
      return false
    }
    if (!acceptTerms) {
      setError('Please accept the Terms of Service and Privacy Policy')
      return false
    }
    return true
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setError('')
    setSuccess('')

    if (!validateForm()) return

    setLoading(true)

    try {
      const result = await signUp(formData.email, formData.password, {
        full_name: formData.fullName,
        phone: formData.phone,
        location: formData.location,
        referral_code: formData.referralCode
      })

      if (result.requireEmailVerification) {
        // Show success message first, then redirect
        setSuccess('Account created successfully! Please check your email for verification code.')

        // Delay redirect to prevent jarring UX
        setTimeout(() => {
          router.push(`/auth/verify-otp?email=${encodeURIComponent(formData.email)}`)
        }, 2000)
      } else {
        setSuccess('Account created successfully! Welcome to OKDOI!')
        setTimeout(() => {
          router.push('/')
        }, 1500)
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred during registration')
      setLoading(false) // Only set loading to false on error
    }
  }

  return (
    <div className="min-h-screen flex">
      {/* Left Side - Hero Image */}
      <div className="hidden lg:flex lg:w-1/2 relative bg-gradient-to-br from-blue-400 via-blue-500 to-blue-600">
        <div className="absolute inset-0">
          <Image
            src="/images/hero/hero-bg-2.webp"
            alt="OKDOI Marketplace"
            fill
            className="object-cover opacity-80"
            priority
          />
        </div>
        
        {/* Overlay Content */}
        <div className="relative z-10 flex flex-col justify-center items-center text-white p-12">
          <div className="text-center max-w-md">
            <h1 className="text-4xl font-bold mb-4 leading-tight">
              Join OKDOI
            </h1>
            <p className="text-lg opacity-90 mb-8">
              Create your premium marketplace account and start your journey with us
            </p>
            
            {/* Decorative Elements */}
            <div className="flex justify-center space-x-4 mb-8">
              <div className="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center backdrop-blur-sm">
                <User className="w-6 h-6" />
              </div>
              <div className="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center backdrop-blur-sm">
                <Mail className="w-6 h-6" />
              </div>
              <div className="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center backdrop-blur-sm">
                <Lock className="w-6 h-6" />
              </div>
            </div>
          </div>
        </div>

        {/* Bottom Decorative Elements - Sri Lankan Landmarks */}
        <div className="absolute bottom-0 left-0 right-0">
          {/* Buildings Silhouette */}
          <div className="flex items-end justify-center space-x-2 mb-4">
            <div className="w-6 h-14 bg-white/25 rounded-t-lg"></div>
            <div className="w-8 h-18 bg-white/20 rounded-t-lg"></div>
            <div className="w-12 h-24 bg-white/25 rounded-t-lg relative">
              <div className="absolute top-0 left-1/2 transform -translate-x-1/2 w-2 h-6 bg-white/40 rounded-full"></div>
            </div>
            <div className="w-10 h-20 bg-white/30 rounded-t-lg"></div>
            <div className="w-6 h-12 bg-white/25 rounded-t-lg"></div>
            <div className="w-8 h-16 bg-white/20 rounded-t-lg"></div>
          </div>
          <svg viewBox="0 0 1200 120" className="w-full h-20 text-blue-300/30">
            <path d="M0,60 C300,120 900,0 1200,60 L1200,120 L0,120 Z" fill="currentColor" />
          </svg>
        </div>
      </div>

      {/* Right Side - Registration Form */}
      <div className="w-full lg:w-1/2 flex items-center justify-center p-8 bg-gray-50">
        <div className="w-full max-w-md">
          {/* Welcome Section */}
          <div className="text-center mb-8">
            <div className="flex justify-center mb-4">
              <Plane className="w-8 h-8 text-blue-500 transform rotate-45" />
            </div>
            <h2 className="text-3xl font-bold text-gray-900 mb-2">Create Account</h2>
            <p className="text-gray-600">Join OKDOI Marketplace</p>
          </div>

          {/* Success Message */}
          {success && (
            <div className="mb-6 p-4 bg-green-50 border border-green-200 rounded-lg">
              <p className="text-green-600 text-sm">{success}</p>
            </div>
          )}

          {/* Error Message */}
          {error && (
            <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
              <p className="text-red-600 text-sm">{error}</p>
            </div>
          )}

          {/* Referrer Information */}
          {referralCode && (
            <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
              {referrerLoading ? (
                <div className="flex items-center space-x-2">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                  <p className="text-blue-600 text-sm">Loading referrer information...</p>
                </div>
              ) : referrer ? (
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                    <User className="w-5 h-5 text-blue-600" />
                  </div>
                  <div>
                    <p className="text-blue-800 font-medium text-sm">
                      You are invited by {referrer.full_name || referrer.email}
                    </p>
                    <p className="text-blue-600 text-xs">
                      Join the OKDOI community through this referral
                    </p>
                  </div>
                </div>
              ) : (
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                    <User className="w-5 h-5 text-blue-600" />
                  </div>
                  <div>
                    <p className="text-blue-800 font-medium text-sm">
                      You are invited to join OKDOI
                    </p>
                    <p className="text-blue-600 text-xs">
                      Join the OKDOI community through this referral
                    </p>
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Registration Form */}
          <form onSubmit={handleSubmit} className="space-y-4">
            {/* Full Name Field */}
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                <User className="h-5 w-5 text-gray-400" />
              </div>
              <input
                type="text"
                name="fullName"
                value={formData.fullName}
                onChange={handleChange}
                placeholder="Enter your full name"
                required
                disabled={loading}
                className="w-full pl-12 pr-4 py-3 border-2 border-blue-200 rounded-lg focus:border-blue-500 focus:outline-none transition-colors bg-white text-gray-900 placeholder-gray-400"
              />
              <label className="absolute -top-2 left-3 bg-white px-2 text-xs font-medium text-blue-600">
                Full Name
              </label>
            </div>

            {/* Email Field */}
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                <Mail className="h-5 w-5 text-gray-400" />
              </div>
              <input
                type="email"
                name="email"
                value={formData.email}
                onChange={handleChange}
                placeholder="Enter your email"
                required
                disabled={loading}
                className="w-full pl-12 pr-4 py-3 border-2 border-blue-200 rounded-lg focus:border-blue-500 focus:outline-none transition-colors bg-white text-gray-900 placeholder-gray-400"
              />
              <label className="absolute -top-2 left-3 bg-white px-2 text-xs font-medium text-blue-600">
                Email Address
              </label>
            </div>

            {/* Phone and Location Row */}
            <div className="grid grid-cols-2 gap-4">
              {/* Phone Field */}
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Phone className="h-4 w-4 text-gray-400" />
                </div>
                <input
                  type="tel"
                  name="phone"
                  value={formData.phone}
                  onChange={handleChange}
                  placeholder="Phone"
                  disabled={loading}
                  className="w-full pl-10 pr-3 py-3 border-2 border-blue-200 rounded-lg focus:border-blue-500 focus:outline-none transition-colors bg-white text-gray-900 placeholder-gray-400"
                />
                <label className="absolute -top-2 left-3 bg-white px-2 text-xs font-medium text-blue-600">
                  Phone (Optional)
                </label>
              </div>

              {/* Location Field */}
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <MapPin className="h-4 w-4 text-gray-400" />
                </div>
                <input
                  type="text"
                  name="location"
                  value={formData.location}
                  onChange={handleChange}
                  placeholder="Location"
                  disabled={loading}
                  className="w-full pl-10 pr-3 py-3 border-2 border-blue-200 rounded-lg focus:border-blue-500 focus:outline-none transition-colors bg-white text-gray-900 placeholder-gray-400"
                />
                <label className="absolute -top-2 left-3 bg-white px-2 text-xs font-medium text-blue-600">
                  Location (Optional)
                </label>
              </div>
            </div>

            {/* Password Field */}
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                <Lock className="h-5 w-5 text-gray-400" />
              </div>
              <input
                type={showPassword ? 'text' : 'password'}
                name="password"
                value={formData.password}
                onChange={handleChange}
                placeholder="Create a password"
                required
                disabled={loading}
                className="w-full pl-12 pr-12 py-3 border-2 border-blue-200 rounded-lg focus:border-blue-500 focus:outline-none transition-colors bg-white text-gray-900 placeholder-gray-400"
              />
              <label className="absolute -top-2 left-3 bg-white px-2 text-xs font-medium text-blue-600">
                Password
              </label>
              <button
                type="button"
                onClick={() => setShowPassword(!showPassword)}
                disabled={loading}
                className="absolute inset-y-0 right-0 pr-4 flex items-center text-gray-400 hover:text-gray-600 transition-colors"
              >
                {showPassword ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
              </button>
            </div>
            <p className="text-xs text-gray-500 mt-1">Must be at least 6 characters long</p>

            {/* Confirm Password Field */}
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                <Lock className="h-5 w-5 text-gray-400" />
              </div>
              <input
                type={showConfirmPassword ? 'text' : 'password'}
                name="confirmPassword"
                value={formData.confirmPassword}
                onChange={handleChange}
                placeholder="Confirm your password"
                required
                disabled={loading}
                className="w-full pl-12 pr-12 py-3 border-2 border-blue-200 rounded-lg focus:border-blue-500 focus:outline-none transition-colors bg-white text-gray-900 placeholder-gray-400"
              />
              <label className="absolute -top-2 left-3 bg-white px-2 text-xs font-medium text-blue-600">
                Confirm Password
              </label>
              <button
                type="button"
                onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                disabled={loading}
                className="absolute inset-y-0 right-0 pr-4 flex items-center text-gray-400 hover:text-gray-600 transition-colors"
              >
                {showConfirmPassword ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
              </button>
            </div>

            {/* Terms and Conditions */}
            <div className="flex items-start space-x-3 pt-2">
              <input
                type="checkbox"
                id="acceptTerms"
                checked={acceptTerms}
                onChange={(e) => setAcceptTerms(e.target.checked)}
                disabled={loading}
                className="mt-1 rounded border-gray-300 text-blue-600 focus:ring-blue-500 focus:ring-offset-0 transition-colors"
              />
              <label htmlFor="acceptTerms" className="text-sm text-gray-600 leading-relaxed">
                I agree to the{' '}
                <Link href="/terms" className="text-blue-600 hover:text-blue-700 font-medium">
                  Terms of Service
                </Link>
                {' '}and{' '}
                <Link href="/privacy" className="text-blue-600 hover:text-blue-700 font-medium">
                  Privacy Policy
                </Link>
              </label>
            </div>

            {/* Create Account Button */}
            <button
              type="submit"
              disabled={loading}
              className="w-full bg-blue-500 hover:bg-blue-600 disabled:bg-blue-300 text-white font-semibold py-4 rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 mt-6"
            >
              {loading ? 'Creating Account...' : 'Create Account'}
            </button>
          </form>

          {/* Sign In Link */}
          <div className="text-center mt-8">
            <p className="text-gray-600">
              Already have an account?{' '}
              <Link
                href="/auth/signin"
                className="text-blue-600 hover:text-blue-700 font-semibold transition-colors"
              >
                Sign In
              </Link>
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}
