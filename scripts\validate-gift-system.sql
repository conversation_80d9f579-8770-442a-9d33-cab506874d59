-- Gift Tasks & Present System Validation Script
-- Run this script to validate the implementation

-- 1. Verify all tables exist
SELECT
    table_name,
    table_type
FROM information_schema.tables
WHERE table_schema = 'public'
AND table_name IN (
    'user_tasks',
    'user_task_assignments',
    'present_allocations',
    'gift_transactions',
    'present_pools',
    'gift_system_audit_log'
)
ORDER BY table_name;

-- 2. Verify present pools are initialized
SELECT * FROM present_pools ORDER BY pool_type;

-- 3. Verify commission structure has present rates
SELECT
    package_value,
    present_leader_rate,
    annual_present_user_rate,
    annual_present_leader_rate
FROM commission_structure
WHERE is_active = TRUE
ORDER BY package_value;

-- 4. Test present pool summary function
SELECT * FROM get_present_pool_summary();

-- 5. Verify Row Level Security policies exist
SELECT
    schemaname,
    tablename,
    policyname,
    permissive,
    roles,
    cmd,
    qual
FROM pg_policies
WHERE tablename IN (
    'user_tasks',
    'user_task_assignments',
    'present_allocations',
    'gift_transactions',
    'present_pools'
)
ORDER BY tablename, policyname;

-- 6. Verify audit triggers exist
SELECT
    trigger_name,
    event_object_table,
    action_timing,
    event_manipulation
FROM information_schema.triggers
WHERE trigger_name LIKE 'audit_%'
ORDER BY event_object_table, trigger_name;

-- 7. Verify indexes exist for performance
SELECT
    indexname,
    tablename,
    indexdef
FROM pg_indexes
WHERE tablename IN (
    'user_tasks',
    'user_task_assignments',
    'present_allocations',
    'gift_transactions',
    'gift_system_audit_log'
)
ORDER BY tablename, indexname;

-- 8. Check table constraints
SELECT
    tc.table_name,
    tc.constraint_name,
    tc.constraint_type,
    cc.check_clause
FROM information_schema.table_constraints tc
LEFT JOIN information_schema.check_constraints cc
    ON tc.constraint_name = cc.constraint_name
WHERE tc.table_name IN (
    'user_tasks',
    'user_task_assignments',
    'present_allocations',
    'gift_transactions',
    'present_pools'
)
AND tc.constraint_type IN ('CHECK', 'UNIQUE', 'PRIMARY KEY', 'FOREIGN KEY')
ORDER BY tc.table_name, tc.constraint_type, tc.constraint_name;

-- 9. Verify function permissions
SELECT
    routine_name,
    routine_type,
    security_type,
    is_deterministic
FROM information_schema.routines
WHERE routine_name IN (
    'calculate_present_allocations',
    'get_present_pool_summary',
    'get_user_present_balance',
    'verify_task_completion',
    'log_gift_system_audit',
    'get_gift_system_audit_trail'
)
ORDER BY routine_name;

-- 10. Security validation - ensure present data is protected
-- This should return no rows for non-admin users
SELECT
    'present_allocations' as table_name,
    COUNT(*) as accessible_rows
FROM present_allocations;

SELECT
    'present_pools' as table_name,
    COUNT(*) as accessible_rows
FROM present_pools;

-- 11. Audit log validation
SELECT
    action,
    entity_type,
    COUNT(*) as event_count
FROM gift_system_audit_log
GROUP BY action, entity_type
ORDER BY action, entity_type;

COMMENT ON SCRIPT IS 'Gift Tasks & Present System Validation - Run to verify complete implementation';