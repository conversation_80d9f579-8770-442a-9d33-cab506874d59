'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import {
  Home,
  Car,
  Laptop,
  Smartphone,
  Sofa,
  Wrench,
  Building,
  Briefcase,
  Gamepad2,
  Heart,
  Shirt,
  GraduationCap,
  ShoppingCart,
  MoreHorizontal,
  ChevronRight
} from 'lucide-react'
import Header from '@/components/layout/Header'
import Footer from '@/components/layout/Footer'
import Card from '@/components/ui/card'
import { Category } from '@/types'
import { CategoryService } from '@/lib/services/categories'
import { useAsyncOperation } from '@/hooks/useErrorHandler'

// Icon mapping for categories
const categoryIcons: Record<string, any> = {
  'property': Home,
  'vehicles': Car,
  'electronics': Laptop,
  'mobiles': Smartphone,
  'home-garden': Sofa,
  'services': Wrench,
  'business-industry': Building,
  'jobs': Briefcase,
  'hobby-sport-kids': Gamepad2,
  'animals': Heart,
  'fashion-beauty': Shirt,
  'education': GraduationCap,
  'essentials': ShoppingCart,
  'other': MoreHorizontal,
}

export default function CategoriesPage() {
  const {
    loading,
    data: categories,
    error,
    isRetrying,
    retryCount,
    hasError,
    errorMessage,
    execute,
    retry,
    clearError
  } = useAsyncOperation<Category[]>({ maxRetries: 3, retryDelay: 1000 })

  useEffect(() => {
    execute(
      () => CategoryService.getAllCategories(),
      { component: 'CategoriesPage', operation: 'fetchCategories' }
    )
  }, []) // Remove execute dependency to prevent infinite loop

  const handleRetry = () => {
    retry(() => CategoryService.getAllCategories())
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <main className="py-8">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h1 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
                Browse Categories
              </h1>
              <p className="text-xl text-gray-600">
                Find exactly what you're looking for
              </p>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {Array.from({ length: 12 }).map((_, index) => (
                <Card key={index} className="animate-pulse">
                  <div className="p-6">
                    <div className="flex items-center mb-4">
                      <div className="w-12 h-12 bg-gray-300 rounded-lg mr-4"></div>
                      <div className="flex-1">
                        <div className="h-5 bg-gray-300 rounded w-3/4 mb-2"></div>
                        <div className="h-3 bg-gray-300 rounded w-1/2"></div>
                      </div>
                    </div>
                    <div className="space-y-2">
                      {Array.from({ length: 4 }).map((_, i) => (
                        <div key={i} className="h-3 bg-gray-300 rounded w-full"></div>
                      ))}
                    </div>
                  </div>
                </Card>
              ))}
            </div>
          </div>
        </main>
        <Footer />
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <main className="py-8">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center">
              <div className="mb-4">
                <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg className="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                  </svg>
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Failed to Load Categories</h3>
                <p className="text-red-600 mb-4">{error}</p>
                {retryCount > 0 && (
                  <p className="text-sm text-gray-500 mb-4">
                    Attempted {retryCount} time{retryCount > 1 ? 's' : ''}
                  </p>
                )}
                <button
                  onClick={handleRetry}
                  disabled={loading}
                  className="inline-flex items-center px-4 py-2 bg-primary-blue text-white rounded-lg hover:bg-primary-blue/90 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                >
                  {loading ? (
                    <>
                      <svg className="animate-spin -ml-1 mr-3 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      Retrying...
                    </>
                  ) : (
                    'Try Again'
                  )}
                </button>
              </div>
            </div>
          </div>
        </main>
        <Footer />
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      <main className="py-8 pt-28">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h1 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
              Browse Categories
            </h1>
            <p className="text-xl text-gray-600">
              Find exactly what you're looking for
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {categories && categories.length > 0 ? categories.map((category) => {
              const IconComponent = categoryIcons[category.slug] || MoreHorizontal
              
              return (
                <Card key={category.id} className="hover:shadow-lg transition-shadow duration-200">
                  <div className="p-6">
                    <div className="flex items-center mb-4">
                      <div className="w-12 h-12 bg-primary-blue/10 rounded-lg flex items-center justify-center mr-4">
                        <IconComponent className="h-6 w-6 text-primary-blue" />
                      </div>
                      <div className="flex-1">
                        <h3 className="text-lg font-semibold text-gray-900 mb-1">
                          {category.name}
                        </h3>
                        <p className="text-sm text-gray-500">
                          {category.adCount || 0} ads • {category.subcategories?.length || 0} subcategories
                        </p>
                      </div>
                    </div>

                    {/* Subcategories */}
                    <div className="space-y-2">
                      {category.subcategories?.slice(0, 6).map((subcategory) => (
                        <Link
                          key={subcategory.id}
                          href={`/category/${category.slug}?subcategory=${subcategory.slug}`}
                          className="flex items-center justify-between text-sm text-gray-600 hover:text-primary-blue transition-colors py-1"
                        >
                          <span>{subcategory.name}</span>
                          <ChevronRight className="h-3 w-3" />
                        </Link>
                      ))}
                      
                      {category.subcategories && category.subcategories.length > 6 && (
                        <Link
                          href={`/category/${category.slug}`}
                          className="flex items-center justify-between text-sm text-primary-blue hover:text-primary-blue/80 transition-colors py-1 font-medium"
                        >
                          <span>View all {category.subcategories.length} subcategories</span>
                          <ChevronRight className="h-3 w-3" />
                        </Link>
                      )}
                    </div>

                    {/* View Category Button */}
                    <div className="mt-4 pt-4 border-t border-gray-100">
                      <Link
                        href={`/category/${category.slug}`}
                        className="text-primary-blue hover:text-primary-blue/80 font-medium text-sm flex items-center"
                      >
                        Browse {category.name}
                        <ChevronRight className="h-4 w-4 ml-1" />
                      </Link>
                    </div>
                  </div>
                </Card>
              )
            }) : (
              <div className="col-span-full text-center py-12">
                <div className="text-gray-500 mb-4">No categories found</div>
                <p className="text-gray-400">Categories will appear here once they are added.</p>
              </div>
            )}
          </div>
        </div>
      </main>
      <Footer />
    </div>
  )
}
