-- Fix Referral Placement Algorithm
-- This migration ensures proper 3-position limit enforcement and correct spillover logic

-- =====================================================
-- 1. UPDATE PLACEMENT FUNCTION WITH BETTER LOGIC
-- =====================================================

-- Drop and recreate the find_next_placement_position function with improved logic
CREATE OR REPLACE FUNCTION find_next_placement_position(parent_user_id UUID)
RETURNS TABLE(placement_parent_id UUID, placement_position INTEGER) AS $$
DECLARE
    current_parent UUID := parent_user_id;
    pos INTEGER;
    queue UUID[];
    next_user UUID;
    queue_index INTEGER := 1;
BEGIN
    -- Initialize queue with the original parent
    queue := ARRAY[current_parent];

    -- Breadth-first search for available positions
    WHILE queue_index <= array_length(queue, 1) LOOP
        -- Get next user from queue
        next_user := queue[queue_index];
        queue_index := queue_index + 1;

        -- Check for available positions (1, 2, 3) in order
        FOR pos IN 1..3 LOOP
            IF NOT EXISTS (
                SELECT 1 FROM referral_placements
                WHERE parent_id = next_user AND position = pos
            ) THEN
                -- Found available position
                RETURN QUERY SELECT next_user, pos;
                RETURN;
            END IF;
        END LOOP;

        -- Add all children to queue for next level search (breadth-first)
        queue := queue || ARRAY(
            SELECT child_id FROM referral_placements
            WHERE parent_id = next_user
            ORDER BY position ASC
        );
    END LOOP;

    -- No position found (shouldn't happen in normal operation)
    RAISE EXCEPTION 'No available placement position found in the referral tree for parent %', parent_user_id;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- 2. UPDATE PLACEMENT FUNCTION TO USE NEW LOGIC
-- =====================================================

-- Update the place_user_in_hierarchy function to use the corrected placement logic
CREATE OR REPLACE FUNCTION place_user_in_hierarchy(
    new_user_id UUID,
    referrer_id UUID
) RETURNS VOID AS $$
DECLARE
    placement_info RECORD;
    referrer_level INTEGER;
    new_level INTEGER;
    ancestor_record RECORD;
    parent_path TEXT;
    actual_parent_level INTEGER;
BEGIN
    -- Get referrer's level
    SELECT referral_level INTO referrer_level FROM users WHERE id = referrer_id;
    
    -- Find placement position using breadth-first search
    SELECT * INTO placement_info FROM find_next_placement_position(referrer_id);
    
    IF placement_info.placement_parent_id IS NULL THEN
        RAISE EXCEPTION 'No placement position found for user %', new_user_id;
    END IF;
    
    -- Get actual parent's level to calculate new user's level
    SELECT referral_level INTO actual_parent_level FROM users WHERE id = placement_info.placement_parent_id;
    new_level := actual_parent_level + 1;
    
    -- Get parent's referral path (handle null values)
    SELECT COALESCE(referral_path, '') INTO parent_path FROM users WHERE id = placement_info.placement_parent_id;
    
    -- Update user's referral information
    UPDATE users SET
        referred_by_id = placement_info.placement_parent_id,
        referral_level = new_level,
        referral_path = CASE 
            WHEN parent_path = '' THEN new_user_id::TEXT
            ELSE parent_path || '/' || new_user_id::TEXT
        END
    WHERE id = new_user_id;
    
    -- Create placement record
    INSERT INTO referral_placements (parent_id, child_id, position, placement_type)
    VALUES (
        placement_info.placement_parent_id, 
        new_user_id, 
        placement_info.placement_position,
        CASE WHEN placement_info.placement_parent_id = referrer_id THEN 'direct' ELSE 'spillover' END
    );
    
    -- Create hierarchy records for all ancestors (only if parent path exists)
    IF parent_path IS NOT NULL AND parent_path != '' THEN
        FOR ancestor_record IN 
            SELECT id, referral_level FROM users 
            WHERE id = ANY(string_to_array(trim(both '/' from parent_path), '/')::UUID[])
        LOOP
            INSERT INTO referral_hierarchy (user_id, ancestor_id, level_difference)
            VALUES (new_user_id, ancestor_record.id, new_level - ancestor_record.referral_level)
            ON CONFLICT (user_id, ancestor_id) DO NOTHING;
        END LOOP;
    END IF;
    
    -- Always create hierarchy record for direct parent
    INSERT INTO referral_hierarchy (user_id, ancestor_id, level_difference)
    VALUES (new_user_id, placement_info.placement_parent_id, 1)
    ON CONFLICT (user_id, ancestor_id) DO NOTHING;
    
    -- Update referral counts for the original referrer (who gets credit for the referral)
    UPDATE users SET 
        direct_referrals_count = direct_referrals_count + 1
    WHERE id = referrer_id;
    
    -- Update total downline counts for all ancestors
    UPDATE users SET 
        total_downline_count = total_downline_count + 1
    WHERE id IN (
        SELECT ancestor_id FROM referral_hierarchy WHERE user_id = new_user_id
    );
    
    -- Also update the actual placement parent's downline count if different from referrer
    IF placement_info.placement_parent_id != referrer_id THEN
        UPDATE users SET 
            total_downline_count = total_downline_count + 1
        WHERE id = placement_info.placement_parent_id;
    END IF;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- 3. ADD FUNCTION TO VALIDATE REFERRAL TREE INTEGRITY
-- =====================================================

-- Function to validate that no user has more than 3 direct placements
CREATE OR REPLACE FUNCTION validate_referral_tree_integrity()
RETURNS TABLE(
    parent_id UUID,
    parent_email TEXT,
    placement_count BIGINT,
    issue_type TEXT
) AS $$
BEGIN
    -- Check for users with more than 3 direct placements
    RETURN QUERY
    SELECT
        rp.parent_id,
        u.email,
        COUNT(*) as placement_count,
        'excessive_placements'::TEXT as issue_type
    FROM referral_placements rp
    JOIN users u ON u.id = rp.parent_id
    GROUP BY rp.parent_id, u.email
    HAVING COUNT(*) > 3;

    -- Check for duplicate positions under same parent
    RETURN QUERY
    SELECT
        rp.parent_id,
        u.email,
        rp.position::BIGINT as placement_count,
        'duplicate_position'::TEXT as issue_type
    FROM referral_placements rp
    JOIN users u ON u.id = rp.parent_id
    WHERE (rp.parent_id, rp.position) IN (
        SELECT parent_id, position
        FROM referral_placements
        GROUP BY parent_id, position
        HAVING COUNT(*) > 1
    );
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- 4. ADD COMMENTS FOR DOCUMENTATION
-- =====================================================

COMMENT ON FUNCTION find_next_placement_position(UUID) IS
'Finds the next available placement position in the referral tree using breadth-first search. Ensures proper 3-position limit enforcement and spillover logic.';

COMMENT ON FUNCTION place_user_in_hierarchy(UUID, UUID) IS
'Places a new user in the referral hierarchy under the specified referrer. Uses breadth-first search to find the first available position, implementing proper spillover when direct positions are full.';

COMMENT ON FUNCTION validate_referral_tree_integrity() IS
'Validates the referral tree integrity by checking for users with more than 3 direct placements and duplicate positions.';

-- =====================================================
-- 5. CREATE INDEX FOR BETTER PERFORMANCE
-- =====================================================

-- Index for faster placement position lookups
CREATE INDEX IF NOT EXISTS idx_referral_placements_parent_position
ON referral_placements(parent_id, position);

-- Index for faster queue operations in breadth-first search
CREATE INDEX IF NOT EXISTS idx_referral_placements_parent_child
ON referral_placements(parent_id, child_id);
