-- Fix Merchant Wallet Transaction Categories
-- Adds missing categories that are used by the merchant wallet functions

-- Update the category constraint to include missing categories
ALTER TABLE merchant_wallet_transactions 
DROP CONSTRAINT IF EXISTS merchant_wallet_transactions_category_check;

ALTER TABLE merchant_wallet_transactions 
ADD CONSTRAINT merchant_wallet_transactions_category_check 
CHECK (category IN (
  'order_payment', 
  'transfer_to_main', 
  'commission_deduction', 
  'refund', 
  'adjustment',
  'pending_order',      -- Used when order is placed (escrow)
  'order_completion'    -- Used when order is delivered (release escrow)
));
