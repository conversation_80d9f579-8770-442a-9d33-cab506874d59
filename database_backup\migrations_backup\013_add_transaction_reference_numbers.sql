-- Add reference numbers to wallet transactions
-- This migration adds unique reference numbers to all wallet transactions

-- Add reference_number column to wallet_transactions table
ALTER TABLE wallet_transactions 
ADD COLUMN reference_number varchar(50) UNIQUE;

-- Create index for better performance on reference number lookups
CREATE INDEX idx_wallet_transactions_reference_number ON wallet_transactions(reference_number);

-- Add reference_number column to p2p_transfers table as well
ALTER TABLE p2p_transfers 
ADD COLUMN reference_number varchar(50) UNIQUE;

-- Create index for better performance on P2P transfer reference number lookups
CREATE INDEX idx_p2p_transfers_reference_number ON p2p_transfers(reference_number);

-- Add reference_number column to deposit_requests table
ALTER TABLE deposit_requests 
ADD COLUMN reference_number varchar(50) UNIQUE;

-- Create index for better performance on deposit request reference number lookups
CREATE INDEX idx_deposit_requests_reference_number ON deposit_requests(reference_number);

-- Add comments to document the new fields
COMMENT ON COLUMN wallet_transactions.reference_number IS 'Unique transaction reference number (format: TXN-YYYYMMDD-XXXXXXXX)';
COMMENT ON COLUMN p2p_transfers.reference_number IS 'Unique P2P transfer reference number (format: TXN-YYYYMMDD-XXXXXXXX)';
COMMENT ON COLUMN deposit_requests.reference_number IS 'Unique deposit request reference number (format: TXN-YYYYMMDD-XXXXXXXX)';

-- Update existing records with generated reference numbers
-- Note: In a real migration, you might want to generate these server-side
-- For now, we'll leave them NULL and generate them when new transactions are created
