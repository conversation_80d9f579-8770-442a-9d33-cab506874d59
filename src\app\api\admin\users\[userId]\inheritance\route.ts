import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  }
)

export async function GET(
  request: NextRequest,
  { params }: { params: { userId: string } }
) {
  try {
    const { userId } = params

    // Fetch inheritance data for the specified user using admin client
    const { data: inheritanceData, error: inheritanceError } = await supabaseAdmin
      .from('user_inheritance')
      .select('*')
      .eq('user_id', userId)
      .single()

    if (inheritanceError && inheritanceError.code !== 'PGRST116') { // PGRST116 = no rows returned
      console.error('Error fetching inheritance data:', inheritanceError)
      return NextResponse.json({ error: 'Failed to fetch inheritance data' }, { status: 500 })
    }

    // Return null if no inheritance data found (not an error)
    return NextResponse.json(inheritanceData || null)

  } catch (error) {
    console.error('Error in inheritance API:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
