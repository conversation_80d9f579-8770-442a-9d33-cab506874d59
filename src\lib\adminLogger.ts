'use client'

export interface AdminLogEntry {
  timestamp: string
  level: 'info' | 'warn' | 'error' | 'debug'
  category: 'auth' | 'session' | 'admin_check' | 'navigation' | 'error'
  message: string
  data?: any
  userId?: string
  userEmail?: string
}

class AdminLogger {
  private logs: AdminLogEntry[] = []
  private maxLogs = 100 // Keep last 100 logs
  private isEnabled = true

  constructor() {
    // Enable logging in development or when explicitly enabled
    this.isEnabled = process.env.NODE_ENV === 'development' || 
                     localStorage.getItem('admin_debug') === 'true'
  }

  private addLog(entry: AdminLogEntry) {
    if (!this.isEnabled) return

    this.logs.push(entry)
    
    // Keep only the last maxLogs entries
    if (this.logs.length > this.maxLogs) {
      this.logs = this.logs.slice(-this.maxLogs)
    }

    // Also log to console with appropriate level
    const consoleMessage = `[AdminPanel:${entry.category}] ${entry.message}`
    
    switch (entry.level) {
      case 'error':
        console.error(consoleMessage, entry.data)
        break
      case 'warn':
        console.warn(consoleMessage, entry.data)
        break
      case 'debug':
        console.debug(consoleMessage, entry.data)
        break
      default:
        console.log(consoleMessage, entry.data)
    }
  }

  info(category: AdminLogEntry['category'], message: string, data?: any, userId?: string, userEmail?: string) {
    this.addLog({
      timestamp: new Date().toISOString(),
      level: 'info',
      category,
      message,
      data,
      userId,
      userEmail
    })
  }

  warn(category: AdminLogEntry['category'], message: string, data?: any, userId?: string, userEmail?: string) {
    this.addLog({
      timestamp: new Date().toISOString(),
      level: 'warn',
      category,
      message,
      data,
      userId,
      userEmail
    })
  }

  error(category: AdminLogEntry['category'], message: string, data?: any, userId?: string, userEmail?: string) {
    this.addLog({
      timestamp: new Date().toISOString(),
      level: 'error',
      category,
      message,
      data,
      userId,
      userEmail
    })
  }

  debug(category: AdminLogEntry['category'], message: string, data?: any, userId?: string, userEmail?: string) {
    this.addLog({
      timestamp: new Date().toISOString(),
      level: 'debug',
      category,
      message,
      data,
      userId,
      userEmail
    })
  }

  // Get all logs
  getLogs(): AdminLogEntry[] {
    return [...this.logs]
  }

  // Get logs by category
  getLogsByCategory(category: AdminLogEntry['category']): AdminLogEntry[] {
    return this.logs.filter(log => log.category === category)
  }

  // Get logs by level
  getLogsByLevel(level: AdminLogEntry['level']): AdminLogEntry[] {
    return this.logs.filter(log => log.level === level)
  }

  // Clear all logs
  clearLogs() {
    this.logs = []
  }

  // Export logs as JSON for debugging
  exportLogs(): string {
    return JSON.stringify(this.logs, null, 2)
  }

  // Get summary of recent issues
  getIssueSummary(): {
    totalErrors: number
    totalWarnings: number
    recentErrors: AdminLogEntry[]
    commonIssues: { [key: string]: number }
  } {
    const errors = this.logs.filter(log => log.level === 'error')
    const warnings = this.logs.filter(log => log.level === 'warn')
    const recentErrors = errors.slice(-5) // Last 5 errors
    
    // Count common issues
    const commonIssues: { [key: string]: number } = {}
    errors.forEach(error => {
      const key = `${error.category}:${error.message.split(' ').slice(0, 5).join(' ')}`
      commonIssues[key] = (commonIssues[key] || 0) + 1
    })

    return {
      totalErrors: errors.length,
      totalWarnings: warnings.length,
      recentErrors,
      commonIssues
    }
  }

  // Enable/disable logging
  setEnabled(enabled: boolean) {
    this.isEnabled = enabled
    if (typeof window !== 'undefined') {
      if (enabled) {
        localStorage.setItem('admin_debug', 'true')
      } else {
        localStorage.removeItem('admin_debug')
      }
    }
  }

  // Check if logging is enabled
  isLoggingEnabled(): boolean {
    return this.isEnabled
  }
}

// Export singleton instance
export const adminLogger = new AdminLogger()

// Global error handler for admin panel
if (typeof window !== 'undefined') {
  window.addEventListener('error', (event) => {
    if (window.location.pathname.startsWith('/admin')) {
      adminLogger.error('error', 'Global error in admin panel', {
        message: event.error?.message,
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno,
        stack: event.error?.stack
      })
    }
  })

  window.addEventListener('unhandledrejection', (event) => {
    if (window.location.pathname.startsWith('/admin')) {
      adminLogger.error('error', 'Unhandled promise rejection in admin panel', {
        reason: event.reason,
        promise: event.promise
      })
    }
  })
}

// Utility function to enable debug mode from console
if (typeof window !== 'undefined') {
  (window as any).enableAdminDebug = () => {
    adminLogger.setEnabled(true)
    console.log('Admin debug logging enabled. Use adminLogger.getLogs() to view logs.')
  }
  
  (window as any).disableAdminDebug = () => {
    adminLogger.setEnabled(false)
    console.log('Admin debug logging disabled.')
  }
  
  (window as any).getAdminLogs = () => {
    return adminLogger.getLogs()
  }
  
  (window as any).getAdminIssueSummary = () => {
    return adminLogger.getIssueSummary()
  }
}
