import { NextRequest, NextResponse } from 'next/server'
import { AdminService } from '@/lib/services/admin'
import { supabaseAdmin } from '@/lib/supabase'

export async function GET(request: NextRequest) {
  try {
    // Check if user is admin
    const isAdmin = await AdminService.isAdminServerSide()
    if (!isAdmin) {
      return NextResponse.json({ 
        success: false, 
        error: 'Unauthorized access' 
      }, { status: 403 })
    }

    // Get all subscription packages
    const { data: packages, error } = await supabaseAdmin
      .from('subscription_packages')
      .select('id, name, price, currency, is_active')
      .eq('is_active', true)
      .order('price', { ascending: true })

    if (error) {
      console.error('Error fetching subscription packages:', error)
      return NextResponse.json({
        success: false,
        error: 'Failed to fetch subscription packages'
      }, { status: 500 })
    }

    return NextResponse.json({
      success: true,
      data: packages || []
    })

  } catch (error) {
    console.error('Error in subscription packages API:', error)
    return NextResponse.json({
      success: false,
      error: 'Internal server error'
    }, { status: 500 })
  }
}
