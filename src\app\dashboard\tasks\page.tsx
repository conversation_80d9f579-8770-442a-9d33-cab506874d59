'use client'

import { useState, useEffect } from 'react'
import DashboardLayout from '@/components/dashboard/DashboardLayout'
import But<PERSON> from '@/components/ui/Button'
import Card, { CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import Badge from '@/components/ui/badge'
import Progress from '@/components/ui/progress'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Target, Users, Gift, CheckCircle, Clock, AlertCircle, TrendingUp } from 'lucide-react'
import { toast } from 'sonner'

interface UserTaskAssignment {
  id: string
  user_id: string
  task_id: string
  status: 'assigned' | 'in_progress' | 'completed' | 'expired'
  progress: Record<string, any>
  assigned_at: string
  started_at?: string
  completed_at?: string
  reward_claimed_at?: string
  task: {
    id: string
    title: string
    description?: string
    task_type: 'sales_target' | 'referral_target' | 'custom'
    requirements: Record<string, any>
    reward_amount: number
    reward_type: 'normal_present' | 'annual_present' | 'bonus'
    expires_at?: string
  }
}

export default function UserTasksPage() {
  const [assignments, setAssignments] = useState<UserTaskAssignment[]>([])
  const [loading, setLoading] = useState(true)
  const [activeTab, setActiveTab] = useState('active')

  useEffect(() => {
    fetchUserTasks()
  }, [])

  const fetchUserTasks = async () => {
    try {
      const response = await fetch('/api/user/tasks')
      const data = await response.json()

      if (data.success) {
        setAssignments(data.data)
      } else {
        toast.error('Failed to fetch tasks')
      }
    } catch (error) {
      console.error('Error fetching tasks:', error)
      toast.error('Failed to fetch tasks')
    } finally {
      setLoading(false)
    }
  }

  const handleClaimReward = async (taskId: string) => {
    try {
      const response = await fetch(`/api/user/tasks/${taskId}/claim-reward`, {
        method: 'POST'
      })

      const data = await response.json()

      if (data.success) {
        toast.success('Reward claimed successfully!')
        fetchUserTasks()
      } else {
        toast.error(data.error || 'Failed to claim reward')
      }
    } catch (error) {
      console.error('Error claiming reward:', error)
      toast.error('Failed to claim reward')
    }
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'assigned':
        return <Badge variant="secondary"><Clock className="w-3 h-3 mr-1" />Assigned</Badge>
      case 'in_progress':
        return <Badge variant="default"><Target className="w-3 h-3 mr-1" />In Progress</Badge>
      case 'completed':
        return <Badge variant="default" className="bg-green-600"><CheckCircle className="w-3 h-3 mr-1" />Completed</Badge>
      case 'expired':
        return <Badge variant="destructive"><AlertCircle className="w-3 h-3 mr-1" />Expired</Badge>
      default:
        return <Badge variant="secondary">{status}</Badge>
    }
  }

  const getTaskTypeIcon = (taskType: string) => {
    switch (taskType) {
      case 'sales_target':
        return <Target className="w-4 h-4" />
      case 'referral_target':
        return <Users className="w-4 h-4" />
      default:
        return <Gift className="w-4 h-4" />
    }
  }

  const getTaskTypeLabel = (taskType: string) => {
    switch (taskType) {
      case 'sales_target':
        return 'Sales Target'
      case 'referral_target':
        return 'Referral Target'
      case 'custom':
        return 'Custom Task'
      default:
        return taskType
    }
  }

  const calculateProgress = (assignment: UserTaskAssignment) => {
    const { progress, task } = assignment
    const requirements = task.requirements || {}

    if (task.task_type === 'sales_target') {
      const current = progress.current_sales || 0
      const target = requirements.sales_count || 1
      return Math.min((current / target) * 100, 100)
    } else if (task.task_type === 'referral_target') {
      const current = progress.current_referrals || 0
      const target = requirements.referral_count || 1
      return Math.min((current / target) * 100, 100)
    }

    return progress.completion_percentage || 0
  }

  const getProgressText = (assignment: UserTaskAssignment) => {
    const { progress, task } = assignment
    const requirements = task.requirements || {}

    if (task.task_type === 'sales_target') {
      const current = progress.current_sales || 0
      const target = requirements.sales_count || 1
      return `${current} / ${target} sales`
    } else if (task.task_type === 'referral_target') {
      const current = progress.current_referrals || 0
      const target = requirements.referral_count || 1
      return `${current} / ${target} referrals`
    }

    return `${Math.round(progress.completion_percentage || 0)}% complete`
  }

  const filterAssignments = (status: string) => {
    switch (status) {
      case 'active':
        return assignments.filter(a => ['assigned', 'in_progress'].includes(a.status))
      case 'completed':
        return assignments.filter(a => a.status === 'completed')
      case 'expired':
        return assignments.filter(a => a.status === 'expired')
      default:
        return assignments
    }
  }

  if (loading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-pink-600 mx-auto"></div>
            <p className="mt-2 text-gray-600">Loading your tasks...</p>
          </div>
        </div>
      </DashboardLayout>
    )
  }

  const activeAssignments = filterAssignments('active')
  const completedAssignments = filterAssignments('completed')
  const expiredAssignments = filterAssignments('expired')

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div>
          <h1 className="text-3xl font-bold text-gray-900">My Tasks</h1>
          <p className="text-gray-600 mt-1">
            Complete tasks to earn rewards and bonuses
          </p>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">Active Tasks</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-blue-600">{activeAssignments.length}</div>
              <p className="text-xs text-gray-500 mt-1">Tasks in progress</p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">Completed</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">{completedAssignments.length}</div>
              <p className="text-xs text-gray-500 mt-1">Tasks completed</p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">Total Rewards</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-amber-600">
                Rs {completedAssignments.reduce((sum, a) => sum + a.task.reward_amount, 0).toLocaleString()}
              </div>
              <p className="text-xs text-gray-500 mt-1">Rewards earned</p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">Success Rate</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-purple-600">
                {assignments.length > 0 ? Math.round((completedAssignments.length / assignments.length) * 100) : 0}%
              </div>
              <p className="text-xs text-gray-500 mt-1">Completion rate</p>
            </CardContent>
          </Card>
        </div>

        {/* Tasks Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="active">Active Tasks ({activeAssignments.length})</TabsTrigger>
            <TabsTrigger value="completed">Completed ({completedAssignments.length})</TabsTrigger>
            <TabsTrigger value="expired">Expired ({expiredAssignments.length})</TabsTrigger>
          </TabsList>

          {/* Active Tasks Tab */}
          <TabsContent value="active" className="space-y-4">
            {activeAssignments.length > 0 ? (
              activeAssignments.map((assignment) => (
                <Card key={assignment.id} className="hover:shadow-md transition-shadow">
                  <CardHeader>
                    <div className="flex items-start justify-between">
                      <div className="flex items-center gap-3">
                        <div className="p-2 bg-blue-100 rounded-lg">
                          {getTaskTypeIcon(assignment.task.task_type)}
                        </div>
                        <div>
                          <CardTitle className="text-lg">{assignment.task.title}</CardTitle>
                          <p className="text-sm text-gray-500 mt-1">
                            {getTaskTypeLabel(assignment.task.task_type)} •
                            Reward: Rs {assignment.task.reward_amount.toLocaleString()}
                          </p>
                        </div>
                      </div>
                      {getStatusBadge(assignment.status)}
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {assignment.task.description && (
                      <p className="text-gray-600">{assignment.task.description}</p>
                    )}

                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-600">Progress:</span>
                        <span className="font-medium">{getProgressText(assignment)}</span>
                      </div>
                      <Progress value={calculateProgress(assignment)} className="h-2" />
                    </div>

                    {assignment.task.expires_at && (
                      <div className="flex items-center gap-2 text-sm text-orange-600">
                        <Clock className="w-4 h-4" />
                        Expires: {new Date(assignment.task.expires_at).toLocaleDateString()}
                      </div>
                    )}

                    {assignment.status === 'completed' && !assignment.reward_claimed_at && (
                      <Button
                        onClick={() => handleClaimReward(assignment.task_id)}
                        className="w-full bg-green-600 hover:bg-green-700"
                      >
                        <Gift className="w-4 h-4 mr-2" />
                        Claim Reward (Rs {assignment.task.reward_amount.toLocaleString()})
                      </Button>
                    )}
                  </CardContent>
                </Card>
              ))
            ) : (
              <Card>
                <CardContent className="text-center py-8">
                  <Target className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No active tasks</h3>
                  <p className="text-gray-500">
                    You don't have any active tasks at the moment. New tasks will appear here when available.
                  </p>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          {/* Completed Tasks Tab */}
          <TabsContent value="completed" className="space-y-4">
            {completedAssignments.length > 0 ? (
              completedAssignments.map((assignment) => (
                <Card key={assignment.id} className="border-green-200">
                  <CardHeader>
                    <div className="flex items-start justify-between">
                      <div className="flex items-center gap-3">
                        <div className="p-2 bg-green-100 rounded-lg">
                          <CheckCircle className="w-4 h-4 text-green-600" />
                        </div>
                        <div>
                          <CardTitle className="text-lg">{assignment.task.title}</CardTitle>
                          <p className="text-sm text-gray-500 mt-1">
                            Completed on {new Date(assignment.completed_at!).toLocaleDateString()}
                          </p>
                        </div>
                      </div>
                      <Badge variant="default" className="bg-green-600">
                        <CheckCircle className="w-3 h-3 mr-1" />
                        Completed
                      </Badge>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="flex items-center justify-between">
                      <span className="text-gray-600">Reward Earned:</span>
                      <span className="font-bold text-green-600">
                        Rs {assignment.task.reward_amount.toLocaleString()}
                      </span>
                    </div>
                    {assignment.reward_claimed_at && (
                      <div className="mt-2 text-sm text-green-600">
                        ✓ Reward claimed on {new Date(assignment.reward_claimed_at).toLocaleDateString()}
                      </div>
                    )}
                  </CardContent>
                </Card>
              ))
            ) : (
              <Card>
                <CardContent className="text-center py-8">
                  <CheckCircle className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No completed tasks</h3>
                  <p className="text-gray-500">
                    Complete your active tasks to see them here.
                  </p>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          {/* Expired Tasks Tab */}
          <TabsContent value="expired" className="space-y-4">
            {expiredAssignments.length > 0 ? (
              expiredAssignments.map((assignment) => (
                <Card key={assignment.id} className="border-red-200 opacity-75">
                  <CardHeader>
                    <div className="flex items-start justify-between">
                      <div className="flex items-center gap-3">
                        <div className="p-2 bg-red-100 rounded-lg">
                          <AlertCircle className="w-4 h-4 text-red-600" />
                        </div>
                        <div>
                          <CardTitle className="text-lg text-gray-600">{assignment.task.title}</CardTitle>
                          <p className="text-sm text-gray-500 mt-1">
                            Task expired • Reward was Rs {assignment.task.reward_amount.toLocaleString()}
                          </p>
                        </div>
                      </div>
                      <Badge variant="destructive">
                        <AlertCircle className="w-3 h-3 mr-1" />
                        Expired
                      </Badge>
                    </div>
                  </CardHeader>
                </Card>
              ))
            ) : (
              <Card>
                <CardContent className="text-center py-8">
                  <AlertCircle className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No expired tasks</h3>
                  <p className="text-gray-500">
                    Keep up the good work! No tasks have expired.
                  </p>
                </CardContent>
              </Card>
            )}
          </TabsContent>
        </Tabs>
      </div>
    </DashboardLayout>
  )
}