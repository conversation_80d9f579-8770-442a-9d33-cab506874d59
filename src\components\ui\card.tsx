'use client'

import { HTMLAttributes, forwardRef } from 'react'
import { motion, HTMLMotionProps } from 'framer-motion'
import { clsx } from 'clsx'
import { cardVariants } from '@/lib/animations'

interface CardProps extends HTMLAttributes<HTMLDivElement> {
  variant?: 'default' | 'elevated' | 'outlined' | 'glass' | 'premium' | 'glow'
  padding?: 'none' | 'xs' | 'sm' | 'md' | 'lg' | 'xl'
  animated?: boolean
  hover?: boolean
  glow?: boolean
}

const Card = forwardRef<HTMLDivElement, CardProps>(
  ({
    className,
    variant = 'default',
    padding = 'md',
    animated = true,
    hover = true,
    glow = false,
    children,
    ...props
  }, ref) => {
    const baseClasses = 'bg-white transition-all duration-300'

    const variants = {
      default: 'shadow-sm border border-gray-200 hover:shadow-md',
      elevated: 'shadow-lg hover:shadow-xl',
      outlined: 'border-2 border-gray-200 hover:border-primary-blue/30 hover:shadow-sm',
      glass: 'glass-card backdrop-blur-xl border border-white/20 hover:border-white/30',
      premium: 'shadow-premium hover:shadow-glow border border-gray-100 bg-gradient-to-br from-white to-gray-50/50',
      glow: 'shadow-glow hover:shadow-2xl border border-primary-blue/20 bg-gradient-to-br from-white to-primary-50/30'
    }

    const paddings = {
      none: '',
      xs: 'p-2',
      sm: 'p-3',
      md: 'p-4',
      lg: 'p-6',
      xl: 'p-8'
    }

    const cardClasses = clsx(
      baseClasses,
      variants[variant],
      paddings[padding],
      hover && 'hover-lift',
      glow && 'animate-glow',
      className
    )

    if (animated) {
      return (
        <motion.div
          ref={ref}
          className={cardClasses}
          variants={cardVariants}
          initial="rest"
          whileHover={hover ? "hover" : "rest"}
          whileTap="tap"
          {...props}
        >
          {children}
        </motion.div>
      )
    }

    return (
      <div
        ref={ref}
        className={cardClasses}
        {...props}
      >
        {children}
      </div>
    )
  }
)

Card.displayName = 'Card'

// Card sub-components
const CardHeader = forwardRef<HTMLDivElement, HTMLAttributes<HTMLDivElement>>(
  ({ className, children, ...props }, ref) => (
    <div
      className={clsx('flex flex-col space-y-1.5 pb-4', className)}
      ref={ref}
      {...props}
    >
      {children}
    </div>
  )
)

CardHeader.displayName = 'CardHeader'

const CardTitle = forwardRef<HTMLHeadingElement, HTMLAttributes<HTMLHeadingElement>>(
  ({ className, children, ...props }, ref) => (
    <h3
      className={clsx('text-lg font-semibold leading-none tracking-tight text-gray-900', className)}
      ref={ref}
      {...props}
    >
      {children}
    </h3>
  )
)

CardTitle.displayName = 'CardTitle'

const CardContent = forwardRef<HTMLDivElement, HTMLAttributes<HTMLDivElement>>(
  ({ className, children, ...props }, ref) => (
    <div
      className={clsx('text-gray-600', className)}
      ref={ref}
      {...props}
    >
      {children}
    </div>
  )
)

CardContent.displayName = 'CardContent'

const CardFooter = forwardRef<HTMLDivElement, HTMLAttributes<HTMLDivElement>>(
  ({ className, children, ...props }, ref) => (
    <div
      className={clsx('flex items-center pt-4', className)}
      ref={ref}
      {...props}
    >
      {children}
    </div>
  )
)

CardFooter.displayName = 'CardFooter'

export default Card
export { Card, CardHeader, CardTitle, CardContent, CardFooter }

// Add CardDescription component
export const CardDescription = forwardRef<HTMLParagraphElement, HTMLAttributes<HTMLParagraphElement>>(
  ({ className, children, ...props }, ref) => (
    <p
      className={clsx('text-sm text-gray-500', className)}
      ref={ref}
      {...props}
    >
      {children}
    </p>
  )
)

CardDescription.displayName = 'CardDescription'
