import { NextRequest, NextResponse } from 'next/server'
import { CommissionSystemService } from '@/lib/services/commissionSystem'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { purchaserId, subscriptionId, packageAmount } = body

    if (!purchaserId || !subscriptionId || !packageAmount) {
      return NextResponse.json({ 
        error: 'purchaserId, subscriptionId, and packageAmount are required' 
      }, { status: 400 })
    }

    console.log(`Testing commission distribution for:`)
    console.log(`- Purchaser ID: ${purchaserId}`)
    console.log(`- Subscription ID: ${subscriptionId}`)
    console.log(`- Package Amount: Rs ${packageAmount}`)

    // Test commission distribution
    const result = await CommissionSystemService.distributeCommissions(
      purchaserId,
      subscriptionId,
      packageAmount
    )

    console.log('Commission distribution result:', result)

    return NextResponse.json({
      success: true,
      message: 'Commission distribution completed successfully',
      data: result
    })

  } catch (error) {
    console.error('Commission distribution test failed:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined
    }, { status: 500 })
  }
}
