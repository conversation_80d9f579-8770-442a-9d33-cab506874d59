"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/wallet/page",{

/***/ "(app-pages-browser)/./src/lib/services/commissionSystem.ts":
/*!**********************************************!*\
  !*** ./src/lib/services/commissionSystem.ts ***!
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CommissionSystemService: function() { return /* binding */ CommissionSystemService; }\n/* harmony export */ });\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/supabase */ \"(app-pages-browser)/./src/lib/supabase.ts\");\n\n/**\n * CommissionSystemService - Handles commission calculations and distributions\n */ class CommissionSystemService {\n    /**\n   * Get commission structure for a package value\n   */ static async getCommissionStructure(packageValue) {\n        try {\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.COMMISSION_STRUCTURE).select(\"*\").lte(\"package_value\", packageValue).eq(\"is_active\", true).order(\"package_value\", {\n                ascending: false\n            });\n            if (error) {\n                throw new Error(\"Failed to get commission structure: \".concat(error.message));\n            }\n            return data || [];\n        } catch (error) {\n            console.error(\"Error getting commission structure:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Get all commission structures (for admin)\n   */ static async getAllCommissionStructures() {\n        try {\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.COMMISSION_STRUCTURE).select(\"*\").order(\"package_value\", {\n                ascending: true\n            });\n            if (error) {\n                throw new Error(\"Failed to get all commission structures: \".concat(error.message));\n            }\n            return data || [];\n        } catch (error) {\n            console.error(\"Error getting all commission structures:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Create new commission structure\n   */ static async createCommissionStructure(structure) {\n        try {\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.COMMISSION_STRUCTURE).insert(structure).select().single();\n            if (error) {\n                throw new Error(\"Failed to create commission structure: \".concat(error.message));\n            }\n            return data;\n        } catch (error) {\n            console.error(\"Error creating commission structure:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Calculate and distribute commissions for a subscription purchase\n   * NEW: Handles leftover commissions by sending them to company wallet\n   */ static async distributeCommissions(purchaserId, subscriptionId, packageAmount) {\n        try {\n            if (!_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin) {\n                throw new Error(\"Admin client not available\");\n            }\n            console.log(\"Starting commission distribution for subscription \".concat(subscriptionId, \", package amount: Rs \").concat(packageAmount));\n            // Get commission distributions using the new absolute function\n            const { data: distributions, error: distributionError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.rpc(\"calculate_commission_distribution_absolute\", {\n                p_purchaser_id: purchaserId,\n                p_subscription_id: subscriptionId,\n                p_package_price: packageAmount\n            });\n            if (distributionError) {\n                throw new Error(\"Failed to calculate commission distributions: \".concat(distributionError.message));\n            }\n            if (!distributions || distributions.length === 0) {\n                console.warn(\"No commission distributions calculated\");\n                return {\n                    totalDistributed: 0,\n                    transactionsCreated: 0,\n                    unallocatedAmount: 0,\n                    distributionDetails: []\n                };\n            }\n            let totalDistributed = 0;\n            let distributionCount = 0;\n            let companyLeftoverAmount = 0;\n            // Process each commission distribution\n            for (const distribution of distributions){\n                var _distribution_metadata;\n                // Handle company leftover allocation separately\n                if (distribution.beneficiary_id === null && distribution.commission_type === \"company_leftover_allocation\") {\n                    var _distribution_metadata1;\n                    // ✅ Use new function to properly update company wallet balance\n                    const { error: companyWalletError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.rpc(\"update_company_wallet_balance\", {\n                        p_amount: distribution.commission_amount,\n                        p_transaction_type: \"company_profit\",\n                        p_description: \"Leftover commission allocation from network distribution (\".concat(((_distribution_metadata1 = distribution.metadata) === null || _distribution_metadata1 === void 0 ? void 0 : _distribution_metadata1.missing_positions) || 0, \" missing positions)\"),\n                        p_subscription_id: subscriptionId,\n                        p_metadata: {\n                            ...distribution.metadata,\n                            subscription_id: subscriptionId,\n                            package_price: packageAmount\n                        }\n                    });\n                    if (companyWalletError) {\n                        console.error(\"Failed to credit company wallet with leftover:\", companyWalletError);\n                        throw new Error(\"Failed to credit company wallet: \".concat(companyWalletError.message));\n                    }\n                    companyLeftoverAmount += parseFloat(distribution.commission_amount.toString());\n                    console.log(\"Credited Rs \".concat(distribution.commission_amount, \" leftover to company wallet\"));\n                    continue; // Skip normal commission processing for company allocations\n                }\n                const transactionId = \"comm_\".concat(Date.now(), \"_\").concat(Math.random().toString(36).substr(2, 9));\n                // Insert commission transaction (let id auto-generate as UUID)\n                const { error: commissionError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from(\"commission_transactions\").insert({\n                    transaction_id: transactionId,\n                    user_id: distribution.beneficiary_id,\n                    beneficiary_id: distribution.beneficiary_id,\n                    subscription_purchase_id: subscriptionId,\n                    commission_type: distribution.commission_type,\n                    commission_level: distribution.level_position,\n                    package_value: packageAmount,\n                    commission_rate: 0,\n                    commission_amount: distribution.commission_amount,\n                    status: \"processed\",\n                    metadata: distribution.metadata\n                });\n                if (commissionError) {\n                    console.error(\"Failed to insert commission transaction:\", commissionError);\n                    throw new Error(\"Failed to insert commission transaction: \".concat(commissionError.message));\n                }\n                // Get user's wallet ID first\n                const { data: userWallet, error: walletFetchError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from(\"user_wallets\").select(\"id\").eq(\"user_id\", distribution.beneficiary_id).single();\n                if (walletFetchError || !userWallet) {\n                    console.error(\"Failed to get wallet for user \".concat(distribution.beneficiary_id, \":\"), walletFetchError);\n                    throw new Error(\"Failed to get wallet for user \".concat(distribution.beneficiary_id, \": \").concat((walletFetchError === null || walletFetchError === void 0 ? void 0 : walletFetchError.message) || \"Wallet not found\"));\n                }\n                // ✅ CORRECTED: Check if this is a gift system commission\n                const isGiftSystemCommission = ((_distribution_metadata = distribution.metadata) === null || _distribution_metadata === void 0 ? void 0 : _distribution_metadata.gift_system) === true || [\n                    \"present_user\",\n                    \"annual_present_user\",\n                    \"present_leader\",\n                    \"annual_present_leader\"\n                ].includes(distribution.commission_type);\n                // Only update wallet balance for NON-gift system commissions\n                if (!isGiftSystemCommission) {\n                    // Update user's wallet balance using correct wallet_id\n                    const { error: walletError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.rpc(\"update_wallet_balance\", {\n                        p_wallet_id: userWallet.id,\n                        p_amount: distribution.commission_amount,\n                        p_transaction_type: \"deposit\",\n                        p_description: \"\".concat(distribution.commission_type, \" commission from subscription\"),\n                        p_reference_id: subscriptionId,\n                        p_reference_type: \"subscription\",\n                        p_metadata: {\n                            commission_type: distribution.commission_type,\n                            commission_level: distribution.level_position,\n                            subscription_id: subscriptionId\n                        }\n                    });\n                    if (walletError) {\n                        console.error(\"Failed to update wallet balance:\", walletError);\n                        throw new Error(\"Failed to update wallet balance: \".concat(walletError.message));\n                    }\n                    console.log(\"✅ Added Rs \".concat(distribution.commission_amount, \" to wallet for \").concat(distribution.commission_type));\n                } else {\n                    console.log(\"⚠️ Skipped wallet update for gift system commission: \".concat(distribution.commission_type, \" (Rs \").concat(distribution.commission_amount, \")\"));\n                }\n                totalDistributed += parseFloat(distribution.commission_amount.toString());\n                distributionCount++;\n            }\n            // Process gift system commissions\n            try {\n                await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.rpc(\"distribute_gift_system_commissions\", {\n                    p_purchaser_id: purchaserId,\n                    p_subscription_id: subscriptionId,\n                    p_package_price: packageAmount\n                });\n                console.log(\"Gift system commissions distributed successfully\");\n            } catch (giftError) {\n                console.error(\"Failed to distribute gift system commissions:\", giftError);\n            // Don't fail the main distribution, but log the error\n            }\n            // Credit company profit to company wallet\n            const { data: packageStructure } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from(\"commission_structure\").select(\"company_wallet_amount\").eq(\"package_value\", packageAmount).eq(\"commission_type\", \"unified_structure\").single();\n            if (packageStructure === null || packageStructure === void 0 ? void 0 : packageStructure.company_wallet_amount) {\n                // ✅ Use new function to properly update company wallet balance\n                const { error: companyProfitError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.rpc(\"update_company_wallet_balance\", {\n                    p_amount: packageStructure.company_wallet_amount,\n                    p_transaction_type: \"company_profit\",\n                    p_description: \"Company profit from subscription package Rs. \".concat(packageAmount),\n                    p_subscription_id: subscriptionId,\n                    p_metadata: {\n                        package_price: packageAmount,\n                        subscription_id: subscriptionId\n                    }\n                });\n                if (companyProfitError) {\n                    console.error(\"Failed to credit company profit:\", companyProfitError);\n                    throw new Error(\"Failed to credit company profit: \".concat(companyProfitError.message));\n                }\n            }\n            console.log(\"Commission distribution completed: Rs \".concat(totalDistributed, \" to \").concat(distributionCount, \" recipients, Rs \").concat(companyLeftoverAmount, \" leftover to company\"));\n            return {\n                totalDistributed,\n                transactionsCreated: distributionCount,\n                unallocatedAmount: companyLeftoverAmount,\n                distributionDetails: distributions.filter((d)=>d.beneficiary_id !== null).map((d)=>({\n                        level: d.level_position,\n                        beneficiaryId: d.beneficiary_id,\n                        amount: parseFloat(d.commission_amount.toString()),\n                        rate: 0 // Not applicable for absolute amounts\n                    }))\n            };\n        } catch (error) {\n            console.error(\"Error distributing commissions:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Process pending commission transactions and credit user wallets\n   */ static async processPendingCommissions() {\n        try {\n            if (!_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin) {\n                throw new Error(\"Admin client not available\");\n            }\n            // Get all pending commission transactions\n            const { data: pendingCommissions, error: fetchError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.COMMISSION_TRANSACTIONS).select(\"*\").eq(\"status\", \"pending\").order(\"created_at\", {\n                ascending: true\n            });\n            if (fetchError) {\n                throw new Error(\"Failed to fetch pending commissions: \".concat(fetchError.message));\n            }\n            if (!pendingCommissions || pendingCommissions.length === 0) {\n                return {\n                    processed: 0,\n                    totalAmount: 0,\n                    errors: []\n                };\n            }\n            let processedCount = 0;\n            let totalAmount = 0;\n            const errors = [];\n            for (const commission of pendingCommissions){\n                try {\n                    // Get beneficiary's wallet\n                    const { data: wallet, error: walletError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from(\"user_wallets\").select(\"id, balance\").eq(\"user_id\", commission.beneficiary_id).single();\n                    if (walletError || !wallet) {\n                        errors.push(\"No wallet found for beneficiary \".concat(commission.beneficiary_id));\n                        continue;\n                    }\n                    // Create wallet transaction\n                    const { data: walletTransaction, error: walletTransError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from(\"wallet_transactions\").insert({\n                        wallet_id: wallet.id,\n                        transaction_type: \"commission\",\n                        amount: commission.commission_amount,\n                        balance_before: wallet.balance,\n                        balance_after: parseFloat(wallet.balance) + parseFloat(commission.commission_amount),\n                        description: \"Level \".concat(commission.commission_level, \" commission from subscription\"),\n                        reference_id: commission.subscription_purchase_id,\n                        reference_type: \"subscription\",\n                        status: \"completed\",\n                        metadata: {\n                            commission_type: commission.commission_type,\n                            commission_level: commission.commission_level,\n                            original_transaction_id: commission.transaction_id\n                        }\n                    }).select().single();\n                    if (walletTransError) {\n                        errors.push(\"Failed to create wallet transaction for \".concat(commission.beneficiary_id, \": \").concat(walletTransError.message));\n                        continue;\n                    }\n                    // Update wallet balance\n                    const { error: balanceError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from(\"user_wallets\").update({\n                        balance: parseFloat(wallet.balance) + parseFloat(commission.commission_amount),\n                        updated_at: new Date().toISOString()\n                    }).eq(\"id\", wallet.id);\n                    if (balanceError) {\n                        errors.push(\"Failed to update wallet balance for \".concat(commission.beneficiary_id, \": \").concat(balanceError.message));\n                        continue;\n                    }\n                    // Update commission transaction status\n                    const { error: commissionUpdateError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.COMMISSION_TRANSACTIONS).update({\n                        status: \"processed\",\n                        processed_at: new Date().toISOString(),\n                        wallet_transaction_id: walletTransaction.id\n                    }).eq(\"id\", commission.id);\n                    if (commissionUpdateError) {\n                        errors.push(\"Failed to update commission status for \".concat(commission.id, \": \").concat(commissionUpdateError.message));\n                        continue;\n                    }\n                    processedCount++;\n                    totalAmount += parseFloat(commission.commission_amount);\n                } catch (error) {\n                    errors.push(\"Error processing commission \".concat(commission.id, \": \").concat(error instanceof Error ? error.message : \"Unknown error\"));\n                }\n            }\n            return {\n                processed: processedCount,\n                totalAmount,\n                errors\n            };\n        } catch (error) {\n            console.error(\"Error processing pending commissions:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Get commission summary for admin dashboard\n   */ static async getCommissionSummary() {\n        try {\n            var _totalPaidResult_data, _pendingResult_data, _topEarnersResult_data;\n            const [totalPaidResult, pendingResult, failedResult, totalTransactionsResult, topEarnersResult] = await Promise.all([\n                _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.COMMISSION_TRANSACTIONS).select(\"commission_amount\").eq(\"status\", \"processed\"),\n                _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.COMMISSION_TRANSACTIONS).select(\"commission_amount\").eq(\"status\", \"pending\"),\n                _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.COMMISSION_TRANSACTIONS).select(\"*\", {\n                    count: \"exact\",\n                    head: true\n                }).eq(\"status\", \"failed\"),\n                _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.COMMISSION_TRANSACTIONS).select(\"*\", {\n                    count: \"exact\",\n                    head: true\n                }),\n                _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.USERS).select(\"id, full_name, total_commission_earned\").gt(\"total_commission_earned\", 0).order(\"total_commission_earned\", {\n                    ascending: false\n                }).limit(10)\n            ]);\n            const totalCommissionsPaid = ((_totalPaidResult_data = totalPaidResult.data) === null || _totalPaidResult_data === void 0 ? void 0 : _totalPaidResult_data.reduce((sum, t)=>sum + t.commission_amount, 0)) || 0;\n            const pendingCommissions = ((_pendingResult_data = pendingResult.data) === null || _pendingResult_data === void 0 ? void 0 : _pendingResult_data.reduce((sum, t)=>sum + t.commission_amount, 0)) || 0;\n            const failedCommissions = failedResult.count || 0;\n            const totalTransactions = totalTransactionsResult.count || 0;\n            const topEarners = ((_topEarnersResult_data = topEarnersResult.data) === null || _topEarnersResult_data === void 0 ? void 0 : _topEarnersResult_data.map((user)=>({\n                    userId: user.id,\n                    fullName: user.full_name || \"Unknown\",\n                    totalEarned: user.total_commission_earned || 0\n                }))) || [];\n            return {\n                totalCommissionsPaid,\n                pendingCommissions,\n                failedCommissions,\n                totalTransactions,\n                topEarners\n            };\n        } catch (error) {\n            console.error(\"Error getting commission summary:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Update commission structure rates\n   */ static async updateCommissionStructure(id, updates) {\n        try {\n            const { error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.COMMISSION_STRUCTURE).update(updates).eq(\"id\", id);\n            if (error) {\n                throw new Error(\"Failed to update commission structure: \".concat(error.message));\n            }\n        } catch (error) {\n            console.error(\"Error updating commission structure:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Get commission transactions for a specific user with filters\n   * Excludes gift system commissions from user wallet view\n   */ static async getUserCommissionTransactions(userId) {\n        let filters = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {}, page = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 1, limit = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : 20;\n        try {\n            const offset = (page - 1) * limit;\n            let query = _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.COMMISSION_TRANSACTIONS).select(\"*\").eq(\"beneficiary_id\", userId)// Exclude gift system commissions from user wallet view\n            .not(\"commission_type\", \"in\", \"(present_user,annual_present_user,present_leader,annual_present_leader)\");\n            // Apply filters\n            if (filters.status) {\n                query = query.eq(\"status\", filters.status);\n            }\n            if (filters.commissionType) {\n                query = query.eq(\"commission_type\", filters.commissionType);\n            }\n            if (filters.dateFrom) {\n                query = query.gte(\"created_at\", filters.dateFrom);\n            }\n            if (filters.dateTo) {\n                query = query.lte(\"created_at\", filters.dateTo);\n            }\n            const [dataResult, countResult] = await Promise.all([\n                query.order(\"created_at\", {\n                    ascending: false\n                }).range(offset, offset + limit - 1),\n                query.select(\"*\", {\n                    count: \"exact\",\n                    head: true\n                })\n            ]);\n            if (dataResult.error) {\n                throw new Error(\"Failed to get commission transactions: \".concat(dataResult.error.message));\n            }\n            if (countResult.error) {\n                throw new Error(\"Failed to count commission transactions: \".concat(countResult.error.message));\n            }\n            return {\n                transactions: dataResult.data || [],\n                total: countResult.count || 0\n            };\n        } catch (error) {\n            console.error(\"Error getting user commission transactions:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Get detailed commission analytics for admin reports\n   */ static async getCommissionAnalytics() {\n        let filters = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n        try {\n            let query = _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.COMMISSION_TRANSACTIONS).select(\"*\");\n            // Apply filters\n            if (filters.status) {\n                query = query.eq(\"status\", filters.status);\n            }\n            if (filters.commissionType) {\n                query = query.eq(\"commission_type\", filters.commissionType);\n            }\n            if (filters.level) {\n                query = query.eq(\"commission_level\", parseInt(filters.level));\n            }\n            if (filters.dateFrom) {\n                query = query.gte(\"created_at\", filters.dateFrom);\n            }\n            if (filters.dateTo) {\n                query = query.lte(\"created_at\", filters.dateTo);\n            }\n            const [dataResult, countResult] = await Promise.all([\n                query.order(\"created_at\", {\n                    ascending: false\n                }).limit(1000),\n                query.select(\"*\", {\n                    count: \"exact\",\n                    head: true\n                })\n            ]);\n            if (dataResult.error) {\n                throw new Error(\"Failed to get commission analytics: \".concat(dataResult.error.message));\n            }\n            if (countResult.error) {\n                throw new Error(\"Failed to count commission analytics: \".concat(countResult.error.message));\n            }\n            const transactions = dataResult.data || [];\n            // Calculate monthly data\n            const monthlyMap = new Map();\n            transactions.forEach((t)=>{\n                const month = new Date(t.created_at).toISOString().slice(0, 7) // YYYY-MM\n                ;\n                const existing = monthlyMap.get(month) || {\n                    amount: 0,\n                    count: 0\n                };\n                monthlyMap.set(month, {\n                    amount: existing.amount + t.commission_amount,\n                    count: existing.count + 1\n                });\n            });\n            const monthlyData = Array.from(monthlyMap.entries()).map((param)=>{\n                let [month, data] = param;\n                return {\n                    month,\n                    ...data\n                };\n            }).sort((a, b)=>a.month.localeCompare(b.month));\n            // Calculate level distribution\n            const levelMap = new Map();\n            transactions.forEach((t)=>{\n                const existing = levelMap.get(t.commission_level) || {\n                    amount: 0,\n                    count: 0\n                };\n                levelMap.set(t.commission_level, {\n                    amount: existing.amount + t.commission_amount,\n                    count: existing.count + 1\n                });\n            });\n            const levelDistribution = Array.from(levelMap.entries()).map((param)=>{\n                let [level, data] = param;\n                return {\n                    level,\n                    ...data\n                };\n            }).sort((a, b)=>a.level - b.level);\n            // Calculate type distribution\n            const typeMap = new Map();\n            transactions.forEach((t)=>{\n                const existing = typeMap.get(t.commission_type) || {\n                    amount: 0,\n                    count: 0\n                };\n                typeMap.set(t.commission_type, {\n                    amount: existing.amount + t.commission_amount,\n                    count: existing.count + 1\n                });\n            });\n            const typeDistribution = Array.from(typeMap.entries()).map((param)=>{\n                let [type, data] = param;\n                return {\n                    type,\n                    ...data\n                };\n            });\n            return {\n                transactions,\n                total: countResult.count || 0,\n                monthlyData,\n                levelDistribution,\n                typeDistribution\n            };\n        } catch (error) {\n            console.error(\"Error getting commission analytics:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Get commission transactions with pagination and filters\n   */ static async getCommissionTransactionsWithFilters() {\n        let filters = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {}, page = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 1, limit = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 50;\n        try {\n            const offset = (page - 1) * limit;\n            let query = _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.COMMISSION_TRANSACTIONS).select(\"*\");\n            // Apply filters\n            if (filters.status) {\n                query = query.eq(\"status\", filters.status);\n            }\n            if (filters.commissionType) {\n                query = query.eq(\"commission_type\", filters.commissionType);\n            }\n            if (filters.level) {\n                query = query.eq(\"commission_level\", parseInt(filters.level));\n            }\n            if (filters.dateFrom) {\n                query = query.gte(\"created_at\", filters.dateFrom);\n            }\n            if (filters.dateTo) {\n                query = query.lte(\"created_at\", filters.dateTo);\n            }\n            const [dataResult, countResult] = await Promise.all([\n                query.order(\"created_at\", {\n                    ascending: false\n                }).range(offset, offset + limit - 1),\n                query.select(\"*\", {\n                    count: \"exact\",\n                    head: true\n                })\n            ]);\n            if (dataResult.error) {\n                throw new Error(\"Failed to get commission transactions: \".concat(dataResult.error.message));\n            }\n            if (countResult.error) {\n                throw new Error(\"Failed to count commission transactions: \".concat(countResult.error.message));\n            }\n            return {\n                transactions: dataResult.data || [],\n                total: countResult.count || 0\n            };\n        } catch (error) {\n            console.error(\"Error getting commission transactions with filters:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Get commission breakdown by type for a user\n   */ static async getCommissionBreakdown(userId) {\n        try {\n            const response = await fetch(\"/api/commission-breakdown?userId=\".concat(userId));\n            const result = await response.json();\n            if (!response.ok) {\n                throw new Error(result.error || \"Failed to fetch commission breakdown\");\n            }\n            if (!result.success) {\n                throw new Error(result.error || \"API request failed\");\n            }\n            // Map the extended data to the basic breakdown format\n            const data = result.data;\n            const breakdown = {\n                directCommission: data.directCommission || 0,\n                levelCommission: data.levelCommission || 0,\n                rsmBonus: data.rsmBonuses || 0,\n                zmBonus: data.zmBonuses || 0,\n                okdoiHeadCommission: data.okdoiHeadCommission || 0,\n                totalCommissions: data.totalCommissions || 0\n            };\n            return breakdown;\n        } catch (error) {\n            console.error(\"Error getting commission breakdown:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Get extended commission breakdown with all commission types\n   */ static async getExtendedCommissionBreakdown(userId) {\n        try {\n            const response = await fetch(\"/api/commission-breakdown?userId=\".concat(userId));\n            const result = await response.json();\n            if (!response.ok) {\n                throw new Error(result.error || \"Failed to fetch commission breakdown\");\n            }\n            if (!result.success) {\n                throw new Error(result.error || \"API request failed\");\n            }\n            return result.data;\n        } catch (error) {\n            console.error(\"Error getting extended commission breakdown:\", error);\n            throw error;\n        }\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/services/commissionSystem.ts\n"));

/***/ })

});