-- Create admin_settings table for storing application configuration
CREATE TABLE IF NOT EXISTS admin_settings (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    key VARCHAR(100) NOT NULL UNIQUE,
    value JSONB NOT NULL,
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create index for faster key lookups
CREATE INDEX IF NOT EXISTS idx_admin_settings_key ON admin_settings(key);

-- Insert default settings
INSERT INTO admin_settings (key, value, description, created_at, updated_at) VALUES
('site_name', '"OKDOI"', 'Name of the website', NOW(), NOW()),
('site_description', '"Premium Marketplace for Everything"', 'Description of the website', NOW(), NOW()),
('admin_email', '"<EMAIL>"', 'Administrator email address', NOW(), NOW()),
('allow_registration', 'true', 'Whether new user registration is allowed', NOW(), NOW()),
('require_email_verification', 'false', 'Whether email verification is required for new users', NOW(), NOW()),
('auto_approve_ads', 'false', 'Whether ads are automatically approved', NOW(), NOW()),
('max_images_per_ad', '10', 'Maximum number of images per ad', NOW(), NOW()),
('ad_expiry_days', '30', 'Number of days before ads expire', NOW(), NOW()),
('enable_notifications', 'true', 'Whether notifications are enabled', NOW(), NOW()),
('maintenance_mode', 'false', 'Whether the site is in maintenance mode', NOW(), NOW()),
('enable_analytics', 'true', 'Whether analytics are enabled', NOW(), NOW()),
('enable_referrals', 'true', 'Whether referral system is enabled', NOW(), NOW()),
('free_ads_limit', '2', 'Number of free ads new users can post without a subscription', NOW(), NOW())
ON CONFLICT (key) DO NOTHING;

-- Enable RLS
ALTER TABLE admin_settings ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
-- Only admins can read/write admin settings
CREATE POLICY "Admin users can read admin settings" ON admin_settings
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM users 
            WHERE users.id = auth.uid() 
            AND (users.role = 'admin' OR users.is_super_admin = true)
        )
    );

CREATE POLICY "Admin users can insert admin settings" ON admin_settings
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM users 
            WHERE users.id = auth.uid() 
            AND (users.role = 'admin' OR users.is_super_admin = true)
        )
    );

CREATE POLICY "Admin users can update admin settings" ON admin_settings
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM users 
            WHERE users.id = auth.uid() 
            AND (users.role = 'admin' OR users.is_super_admin = true)
        )
    );

CREATE POLICY "Admin users can delete admin settings" ON admin_settings
    FOR DELETE USING (
        EXISTS (
            SELECT 1 FROM users 
            WHERE users.id = auth.uid() 
            AND (users.role = 'admin' OR users.is_super_admin = true)
        )
    );

-- Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_admin_settings_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to automatically update updated_at
CREATE TRIGGER update_admin_settings_updated_at_trigger
    BEFORE UPDATE ON admin_settings
    FOR EACH ROW
    EXECUTE FUNCTION update_admin_settings_updated_at();
