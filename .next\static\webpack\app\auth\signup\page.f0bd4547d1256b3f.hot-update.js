"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/auth/signup/page",{

/***/ "(app-pages-browser)/./src/components/auth/NewSignUpForm.tsx":
/*!***********************************************!*\
  !*** ./src/components/auth/NewSignUpForm.tsx ***!
  \***********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ NewSignUpForm; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_EyeOff_Lock_Mail_MapPin_Phone_Plane_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,EyeOff,Lock,Mail,MapPin,Phone,Plane,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_EyeOff_Lock_Mail_MapPin_Phone_Plane_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,EyeOff,Lock,Mail,MapPin,Phone,Plane,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_EyeOff_Lock_Mail_MapPin_Phone_Plane_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,EyeOff,Lock,Mail,MapPin,Phone,Plane,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/lock.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_EyeOff_Lock_Mail_MapPin_Phone_Plane_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,EyeOff,Lock,Mail,MapPin,Phone,Plane,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plane.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_EyeOff_Lock_Mail_MapPin_Phone_Plane_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,EyeOff,Lock,Mail,MapPin,Phone,Plane,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_EyeOff_Lock_Mail_MapPin_Phone_Plane_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,EyeOff,Lock,Mail,MapPin,Phone,Plane,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_EyeOff_Lock_Mail_MapPin_Phone_Plane_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,EyeOff,Lock,Mail,MapPin,Phone,Plane,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye-off.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_EyeOff_Lock_Mail_MapPin_Phone_Plane_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,EyeOff,Lock,Mail,MapPin,Phone,Plane,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _lib_services_referralSystem__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/services/referralSystem */ \"(app-pages-browser)/./src/lib/services/referralSystem.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction NewSignUpForm(param) {\n    let { referralCode } = param;\n    _s();\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        email: \"\",\n        password: \"\",\n        confirmPassword: \"\",\n        fullName: \"\",\n        phone: \"\",\n        location: \"\",\n        gender: \"\",\n        religion: \"\",\n        referralCode: referralCode || \"\" // Keep for backend compatibility but hide from UI\n    });\n    const [showPassword, setShowPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showConfirmPassword, setShowConfirmPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [acceptTerms, setAcceptTerms] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [success, setSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [referrer, setReferrer] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [referrerLoading, setReferrerLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { signUp } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__.useAuth)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const fetchReferrer = async ()=>{\n            if (referralCode) {\n                setReferrerLoading(true);\n                try {\n                    const referrerData = await _lib_services_referralSystem__WEBPACK_IMPORTED_MODULE_6__.ReferralSystemService.validateReferralCode(referralCode);\n                    setReferrer(referrerData);\n                } catch (error) {\n                    console.warn(\"Could not fetch referrer information:\", error);\n                } finally{\n                    setReferrerLoading(false);\n                }\n            }\n        };\n        fetchReferrer();\n    }, [\n        referralCode\n    ]);\n    const handleChange = (e)=>{\n        setFormData((prev)=>({\n                ...prev,\n                [e.target.name]: e.target.value\n            }));\n    };\n    const validateForm = ()=>{\n        if (!formData.fullName.trim()) {\n            setError(\"Full name is required\");\n            return false;\n        }\n        if (!formData.email) {\n            setError(\"Email is required\");\n            return false;\n        }\n        if (!/\\S+@\\S+\\.\\S+/.test(formData.email)) {\n            setError(\"Please enter a valid email address\");\n            return false;\n        }\n        if (!formData.phone.trim()) {\n            setError(\"Phone number is required\");\n            return false;\n        }\n        if (!formData.location.trim()) {\n            setError(\"Location is required\");\n            return false;\n        }\n        if (!formData.gender) {\n            setError(\"Gender is required\");\n            return false;\n        }\n        if (!formData.religion) {\n            setError(\"Religion is required\");\n            return false;\n        }\n        if (!formData.password) {\n            setError(\"Password is required\");\n            return false;\n        }\n        if (formData.password.length < 6) {\n            setError(\"Password must be at least 6 characters long\");\n            return false;\n        }\n        if (formData.password !== formData.confirmPassword) {\n            setError(\"Passwords do not match\");\n            return false;\n        }\n        if (!acceptTerms) {\n            setError(\"Please accept the Terms of Service and Privacy Policy\");\n            return false;\n        }\n        return true;\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        setError(\"\");\n        setSuccess(\"\");\n        if (!validateForm()) return;\n        setLoading(true);\n        try {\n            const result = await signUp(formData.email, formData.password, {\n                full_name: formData.fullName,\n                phone: formData.phone,\n                location: formData.location,\n                gender: formData.gender,\n                religion: formData.religion,\n                referral_code: formData.referralCode\n            });\n            if (result.requireEmailVerification) {\n                // Show success message first, then redirect\n                setSuccess(\"Account created successfully! Please check your email for verification code.\");\n                // Delay redirect to prevent jarring UX\n                setTimeout(()=>{\n                    router.push(\"/auth/verify-otp?email=\".concat(encodeURIComponent(formData.email)));\n                }, 2000);\n            } else {\n                setSuccess(\"Account created successfully! Welcome to OKDOI!\");\n                setTimeout(()=>{\n                    router.push(\"/\");\n                }, 1500);\n            }\n        } catch (err) {\n            setError(err instanceof Error ? err.message : \"An error occurred during registration\");\n            setLoading(false) // Only set loading to false on error\n            ;\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"hidden lg:flex lg:w-1/2 relative bg-gradient-to-br from-blue-400 via-blue-500 to-blue-600\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            src: \"/images/hero/hero-bg-2.webp\",\n                            alt: \"OKDOI Marketplace\",\n                            fill: true,\n                            className: \"object-cover opacity-80\",\n                            priority: true\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                            lineNumber: 156,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                        lineNumber: 155,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative z-10 flex flex-col justify-center items-center text-white p-12\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center max-w-md\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-4xl font-bold mb-4 leading-tight\",\n                                    children: \"Join OKDOI\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                                    lineNumber: 168,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-lg opacity-90 mb-8\",\n                                    children: \"Create your premium marketplace account and start your journey with us\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                                    lineNumber: 171,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-center space-x-4 mb-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-white/20 rounded-full flex items-center justify-center backdrop-blur-sm\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_EyeOff_Lock_Mail_MapPin_Phone_Plane_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                className: \"w-6 h-6\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                                                lineNumber: 178,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                                            lineNumber: 177,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-white/20 rounded-full flex items-center justify-center backdrop-blur-sm\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_EyeOff_Lock_Mail_MapPin_Phone_Plane_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"w-6 h-6\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                                                lineNumber: 181,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                                            lineNumber: 180,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-white/20 rounded-full flex items-center justify-center backdrop-blur-sm\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_EyeOff_Lock_Mail_MapPin_Phone_Plane_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"w-6 h-6\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                                                lineNumber: 184,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                                            lineNumber: 183,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                                    lineNumber: 176,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                            lineNumber: 167,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                        lineNumber: 166,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute bottom-0 left-0 right-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-end justify-center space-x-2 mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-6 h-14 bg-white/25 rounded-t-lg\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                                        lineNumber: 194,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-8 h-18 bg-white/20 rounded-t-lg\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                                        lineNumber: 195,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-12 h-24 bg-white/25 rounded-t-lg relative\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute top-0 left-1/2 transform -translate-x-1/2 w-2 h-6 bg-white/40 rounded-full\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                                            lineNumber: 197,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                                        lineNumber: 196,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-10 h-20 bg-white/30 rounded-t-lg\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                                        lineNumber: 199,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-6 h-12 bg-white/25 rounded-t-lg\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                                        lineNumber: 200,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-8 h-16 bg-white/20 rounded-t-lg\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                                        lineNumber: 201,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                                lineNumber: 193,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                viewBox: \"0 0 1200 120\",\n                                className: \"w-full h-20 text-blue-300/30\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    d: \"M0,60 C300,120 900,0 1200,60 L1200,120 L0,120 Z\",\n                                    fill: \"currentColor\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                                    lineNumber: 204,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                                lineNumber: 203,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                        lineNumber: 191,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                lineNumber: 154,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full lg:w-1/2 flex items-center justify-center p-8 bg-gray-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-full max-w-md\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-center mb-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_EyeOff_Lock_Mail_MapPin_Phone_Plane_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"w-8 h-8 text-blue-500 transform rotate-45\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                                        lineNumber: 215,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                                    lineNumber: 214,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl font-bold text-gray-900 mb-2\",\n                                    children: \"Create Account\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                                    lineNumber: 217,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600\",\n                                    children: \"Join OKDOI Marketplace\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                                    lineNumber: 218,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                            lineNumber: 213,\n                            columnNumber: 11\n                        }, this),\n                        success && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-6 p-4 bg-green-50 border border-green-200 rounded-lg\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-green-600 text-sm\",\n                                children: success\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                                lineNumber: 224,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                            lineNumber: 223,\n                            columnNumber: 13\n                        }, this),\n                        error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-6 p-4 bg-red-50 border border-red-200 rounded-lg\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-red-600 text-sm\",\n                                children: error\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                                lineNumber: 231,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                            lineNumber: 230,\n                            columnNumber: 13\n                        }, this),\n                        referralCode && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg\",\n                            children: referrerLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                                        lineNumber: 240,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-blue-600 text-sm\",\n                                        children: \"Loading referrer information...\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                                        lineNumber: 241,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                                lineNumber: 239,\n                                columnNumber: 17\n                            }, this) : referrer ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_EyeOff_Lock_Mail_MapPin_Phone_Plane_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"w-5 h-5 text-blue-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                                            lineNumber: 246,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                                        lineNumber: 245,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-blue-800 font-medium text-sm\",\n                                                children: [\n                                                    \"You are invited by \",\n                                                    referrer.full_name || referrer.email\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                                                lineNumber: 249,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-blue-600 text-xs\",\n                                                children: \"Join the OKDOI community through this referral\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                                                lineNumber: 252,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                                        lineNumber: 248,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                                lineNumber: 244,\n                                columnNumber: 17\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_EyeOff_Lock_Mail_MapPin_Phone_Plane_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"w-5 h-5 text-blue-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                                            lineNumber: 260,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                                        lineNumber: 259,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-blue-800 font-medium text-sm\",\n                                                children: \"You are invited to join OKDOI\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                                                lineNumber: 263,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-blue-600 text-xs\",\n                                                children: \"Join the OKDOI community through this referral\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                                                lineNumber: 266,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                                        lineNumber: 262,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                                lineNumber: 258,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                            lineNumber: 237,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            onSubmit: handleSubmit,\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_EyeOff_Lock_Mail_MapPin_Phone_Plane_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                className: \"h-5 w-5 text-gray-400\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                                                lineNumber: 280,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                                            lineNumber: 279,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            name: \"fullName\",\n                                            value: formData.fullName,\n                                            onChange: handleChange,\n                                            placeholder: \"Enter your full name\",\n                                            required: true,\n                                            disabled: loading,\n                                            className: \"w-full pl-12 pr-4 py-3 border-2 border-blue-200 rounded-lg focus:border-blue-500 focus:outline-none transition-colors bg-white text-gray-900 placeholder-gray-400\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                                            lineNumber: 282,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"absolute -top-2 left-3 bg-white px-2 text-xs font-medium text-blue-600\",\n                                            children: \"Full Name\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                                            lineNumber: 292,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                                    lineNumber: 278,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_EyeOff_Lock_Mail_MapPin_Phone_Plane_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"h-5 w-5 text-gray-400\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                                                lineNumber: 300,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                                            lineNumber: 299,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"email\",\n                                            name: \"email\",\n                                            value: formData.email,\n                                            onChange: handleChange,\n                                            placeholder: \"Enter your email\",\n                                            required: true,\n                                            disabled: loading,\n                                            className: \"w-full pl-12 pr-4 py-3 border-2 border-blue-200 rounded-lg focus:border-blue-500 focus:outline-none transition-colors bg-white text-gray-900 placeholder-gray-400\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                                            lineNumber: 302,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"absolute -top-2 left-3 bg-white px-2 text-xs font-medium text-blue-600\",\n                                            children: \"Email Address\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                                            lineNumber: 312,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                                    lineNumber: 298,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-2 gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_EyeOff_Lock_Mail_MapPin_Phone_Plane_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"h-4 w-4 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                                                        lineNumber: 322,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                                                    lineNumber: 321,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"tel\",\n                                                    name: \"phone\",\n                                                    value: formData.phone,\n                                                    onChange: handleChange,\n                                                    placeholder: \"Phone\",\n                                                    disabled: loading,\n                                                    className: \"w-full pl-10 pr-3 py-3 border-2 border-blue-200 rounded-lg focus:border-blue-500 focus:outline-none transition-colors bg-white text-gray-900 placeholder-gray-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                                                    lineNumber: 324,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"absolute -top-2 left-3 bg-white px-2 text-xs font-medium text-blue-600\",\n                                                    children: \"Phone (Optional)\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                                                    lineNumber: 333,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                                            lineNumber: 320,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_EyeOff_Lock_Mail_MapPin_Phone_Plane_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"h-4 w-4 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                                                        lineNumber: 341,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                                                    lineNumber: 340,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    name: \"location\",\n                                                    value: formData.location,\n                                                    onChange: handleChange,\n                                                    placeholder: \"Location\",\n                                                    disabled: loading,\n                                                    className: \"w-full pl-10 pr-3 py-3 border-2 border-blue-200 rounded-lg focus:border-blue-500 focus:outline-none transition-colors bg-white text-gray-900 placeholder-gray-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                                                    lineNumber: 343,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"absolute -top-2 left-3 bg-white px-2 text-xs font-medium text-blue-600\",\n                                                    children: \"Location (Optional)\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                                                    lineNumber: 352,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                                            lineNumber: 339,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                                    lineNumber: 318,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_EyeOff_Lock_Mail_MapPin_Phone_Plane_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"h-5 w-5 text-gray-400\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                                                lineNumber: 361,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                                            lineNumber: 360,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: showPassword ? \"text\" : \"password\",\n                                            name: \"password\",\n                                            value: formData.password,\n                                            onChange: handleChange,\n                                            placeholder: \"Create a password\",\n                                            required: true,\n                                            disabled: loading,\n                                            className: \"w-full pl-12 pr-12 py-3 border-2 border-blue-200 rounded-lg focus:border-blue-500 focus:outline-none transition-colors bg-white text-gray-900 placeholder-gray-400\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                                            lineNumber: 363,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"absolute -top-2 left-3 bg-white px-2 text-xs font-medium text-blue-600\",\n                                            children: \"Password\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                                            lineNumber: 373,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            onClick: ()=>setShowPassword(!showPassword),\n                                            disabled: loading,\n                                            className: \"absolute inset-y-0 right-0 pr-4 flex items-center text-gray-400 hover:text-gray-600 transition-colors\",\n                                            children: showPassword ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_EyeOff_Lock_Mail_MapPin_Phone_Plane_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                                                lineNumber: 382,\n                                                columnNumber: 33\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_EyeOff_Lock_Mail_MapPin_Phone_Plane_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                                                lineNumber: 382,\n                                                columnNumber: 66\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                                            lineNumber: 376,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                                    lineNumber: 359,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-500 mt-1\",\n                                    children: \"Must be at least 6 characters long\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                                    lineNumber: 385,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_EyeOff_Lock_Mail_MapPin_Phone_Plane_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"h-5 w-5 text-gray-400\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                                                lineNumber: 390,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                                            lineNumber: 389,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: showConfirmPassword ? \"text\" : \"password\",\n                                            name: \"confirmPassword\",\n                                            value: formData.confirmPassword,\n                                            onChange: handleChange,\n                                            placeholder: \"Confirm your password\",\n                                            required: true,\n                                            disabled: loading,\n                                            className: \"w-full pl-12 pr-12 py-3 border-2 border-blue-200 rounded-lg focus:border-blue-500 focus:outline-none transition-colors bg-white text-gray-900 placeholder-gray-400\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                                            lineNumber: 392,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"absolute -top-2 left-3 bg-white px-2 text-xs font-medium text-blue-600\",\n                                            children: \"Confirm Password\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                                            lineNumber: 402,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            onClick: ()=>setShowConfirmPassword(!showConfirmPassword),\n                                            disabled: loading,\n                                            className: \"absolute inset-y-0 right-0 pr-4 flex items-center text-gray-400 hover:text-gray-600 transition-colors\",\n                                            children: showConfirmPassword ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_EyeOff_Lock_Mail_MapPin_Phone_Plane_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                                                lineNumber: 411,\n                                                columnNumber: 40\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_EyeOff_Lock_Mail_MapPin_Phone_Plane_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                                                lineNumber: 411,\n                                                columnNumber: 73\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                                            lineNumber: 405,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                                    lineNumber: 388,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-start space-x-3 pt-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"checkbox\",\n                                            id: \"acceptTerms\",\n                                            checked: acceptTerms,\n                                            onChange: (e)=>setAcceptTerms(e.target.checked),\n                                            disabled: loading,\n                                            className: \"mt-1 rounded border-gray-300 text-blue-600 focus:ring-blue-500 focus:ring-offset-0 transition-colors\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                                            lineNumber: 417,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"acceptTerms\",\n                                            className: \"text-sm text-gray-600 leading-relaxed\",\n                                            children: [\n                                                \"I agree to the\",\n                                                \" \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                    href: \"/terms\",\n                                                    className: \"text-blue-600 hover:text-blue-700 font-medium\",\n                                                    children: \"Terms of Service\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                                                    lineNumber: 427,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \" \",\n                                                \"and\",\n                                                \" \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                    href: \"/privacy\",\n                                                    className: \"text-blue-600 hover:text-blue-700 font-medium\",\n                                                    children: \"Privacy Policy\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                                                    lineNumber: 431,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                                            lineNumber: 425,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                                    lineNumber: 416,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"submit\",\n                                    disabled: loading,\n                                    className: \"w-full bg-blue-500 hover:bg-blue-600 disabled:bg-blue-300 text-white font-semibold py-4 rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 mt-6\",\n                                    children: loading ? \"Creating Account...\" : \"Create Account\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                                    lineNumber: 438,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                            lineNumber: 276,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mt-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600\",\n                                children: [\n                                    \"Already have an account?\",\n                                    \" \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        href: \"/auth/signin\",\n                                        className: \"text-blue-600 hover:text-blue-700 font-semibold transition-colors\",\n                                        children: \"Sign In\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                                        lineNumber: 451,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                                lineNumber: 449,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                            lineNumber: 448,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                    lineNumber: 211,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                lineNumber: 210,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n        lineNumber: 152,\n        columnNumber: 5\n    }, this);\n}\n_s(NewSignUpForm, \"ramtDhjd7ax9JEf3lYbr8JXRIYc=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__.useAuth,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = NewSignUpForm;\nvar _c;\n$RefreshReg$(_c, \"NewSignUpForm\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/auth/NewSignUpForm.tsx\n"));

/***/ })

});