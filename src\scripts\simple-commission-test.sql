-- Comprehensive Commission Distribution Test Suite
-- Tests the new commission distribution system according to Commission_Distribution_Explained.md
-- Date: 2025-01-09

-- =====================================================
-- 1. VERIFY ACCOUNT SETUP
-- =====================================================

-- Test OKDOI Head account
SELECT 
    '1. OKDOI Head Account' as test_name,
    CASE 
        WHEN COUNT(*) = 1 THEN 'PASS'
        ELSE 'FAIL'
    END as status,
    CASE 
        WHEN COUNT(*) = 1 THEN 'OKDOI Head account exists'
        ELSE 'OKDOI Head account missing'
    END as message,
    json_agg(json_build_object('id', id, 'email', email, 'full_name', full_name)) as details
FROM users 
WHERE email = '<EMAIL>' AND user_type = 'okdoi_head';

-- Test ZM account
SELECT 
    '2. Default ZM Account' as test_name,
    CASE 
        WHEN COUNT(*) = 1 THEN 'PASS'
        ELSE 'FAIL'
    END as status,
    CASE 
        WHEN COUNT(*) = 1 THEN 'Default ZM account exists'
        ELSE 'Default ZM account missing'
    END as message,
    json_agg(json_build_object('id', id, 'email', email, 'full_name', full_name, 'referred_by_id', referred_by_id)) as details
FROM users 
WHERE email = '<EMAIL>' AND user_type = 'zonal_manager';

-- Test ZM hierarchy
SELECT 
    '3. ZM Hierarchy' as test_name,
    CASE 
        WHEN zm.referred_by_id = okdoi.id THEN 'PASS'
        ELSE 'FAIL'
    END as status,
    CASE 
        WHEN zm.referred_by_id = okdoi.id THEN 'ZM correctly referred by OKDOI Head'
        ELSE 'ZM hierarchy incorrect'
    END as message,
    json_build_object(
        'zm_id', zm.id,
        'zm_referrer', zm.referred_by_id,
        'okdoi_head_id', okdoi.id
    ) as details
FROM users zm
CROSS JOIN users okdoi
WHERE zm.email = '<EMAIL>' 
AND okdoi.user_type = 'okdoi_head';

-- =====================================================
-- 2. VERIFY COMMISSION STRUCTURE
-- =====================================================

-- Test commission structures exist
SELECT 
    '4. Commission Structures' as test_name,
    CASE 
        WHEN COUNT(*) >= 4 THEN 'PASS'
        ELSE 'FAIL'
    END as status,
    CASE 
        WHEN COUNT(*) >= 4 THEN CONCAT('Found ', COUNT(*), ' commission structures')
        ELSE CONCAT('Only found ', COUNT(*), ' commission structures, expected 4+')
    END as message,
    json_agg(json_build_object('package_value', package_value, 'id', id)) as details
FROM commission_structure 
WHERE commission_type = 'unified_structure';

-- Test OKDOI Head rates
SELECT 
    '5. OKDOI Head Rates' as test_name,
    CASE 
        WHEN COUNT(CASE WHEN package_value = 2000 AND okdoi_head_rate_2000 = 0.025 THEN 1 END) = 1
         AND COUNT(CASE WHEN package_value = 5000 AND okdoi_head_rate_5000 = 0.02 THEN 1 END) = 1
         AND COUNT(CASE WHEN package_value = 10000 AND okdoi_head_rate_10000 = 0.02 THEN 1 END) = 1
         AND COUNT(CASE WHEN package_value = 50000 AND okdoi_head_rate_50000 = 0.02 THEN 1 END) = 1
        THEN 'PASS'
        ELSE 'FAIL'
    END as status,
    'OKDOI Head rates verification' as message,
    json_agg(json_build_object(
        'package_value', package_value,
        'rate_2000', okdoi_head_rate_2000,
        'rate_5000', okdoi_head_rate_5000,
        'rate_10000', okdoi_head_rate_10000,
        'rate_50000', okdoi_head_rate_50000
    )) as details
FROM commission_structure 
WHERE commission_type = 'unified_structure';

-- =====================================================
-- 3. VERIFY COMMISSION FUNCTIONS
-- =====================================================

-- Test commission distribution function exists
SELECT 
    '6. Commission Functions' as test_name,
    CASE 
        WHEN COUNT(*) > 0 THEN 'PASS'
        ELSE 'FAIL'
    END as status,
    CASE 
        WHEN COUNT(*) > 0 THEN 'Commission distribution function exists'
        ELSE 'Commission distribution function missing'
    END as message,
    json_agg(proname) as details
FROM pg_proc 
WHERE proname = 'calculate_commission_distribution';

-- Test OKDOI Head rate function exists
SELECT 
    '7. OKDOI Head Rate Function' as test_name,
    CASE 
        WHEN COUNT(*) > 0 THEN 'PASS'
        ELSE 'FAIL'
    END as status,
    CASE 
        WHEN COUNT(*) > 0 THEN 'OKDOI Head rate function exists'
        ELSE 'OKDOI Head rate function missing'
    END as message,
    json_agg(proname) as details
FROM pg_proc 
WHERE proname = 'get_okdoi_head_commission_rate';

-- =====================================================
-- 4. TEST NETWORK HIERARCHY
-- =====================================================

-- Test direct users under ZM
SELECT 
    '8. Direct Users Migration' as test_name,
    CASE 
        WHEN COUNT(*) > 0 THEN 'PASS'
        ELSE 'FAIL'
    END as status,
    CASE 
        WHEN COUNT(*) > 0 THEN CONCAT('Found ', COUNT(*), ' users under default ZM')
        ELSE 'No users found under default ZM'
    END as message,
    COUNT(*) as details
FROM users u
JOIN users zm ON zm.email = '<EMAIL>'
WHERE u.referred_by_id = zm.id AND u.user_type = 'user';

-- Test orphaned users (should be none except OKDOI Head)
SELECT 
    '9. Orphaned Users Check' as test_name,
    CASE 
        WHEN COUNT(*) = 0 THEN 'PASS'
        ELSE 'FAIL'
    END as status,
    CASE 
        WHEN COUNT(*) = 0 THEN 'No orphaned users found'
        ELSE CONCAT('Found ', COUNT(*), ' orphaned users')
    END as message,
    json_agg(json_build_object('id', id, 'email', email, 'user_type', user_type)) as details
FROM users 
WHERE referred_by_id IS NULL AND user_type != 'okdoi_head';

-- =====================================================
-- 5. TEST COMMISSION DISTRIBUTION SIMULATION
-- =====================================================

-- Create a test commission distribution simulation
DO $$
DECLARE
    test_user_id UUID;
    okdoi_head_id UUID;
    zm_id UUID;
    test_package_id UUID := gen_random_uuid();
    test_amount DECIMAL(12,2) := 2000.00;
    initial_commission_count INTEGER;
    final_commission_count INTEGER;
BEGIN
    -- Get test users
    SELECT id INTO okdoi_head_id FROM users WHERE user_type = 'okdoi_head' LIMIT 1;
    SELECT id INTO zm_id FROM users WHERE email = '<EMAIL>';
    SELECT id INTO test_user_id FROM users WHERE referred_by_id = zm_id AND user_type = 'user' LIMIT 1;
    
    IF okdoi_head_id IS NULL OR zm_id IS NULL OR test_user_id IS NULL THEN
        RAISE NOTICE 'Test users not found - skipping commission simulation';
        RETURN;
    END IF;
    
    -- Count initial commissions
    SELECT COUNT(*) INTO initial_commission_count FROM commission_transactions;
    
    -- Run commission distribution
    PERFORM calculate_commission_distribution(test_user_id, test_package_id, test_amount);
    
    -- Count final commissions
    SELECT COUNT(*) INTO final_commission_count FROM commission_transactions;
    
    -- Log results
    RAISE NOTICE 'Commission simulation completed: % new transactions created', (final_commission_count - initial_commission_count);
    
EXCEPTION WHEN OTHERS THEN
    RAISE NOTICE 'Commission simulation failed: %', SQLERRM;
END $$;

-- Verify commission simulation results
SELECT 
    '10. Commission Simulation' as test_name,
    CASE 
        WHEN COUNT(*) > 0 THEN 'PASS'
        ELSE 'FAIL'
    END as status,
    CASE 
        WHEN COUNT(*) > 0 THEN CONCAT('Commission simulation created ', COUNT(*), ' transactions')
        ELSE 'Commission simulation failed - no transactions created'
    END as message,
    COUNT(*) as details
FROM commission_transactions 
WHERE created_at > NOW() - INTERVAL '1 minute';

-- =====================================================
-- 6. SUMMARY REPORT
-- =====================================================

-- Generate final summary
SELECT 
    'FINAL SUMMARY' as test_name,
    CASE 
        WHEN (
            SELECT COUNT(*) FROM users WHERE email = '<EMAIL>' AND user_type = 'okdoi_head'
        ) = 1
        AND (
            SELECT COUNT(*) FROM users WHERE email = '<EMAIL>' AND user_type = 'zonal_manager'
        ) = 1
        AND (
            SELECT COUNT(*) FROM commission_structure WHERE commission_type = 'unified_structure'
        ) >= 4
        AND (
            SELECT COUNT(*) FROM pg_proc WHERE proname = 'calculate_commission_distribution'
        ) > 0
        THEN 'SYSTEM READY'
        ELSE 'NEEDS ATTENTION'
    END as status,
    'Commission system comprehensive test results' as message,
    json_build_object(
        'okdoi_head_exists', (SELECT COUNT(*) FROM users WHERE email = '<EMAIL>' AND user_type = 'okdoi_head') = 1,
        'zm_exists', (SELECT COUNT(*) FROM users WHERE email = '<EMAIL>' AND user_type = 'zonal_manager') = 1,
        'commission_structures', (SELECT COUNT(*) FROM commission_structure WHERE commission_type = 'unified_structure'),
        'commission_functions', (SELECT COUNT(*) FROM pg_proc WHERE proname = 'calculate_commission_distribution'),
        'users_under_zm', (SELECT COUNT(*) FROM users u JOIN users zm ON zm.email = '<EMAIL>' WHERE u.referred_by_id = zm.id)
    ) as details;
