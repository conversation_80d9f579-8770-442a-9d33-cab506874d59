"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/auth/signup/page",{

/***/ "(app-pages-browser)/./src/components/auth/NewSignUpForm.tsx":
/*!***********************************************!*\
  !*** ./src/components/auth/NewSignUpForm.tsx ***!
  \***********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ NewSignUpForm; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_EyeOff_Lock_Mail_MapPin_Phone_Plane_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,EyeOff,Lock,Mail,MapPin,Phone,Plane,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_EyeOff_Lock_Mail_MapPin_Phone_Plane_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,EyeOff,Lock,Mail,MapPin,Phone,Plane,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_EyeOff_Lock_Mail_MapPin_Phone_Plane_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,EyeOff,Lock,Mail,MapPin,Phone,Plane,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/lock.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_EyeOff_Lock_Mail_MapPin_Phone_Plane_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,EyeOff,Lock,Mail,MapPin,Phone,Plane,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plane.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_EyeOff_Lock_Mail_MapPin_Phone_Plane_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,EyeOff,Lock,Mail,MapPin,Phone,Plane,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_EyeOff_Lock_Mail_MapPin_Phone_Plane_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,EyeOff,Lock,Mail,MapPin,Phone,Plane,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_EyeOff_Lock_Mail_MapPin_Phone_Plane_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,EyeOff,Lock,Mail,MapPin,Phone,Plane,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye-off.js\");\n/* harmony import */ var _barrel_optimize_names_Eye_EyeOff_Lock_Mail_MapPin_Phone_Plane_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Eye,EyeOff,Lock,Mail,MapPin,Phone,Plane,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _lib_services_referralSystem__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/services/referralSystem */ \"(app-pages-browser)/./src/lib/services/referralSystem.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction NewSignUpForm(param) {\n    let { referralCode } = param;\n    _s();\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        email: \"\",\n        password: \"\",\n        confirmPassword: \"\",\n        fullName: \"\",\n        phone: \"\",\n        location: \"\",\n        gender: \"\",\n        religion: \"\",\n        referralCode: referralCode || \"\" // Keep for backend compatibility but hide from UI\n    });\n    const [showPassword, setShowPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showConfirmPassword, setShowConfirmPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [acceptTerms, setAcceptTerms] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [success, setSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [referrer, setReferrer] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [referrerLoading, setReferrerLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { signUp } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__.useAuth)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const fetchReferrer = async ()=>{\n            if (referralCode) {\n                setReferrerLoading(true);\n                try {\n                    const referrerData = await _lib_services_referralSystem__WEBPACK_IMPORTED_MODULE_6__.ReferralSystemService.validateReferralCode(referralCode);\n                    setReferrer(referrerData);\n                } catch (error) {\n                    console.warn(\"Could not fetch referrer information:\", error);\n                } finally{\n                    setReferrerLoading(false);\n                }\n            }\n        };\n        fetchReferrer();\n    }, [\n        referralCode\n    ]);\n    const handleChange = (e)=>{\n        setFormData((prev)=>({\n                ...prev,\n                [e.target.name]: e.target.value\n            }));\n    };\n    const validateForm = ()=>{\n        if (!formData.fullName.trim()) {\n            setError(\"Full name is required\");\n            return false;\n        }\n        if (!formData.email) {\n            setError(\"Email is required\");\n            return false;\n        }\n        if (!/\\S+@\\S+\\.\\S+/.test(formData.email)) {\n            setError(\"Please enter a valid email address\");\n            return false;\n        }\n        if (!formData.password) {\n            setError(\"Password is required\");\n            return false;\n        }\n        if (formData.password.length < 6) {\n            setError(\"Password must be at least 6 characters long\");\n            return false;\n        }\n        if (formData.password !== formData.confirmPassword) {\n            setError(\"Passwords do not match\");\n            return false;\n        }\n        if (!acceptTerms) {\n            setError(\"Please accept the Terms of Service and Privacy Policy\");\n            return false;\n        }\n        return true;\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        setError(\"\");\n        setSuccess(\"\");\n        if (!validateForm()) return;\n        setLoading(true);\n        try {\n            const result = await signUp(formData.email, formData.password, {\n                full_name: formData.fullName,\n                phone: formData.phone,\n                location: formData.location,\n                referral_code: formData.referralCode\n            });\n            if (result.requireEmailVerification) {\n                // Show success message first, then redirect\n                setSuccess(\"Account created successfully! Please check your email for verification code.\");\n                // Delay redirect to prevent jarring UX\n                setTimeout(()=>{\n                    router.push(\"/auth/verify-otp?email=\".concat(encodeURIComponent(formData.email)));\n                }, 2000);\n            } else {\n                setSuccess(\"Account created successfully! Welcome to OKDOI!\");\n                setTimeout(()=>{\n                    router.push(\"/\");\n                }, 1500);\n            }\n        } catch (err) {\n            setError(err instanceof Error ? err.message : \"An error occurred during registration\");\n            setLoading(false) // Only set loading to false on error\n            ;\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"hidden lg:flex lg:w-1/2 relative bg-gradient-to-br from-blue-400 via-blue-500 to-blue-600\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            src: \"/images/hero/hero-bg-2.webp\",\n                            alt: \"OKDOI Marketplace\",\n                            fill: true,\n                            className: \"object-cover opacity-80\",\n                            priority: true\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                            lineNumber: 138,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                        lineNumber: 137,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative z-10 flex flex-col justify-center items-center text-white p-12\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center max-w-md\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-4xl font-bold mb-4 leading-tight\",\n                                    children: \"Join OKDOI\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                                    lineNumber: 150,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-lg opacity-90 mb-8\",\n                                    children: \"Create your premium marketplace account and start your journey with us\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                                    lineNumber: 153,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-center space-x-4 mb-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-white/20 rounded-full flex items-center justify-center backdrop-blur-sm\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_EyeOff_Lock_Mail_MapPin_Phone_Plane_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                className: \"w-6 h-6\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                                                lineNumber: 160,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                                            lineNumber: 159,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-white/20 rounded-full flex items-center justify-center backdrop-blur-sm\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_EyeOff_Lock_Mail_MapPin_Phone_Plane_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"w-6 h-6\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                                                lineNumber: 163,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                                            lineNumber: 162,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-white/20 rounded-full flex items-center justify-center backdrop-blur-sm\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_EyeOff_Lock_Mail_MapPin_Phone_Plane_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"w-6 h-6\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                                                lineNumber: 166,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                                            lineNumber: 165,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                                    lineNumber: 158,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                            lineNumber: 149,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                        lineNumber: 148,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute bottom-0 left-0 right-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-end justify-center space-x-2 mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-6 h-14 bg-white/25 rounded-t-lg\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                                        lineNumber: 176,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-8 h-18 bg-white/20 rounded-t-lg\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                                        lineNumber: 177,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-12 h-24 bg-white/25 rounded-t-lg relative\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute top-0 left-1/2 transform -translate-x-1/2 w-2 h-6 bg-white/40 rounded-full\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                                            lineNumber: 179,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                                        lineNumber: 178,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-10 h-20 bg-white/30 rounded-t-lg\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                                        lineNumber: 181,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-6 h-12 bg-white/25 rounded-t-lg\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                                        lineNumber: 182,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-8 h-16 bg-white/20 rounded-t-lg\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                                        lineNumber: 183,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                                lineNumber: 175,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                viewBox: \"0 0 1200 120\",\n                                className: \"w-full h-20 text-blue-300/30\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    d: \"M0,60 C300,120 900,0 1200,60 L1200,120 L0,120 Z\",\n                                    fill: \"currentColor\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                                    lineNumber: 186,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                                lineNumber: 185,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                        lineNumber: 173,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                lineNumber: 136,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full lg:w-1/2 flex items-center justify-center p-8 bg-gray-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-full max-w-md\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-center mb-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_EyeOff_Lock_Mail_MapPin_Phone_Plane_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"w-8 h-8 text-blue-500 transform rotate-45\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                                        lineNumber: 197,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                                    lineNumber: 196,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl font-bold text-gray-900 mb-2\",\n                                    children: \"Create Account\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                                    lineNumber: 199,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600\",\n                                    children: \"Join OKDOI Marketplace\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                                    lineNumber: 200,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                            lineNumber: 195,\n                            columnNumber: 11\n                        }, this),\n                        success && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-6 p-4 bg-green-50 border border-green-200 rounded-lg\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-green-600 text-sm\",\n                                children: success\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                                lineNumber: 206,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                            lineNumber: 205,\n                            columnNumber: 13\n                        }, this),\n                        error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-6 p-4 bg-red-50 border border-red-200 rounded-lg\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-red-600 text-sm\",\n                                children: error\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                                lineNumber: 213,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                            lineNumber: 212,\n                            columnNumber: 13\n                        }, this),\n                        referralCode && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg\",\n                            children: referrerLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                                        lineNumber: 222,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-blue-600 text-sm\",\n                                        children: \"Loading referrer information...\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                                        lineNumber: 223,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                                lineNumber: 221,\n                                columnNumber: 17\n                            }, this) : referrer ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_EyeOff_Lock_Mail_MapPin_Phone_Plane_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"w-5 h-5 text-blue-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                                            lineNumber: 228,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                                        lineNumber: 227,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-blue-800 font-medium text-sm\",\n                                                children: [\n                                                    \"You are invited by \",\n                                                    referrer.full_name || referrer.email\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                                                lineNumber: 231,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-blue-600 text-xs\",\n                                                children: \"Join the OKDOI community through this referral\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                                                lineNumber: 234,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                                        lineNumber: 230,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                                lineNumber: 226,\n                                columnNumber: 17\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_EyeOff_Lock_Mail_MapPin_Phone_Plane_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"w-5 h-5 text-blue-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                                            lineNumber: 242,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                                        lineNumber: 241,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-blue-800 font-medium text-sm\",\n                                                children: \"You are invited to join OKDOI\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                                                lineNumber: 245,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-blue-600 text-xs\",\n                                                children: \"Join the OKDOI community through this referral\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                                                lineNumber: 248,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                                        lineNumber: 244,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                                lineNumber: 240,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                            lineNumber: 219,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            onSubmit: handleSubmit,\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_EyeOff_Lock_Mail_MapPin_Phone_Plane_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                className: \"h-5 w-5 text-gray-400\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                                                lineNumber: 262,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                                            lineNumber: 261,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            name: \"fullName\",\n                                            value: formData.fullName,\n                                            onChange: handleChange,\n                                            placeholder: \"Enter your full name\",\n                                            required: true,\n                                            disabled: loading,\n                                            className: \"w-full pl-12 pr-4 py-3 border-2 border-blue-200 rounded-lg focus:border-blue-500 focus:outline-none transition-colors bg-white text-gray-900 placeholder-gray-400\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                                            lineNumber: 264,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"absolute -top-2 left-3 bg-white px-2 text-xs font-medium text-blue-600\",\n                                            children: \"Full Name\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                                            lineNumber: 274,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                                    lineNumber: 260,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_EyeOff_Lock_Mail_MapPin_Phone_Plane_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"h-5 w-5 text-gray-400\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                                                lineNumber: 282,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                                            lineNumber: 281,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"email\",\n                                            name: \"email\",\n                                            value: formData.email,\n                                            onChange: handleChange,\n                                            placeholder: \"Enter your email\",\n                                            required: true,\n                                            disabled: loading,\n                                            className: \"w-full pl-12 pr-4 py-3 border-2 border-blue-200 rounded-lg focus:border-blue-500 focus:outline-none transition-colors bg-white text-gray-900 placeholder-gray-400\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                                            lineNumber: 284,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"absolute -top-2 left-3 bg-white px-2 text-xs font-medium text-blue-600\",\n                                            children: \"Email Address\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                                            lineNumber: 294,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                                    lineNumber: 280,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-2 gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_EyeOff_Lock_Mail_MapPin_Phone_Plane_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"h-4 w-4 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                                                        lineNumber: 304,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                                                    lineNumber: 303,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"tel\",\n                                                    name: \"phone\",\n                                                    value: formData.phone,\n                                                    onChange: handleChange,\n                                                    placeholder: \"Phone\",\n                                                    disabled: loading,\n                                                    className: \"w-full pl-10 pr-3 py-3 border-2 border-blue-200 rounded-lg focus:border-blue-500 focus:outline-none transition-colors bg-white text-gray-900 placeholder-gray-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                                                    lineNumber: 306,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"absolute -top-2 left-3 bg-white px-2 text-xs font-medium text-blue-600\",\n                                                    children: \"Phone (Optional)\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                                                    lineNumber: 315,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                                            lineNumber: 302,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_EyeOff_Lock_Mail_MapPin_Phone_Plane_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"h-4 w-4 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                                                        lineNumber: 323,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                                                    lineNumber: 322,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    name: \"location\",\n                                                    value: formData.location,\n                                                    onChange: handleChange,\n                                                    placeholder: \"Location\",\n                                                    disabled: loading,\n                                                    className: \"w-full pl-10 pr-3 py-3 border-2 border-blue-200 rounded-lg focus:border-blue-500 focus:outline-none transition-colors bg-white text-gray-900 placeholder-gray-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                                                    lineNumber: 325,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"absolute -top-2 left-3 bg-white px-2 text-xs font-medium text-blue-600\",\n                                                    children: \"Location (Optional)\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                                                    lineNumber: 334,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                                            lineNumber: 321,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                                    lineNumber: 300,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_EyeOff_Lock_Mail_MapPin_Phone_Plane_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"h-5 w-5 text-gray-400\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                                                lineNumber: 343,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                                            lineNumber: 342,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: showPassword ? \"text\" : \"password\",\n                                            name: \"password\",\n                                            value: formData.password,\n                                            onChange: handleChange,\n                                            placeholder: \"Create a password\",\n                                            required: true,\n                                            disabled: loading,\n                                            className: \"w-full pl-12 pr-12 py-3 border-2 border-blue-200 rounded-lg focus:border-blue-500 focus:outline-none transition-colors bg-white text-gray-900 placeholder-gray-400\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                                            lineNumber: 345,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"absolute -top-2 left-3 bg-white px-2 text-xs font-medium text-blue-600\",\n                                            children: \"Password\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                                            lineNumber: 355,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            onClick: ()=>setShowPassword(!showPassword),\n                                            disabled: loading,\n                                            className: \"absolute inset-y-0 right-0 pr-4 flex items-center text-gray-400 hover:text-gray-600 transition-colors\",\n                                            children: showPassword ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_EyeOff_Lock_Mail_MapPin_Phone_Plane_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                                                lineNumber: 364,\n                                                columnNumber: 33\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_EyeOff_Lock_Mail_MapPin_Phone_Plane_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                                                lineNumber: 364,\n                                                columnNumber: 66\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                                            lineNumber: 358,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                                    lineNumber: 341,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-500 mt-1\",\n                                    children: \"Must be at least 6 characters long\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                                    lineNumber: 367,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_EyeOff_Lock_Mail_MapPin_Phone_Plane_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"h-5 w-5 text-gray-400\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                                                lineNumber: 372,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                                            lineNumber: 371,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: showConfirmPassword ? \"text\" : \"password\",\n                                            name: \"confirmPassword\",\n                                            value: formData.confirmPassword,\n                                            onChange: handleChange,\n                                            placeholder: \"Confirm your password\",\n                                            required: true,\n                                            disabled: loading,\n                                            className: \"w-full pl-12 pr-12 py-3 border-2 border-blue-200 rounded-lg focus:border-blue-500 focus:outline-none transition-colors bg-white text-gray-900 placeholder-gray-400\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                                            lineNumber: 374,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"absolute -top-2 left-3 bg-white px-2 text-xs font-medium text-blue-600\",\n                                            children: \"Confirm Password\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                                            lineNumber: 384,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"button\",\n                                            onClick: ()=>setShowConfirmPassword(!showConfirmPassword),\n                                            disabled: loading,\n                                            className: \"absolute inset-y-0 right-0 pr-4 flex items-center text-gray-400 hover:text-gray-600 transition-colors\",\n                                            children: showConfirmPassword ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_EyeOff_Lock_Mail_MapPin_Phone_Plane_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                                                lineNumber: 393,\n                                                columnNumber: 40\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Eye_EyeOff_Lock_Mail_MapPin_Phone_Plane_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                                                lineNumber: 393,\n                                                columnNumber: 73\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                                            lineNumber: 387,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                                    lineNumber: 370,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-start space-x-3 pt-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"checkbox\",\n                                            id: \"acceptTerms\",\n                                            checked: acceptTerms,\n                                            onChange: (e)=>setAcceptTerms(e.target.checked),\n                                            disabled: loading,\n                                            className: \"mt-1 rounded border-gray-300 text-blue-600 focus:ring-blue-500 focus:ring-offset-0 transition-colors\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                                            lineNumber: 399,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            htmlFor: \"acceptTerms\",\n                                            className: \"text-sm text-gray-600 leading-relaxed\",\n                                            children: [\n                                                \"I agree to the\",\n                                                \" \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                    href: \"/terms\",\n                                                    className: \"text-blue-600 hover:text-blue-700 font-medium\",\n                                                    children: \"Terms of Service\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                                                    lineNumber: 409,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \" \",\n                                                \"and\",\n                                                \" \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                    href: \"/privacy\",\n                                                    className: \"text-blue-600 hover:text-blue-700 font-medium\",\n                                                    children: \"Privacy Policy\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                                                    lineNumber: 413,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                                            lineNumber: 407,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                                    lineNumber: 398,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"submit\",\n                                    disabled: loading,\n                                    className: \"w-full bg-blue-500 hover:bg-blue-600 disabled:bg-blue-300 text-white font-semibold py-4 rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 mt-6\",\n                                    children: loading ? \"Creating Account...\" : \"Create Account\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                                    lineNumber: 420,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                            lineNumber: 258,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mt-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600\",\n                                children: [\n                                    \"Already have an account?\",\n                                    \" \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        href: \"/auth/signin\",\n                                        className: \"text-blue-600 hover:text-blue-700 font-semibold transition-colors\",\n                                        children: \"Sign In\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                                        lineNumber: 433,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                                lineNumber: 431,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                            lineNumber: 430,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                    lineNumber: 193,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n                lineNumber: 192,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\components\\\\auth\\\\NewSignUpForm.tsx\",\n        lineNumber: 134,\n        columnNumber: 5\n    }, this);\n}\n_s(NewSignUpForm, \"ramtDhjd7ax9JEf3lYbr8JXRIYc=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_5__.useAuth,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = NewSignUpForm;\nvar _c;\n$RefreshReg$(_c, \"NewSignUpForm\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/auth/NewSignUpForm.tsx\n"));

/***/ })

});