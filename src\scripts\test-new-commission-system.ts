/**
 * Test Script for New Commission Distribution System
 * Tests the implementation based on Commission_Distribution_Explained.md requirements
 */

import { supabase, supabaseAdmin } from '@/lib/supabase'

interface TestResult {
  testName: string
  passed: boolean
  details: string
  data?: any
}

class CommissionSystemTester {
  private results: TestResult[] = []

  private addResult(testName: string, passed: boolean, details: string, data?: any) {
    this.results.push({ testName, passed, details, data })
    console.log(`${passed ? '✅' : '❌'} ${testName}: ${details}`)
  }

  async testOKDOIHeadExists(): Promise<void> {
    try {
      const { data: okdoiHead, error } = await supabaseAdmin
        .from('users')
        .select('*')
        .eq('user_type', 'okdoi_head')
        .single()

      if (error || !okdoiHead) {
        this.addResult('OKDOI Head Exists', false, 'OKDOI Head user not found')
        return
      }

      if (okdoiHead.email === '<EMAIL>') {
        this.addResult('OKDOI Head Exists', true, `Found OKDOI Head: ${okdoiHead.email}`, okdoiHead)
      } else {
        this.addResult('OKDOI Head Exists', false, `Wrong email: ${okdoiHead.email}`)
      }
    } catch (error) {
      this.addResult('OKDOI Head Exists', false, `Error: ${error}`)
    }
  }

  async testDefaultZMExists(): Promise<void> {
    try {
      const { data: defaultZM, error } = await supabaseAdmin
        .from('users')
        .select('*')
        .eq('email', '<EMAIL>')
        .single()

      if (error || !defaultZM) {
        this.addResult('Default ZM Exists', false, 'Default ZM user not found')
        return
      }

      if (defaultZM.user_type === 'zonal_manager') {
        this.addResult('Default ZM Exists', true, `Found Default ZM: ${defaultZM.email}`, defaultZM)
      } else {
        this.addResult('Default ZM Exists', false, `Wrong user type: ${defaultZM.user_type}`)
      }
    } catch (error) {
      this.addResult('Default ZM Exists', false, `Error: ${error}`)
    }
  }

  async testCommissionStructureUpdated(): Promise<void> {
    try {
      const { data: structures, error } = await supabaseAdmin
        .from('commission_structure')
        .select('*')
        .eq('is_active', true)

      if (error || !structures || structures.length === 0) {
        this.addResult('Commission Structure Updated', false, 'No active commission structures found')
        return
      }

      const structure = structures[0]
      const hasNewFields = 
        'okdoi_head_rate_2000' in structure &&
        'okdoi_head_rate_5000' in structure &&
        'okdoi_head_rate_10000' in structure &&
        'okdoi_head_rate_50000' in structure

      if (hasNewFields) {
        this.addResult('Commission Structure Updated', true, 'New OKDOI Head rate fields found', {
          okdoi_head_rate_2000: structure.okdoi_head_rate_2000,
          okdoi_head_rate_5000: structure.okdoi_head_rate_5000,
          okdoi_head_rate_10000: structure.okdoi_head_rate_10000,
          okdoi_head_rate_50000: structure.okdoi_head_rate_50000
        })
      } else {
        this.addResult('Commission Structure Updated', false, 'New OKDOI Head rate fields not found')
      }
    } catch (error) {
      this.addResult('Commission Structure Updated', false, `Error: ${error}`)
    }
  }

  async testNewCommissionFunction(): Promise<void> {
    try {
      // Test if the new commission distribution function exists
      const { data, error } = await supabaseAdmin.rpc('calculate_commission_distribution_new', {
        purchaser_id: '00000000-0000-0000-0000-000000000000', // Dummy UUID
        package_id: '00000000-0000-0000-0000-000000000000',
        package_amount: 1000
      })

      // We expect this to fail with "OKDOI Head user not found" if function exists
      // or "function does not exist" if function doesn't exist
      if (error) {
        if (error.message.includes('OKDOI Head user not found')) {
          this.addResult('New Commission Function', true, 'Function exists and validates OKDOI Head requirement')
        } else if (error.message.includes('function') && error.message.includes('does not exist')) {
          this.addResult('New Commission Function', false, 'Function does not exist')
        } else {
          this.addResult('New Commission Function', true, `Function exists: ${error.message}`)
        }
      } else {
        this.addResult('New Commission Function', true, 'Function executed successfully')
      }
    } catch (error) {
      this.addResult('New Commission Function', false, `Error: ${error}`)
    }
  }

  async testLeftoverCommissionConstraint(): Promise<void> {
    try {
      // Check if wallet_transactions table accepts leftover_commission type
      const { data: wallets, error } = await supabaseAdmin
        .from('user_wallets')
        .select('id')
        .limit(1)

      if (error || !wallets || wallets.length === 0) {
        this.addResult('Leftover Commission Constraint', false, 'No wallets found for testing')
        return
      }

      // Try to insert a test leftover commission transaction
      const { error: insertError } = await supabaseAdmin
        .from('wallet_transactions')
        .insert({
          wallet_id: wallets[0].id,
          transaction_type: 'leftover_commission',
          amount: 1.00,
          description: 'Test leftover commission',
          status: 'completed'
        })

      if (insertError) {
        if (insertError.message.includes('violates check constraint')) {
          this.addResult('Leftover Commission Constraint', false, 'Leftover commission type not allowed in constraint')
        } else {
          this.addResult('Leftover Commission Constraint', false, `Insert error: ${insertError.message}`)
        }
      } else {
        this.addResult('Leftover Commission Constraint', true, 'Leftover commission type accepted')
        
        // Clean up test transaction
        await supabaseAdmin
          .from('wallet_transactions')
          .delete()
          .eq('description', 'Test leftover commission')
      }
    } catch (error) {
      this.addResult('Leftover Commission Constraint', false, `Error: ${error}`)
    }
  }

  async testGiftSystemFunction(): Promise<void> {
    try {
      // Test if the gift system function exists
      const { data, error } = await supabaseAdmin.rpc('distribute_gift_system_commissions', {
        purchaser_id: '00000000-0000-0000-0000-000000000000', // Dummy UUID
        package_id: '00000000-0000-0000-0000-000000000000',
        package_amount: 1000
      })

      if (error) {
        if (error.message.includes('function') && error.message.includes('does not exist')) {
          this.addResult('Gift System Function', false, 'Function does not exist')
        } else {
          this.addResult('Gift System Function', true, `Function exists: ${error.message}`)
        }
      } else {
        this.addResult('Gift System Function', true, 'Function executed successfully')
      }
    } catch (error) {
      this.addResult('Gift System Function', false, `Error: ${error}`)
    }
  }

  async testDirectUserMigration(): Promise<void> {
    try {
      // Check if there are any users without referrers (should be migrated to default ZM)
      const { data: directUsers, error } = await supabaseAdmin
        .from('users')
        .select('id, email, referred_by_id, user_type')
        .is('referred_by_id', null)
        .eq('user_type', 'user')

      if (error) {
        this.addResult('Direct User Migration', false, `Error: ${error.message}`)
        return
      }

      if (!directUsers || directUsers.length === 0) {
        this.addResult('Direct User Migration', true, 'No direct users found - all users have referrers')
      } else {
        this.addResult('Direct User Migration', false, `Found ${directUsers.length} users without referrers`, directUsers)
      }
    } catch (error) {
      this.addResult('Direct User Migration', false, `Error: ${error}`)
    }
  }

  async runAllTests(): Promise<TestResult[]> {
    console.log('🧪 Starting Commission System Tests...\n')

    await this.testOKDOIHeadExists()
    await this.testDefaultZMExists()
    await this.testCommissionStructureUpdated()
    await this.testNewCommissionFunction()
    await this.testLeftoverCommissionConstraint()
    await this.testGiftSystemFunction()
    await this.testDirectUserMigration()

    console.log('\n📊 Test Summary:')
    const passed = this.results.filter(r => r.passed).length
    const total = this.results.length
    console.log(`Passed: ${passed}/${total}`)
    
    if (passed === total) {
      console.log('🎉 All tests passed!')
    } else {
      console.log('⚠️  Some tests failed. Please review the results above.')
    }

    return this.results
  }

  getResults(): TestResult[] {
    return this.results
  }
}

// Export for use in other scripts
export { CommissionSystemTester }

// Run tests if this script is executed directly
if (require.main === module) {
  const tester = new CommissionSystemTester()
  tester.runAllTests().then(() => {
    process.exit(0)
  }).catch((error) => {
    console.error('Test execution failed:', error)
    process.exit(1)
  })
}
