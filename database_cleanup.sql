-- OKDOI Database Cleanup Script
-- This script removes all user-generated data while preserving system configuration
-- and the OKDOI Head + Company ZM accounts

-- ============================================================================
-- IMPORTANT: This script will DELETE all user data except OKDOI Head and ZM
-- ============================================================================

BEGIN;

-- Store OKDOI Head and Company ZM IDs for preservation
DO $$
DECLARE
    okdoi_head_id UUID := '5fa9fc30-3955-4fb6-91fe-2a2c6943aa98';
    company_zm_id UUID := '071381cb-ad98-42f2-a591-6ff996f0fb25';
    cleanup_count INTEGER;
BEGIN
    RAISE NOTICE '🧹 Starting OKDOI Database Cleanup...';
    RAISE NOTICE 'Preserving OKDOI Head: %', okdoi_head_id;
    RAISE NOTICE 'Preserving Company ZM: %', company_zm_id;
    RAISE NOTICE '';

    -- 1. Delete user-related transactional data (in dependency order)
    RAISE NOTICE '1. Cleaning transactional data...';
    
    -- Gift system data
    DELETE FROM gift_system_audit_log WHERE user_id NOT IN (okdoi_head_id, company_zm_id);
    GET DIAGNOSTICS cleanup_count = ROW_COUNT;
    RAISE NOTICE '   ✓ Gift audit logs: % deleted', cleanup_count;
    
    DELETE FROM gift_transactions WHERE user_id NOT IN (okdoi_head_id, company_zm_id);
    GET DIAGNOSTICS cleanup_count = ROW_COUNT;
    RAISE NOTICE '   ✓ Gift transactions: % deleted', cleanup_count;
    
    DELETE FROM present_allocations WHERE user_id NOT IN (okdoi_head_id, company_zm_id);
    GET DIAGNOSTICS cleanup_count = ROW_COUNT;
    RAISE NOTICE '   ✓ Present allocations: % deleted', cleanup_count;
    
    DELETE FROM user_task_assignments WHERE user_id NOT IN (okdoi_head_id, company_zm_id);
    GET DIAGNOSTICS cleanup_count = ROW_COUNT;
    RAISE NOTICE '   ✓ Task assignments: % deleted', cleanup_count;
    
    DELETE FROM user_tasks WHERE created_by NOT IN (okdoi_head_id, company_zm_id) OR created_by IS NULL;
    GET DIAGNOSTICS cleanup_count = ROW_COUNT;
    RAISE NOTICE '   ✓ User tasks: % deleted', cleanup_count;

    -- Commission and wallet data
    DELETE FROM commission_transactions WHERE user_id NOT IN (okdoi_head_id, company_zm_id) 
        AND beneficiary_id NOT IN (okdoi_head_id, company_zm_id);
    GET DIAGNOSTICS cleanup_count = ROW_COUNT;
    RAISE NOTICE '   ✓ Commission transactions: % deleted', cleanup_count;
    
    DELETE FROM wallet_transactions WHERE wallet_id NOT IN (
        SELECT id FROM user_wallets WHERE user_id IN (okdoi_head_id, company_zm_id)
    );
    GET DIAGNOSTICS cleanup_count = ROW_COUNT;
    RAISE NOTICE '   ✓ Wallet transactions: % deleted', cleanup_count;
    
    DELETE FROM p2p_transfers WHERE sender_id NOT IN (okdoi_head_id, company_zm_id) 
        OR receiver_id NOT IN (okdoi_head_id, company_zm_id);
    GET DIAGNOSTICS cleanup_count = ROW_COUNT;
    RAISE NOTICE '   ✓ P2P transfers: % deleted', cleanup_count;
    
    DELETE FROM deposit_requests WHERE user_id NOT IN (okdoi_head_id, company_zm_id);
    GET DIAGNOSTICS cleanup_count = ROW_COUNT;
    RAISE NOTICE '   ✓ Deposit requests: % deleted', cleanup_count;
    
    DELETE FROM withdrawal_requests WHERE user_id NOT IN (okdoi_head_id, company_zm_id);
    GET DIAGNOSTICS cleanup_count = ROW_COUNT;
    RAISE NOTICE '   ✓ Withdrawal requests: % deleted', cleanup_count;

    -- 2. Delete shop and e-commerce data
    RAISE NOTICE '';
    RAISE NOTICE '2. Cleaning e-commerce data...';
    
    DELETE FROM merchant_to_main_transfers WHERE merchant_wallet_id IN (
        SELECT id FROM merchant_wallets WHERE user_id NOT IN (okdoi_head_id, company_zm_id)
    );
    GET DIAGNOSTICS cleanup_count = ROW_COUNT;
    RAISE NOTICE '   ✓ Merchant transfers: % deleted', cleanup_count;
    
    DELETE FROM merchant_wallet_transactions WHERE merchant_wallet_id IN (
        SELECT id FROM merchant_wallets WHERE user_id NOT IN (okdoi_head_id, company_zm_id)
    );
    GET DIAGNOSTICS cleanup_count = ROW_COUNT;
    RAISE NOTICE '   ✓ Merchant wallet transactions: % deleted', cleanup_count;
    
    DELETE FROM merchant_wallets WHERE user_id NOT IN (okdoi_head_id, company_zm_id);
    GET DIAGNOSTICS cleanup_count = ROW_COUNT;
    RAISE NOTICE '   ✓ Merchant wallets: % deleted', cleanup_count;
    
    DELETE FROM order_status_history;
    GET DIAGNOSTICS cleanup_count = ROW_COUNT;
    RAISE NOTICE '   ✓ Order status history: % deleted', cleanup_count;
    
    DELETE FROM shop_orders;
    GET DIAGNOSTICS cleanup_count = ROW_COUNT;
    RAISE NOTICE '   ✓ Shop orders: % deleted', cleanup_count;
    
    DELETE FROM product_reviews;
    GET DIAGNOSTICS cleanup_count = ROW_COUNT;
    RAISE NOTICE '   ✓ Product reviews: % deleted', cleanup_count;
    
    DELETE FROM shop_reviews;
    GET DIAGNOSTICS cleanup_count = ROW_COUNT;
    RAISE NOTICE '   ✓ Shop reviews: % deleted', cleanup_count;
    
    DELETE FROM shop_followers;
    GET DIAGNOSTICS cleanup_count = ROW_COUNT;
    RAISE NOTICE '   ✓ Shop followers: % deleted', cleanup_count;
    
    DELETE FROM cart_items;
    GET DIAGNOSTICS cleanup_count = ROW_COUNT;
    RAISE NOTICE '   ✓ Cart items: % deleted', cleanup_count;
    
    DELETE FROM shop_product_images;
    GET DIAGNOSTICS cleanup_count = ROW_COUNT;
    RAISE NOTICE '   ✓ Product images: % deleted', cleanup_count;
    
    DELETE FROM shop_products;
    GET DIAGNOSTICS cleanup_count = ROW_COUNT;
    RAISE NOTICE '   ✓ Shop products: % deleted', cleanup_count;
    
    DELETE FROM vendor_shops WHERE user_id NOT IN (okdoi_head_id, company_zm_id);
    GET DIAGNOSTICS cleanup_count = ROW_COUNT;
    RAISE NOTICE '   ✓ Vendor shops: % deleted', cleanup_count;

    -- 3. Delete classified ads data
    RAISE NOTICE '';
    RAISE NOTICE '3. Cleaning classified ads data...';
    
    DELETE FROM chat_messages;
    GET DIAGNOSTICS cleanup_count = ROW_COUNT;
    RAISE NOTICE '   ✓ Chat messages: % deleted', cleanup_count;
    
    DELETE FROM chat_conversations;
    GET DIAGNOSTICS cleanup_count = ROW_COUNT;
    RAISE NOTICE '   ✓ Chat conversations: % deleted', cleanup_count;
    
    DELETE FROM user_favorites;
    GET DIAGNOSTICS cleanup_count = ROW_COUNT;
    RAISE NOTICE '   ✓ User favorites: % deleted', cleanup_count;
    
    DELETE FROM ad_boosts;
    GET DIAGNOSTICS cleanup_count = ROW_COUNT;
    RAISE NOTICE '   ✓ Ad boosts: % deleted', cleanup_count;
    
    DELETE FROM ad_images;
    GET DIAGNOSTICS cleanup_count = ROW_COUNT;
    RAISE NOTICE '   ✓ Ad images: % deleted', cleanup_count;
    
    DELETE FROM ads WHERE user_id NOT IN (okdoi_head_id, company_zm_id);
    GET DIAGNOSTICS cleanup_count = ROW_COUNT;
    RAISE NOTICE '   ✓ Ads: % deleted', cleanup_count;
    
    DELETE FROM ad_drafts WHERE user_id NOT IN (okdoi_head_id, company_zm_id);
    GET DIAGNOSTICS cleanup_count = ROW_COUNT;
    RAISE NOTICE '   ✓ Ad drafts: % deleted', cleanup_count;

    -- 4. Delete user subscription and referral data
    RAISE NOTICE '';
    RAISE NOTICE '4. Cleaning user subscriptions and referrals...';

    DELETE FROM user_subscriptions WHERE user_id NOT IN (okdoi_head_id, company_zm_id);
    GET DIAGNOSTICS cleanup_count = ROW_COUNT;
    RAISE NOTICE '   ✓ User subscriptions: % deleted', cleanup_count;

    DELETE FROM user_rewards WHERE user_id NOT IN (okdoi_head_id, company_zm_id);
    GET DIAGNOSTICS cleanup_count = ROW_COUNT;
    RAISE NOTICE '   ✓ User rewards: % deleted', cleanup_count;

    DELETE FROM referral_placements WHERE parent_id NOT IN (okdoi_head_id, company_zm_id)
        AND child_id NOT IN (okdoi_head_id, company_zm_id);
    GET DIAGNOSTICS cleanup_count = ROW_COUNT;
    RAISE NOTICE '   ✓ Referral placements: % deleted', cleanup_count;

    DELETE FROM referral_hierarchy WHERE user_id NOT IN (okdoi_head_id, company_zm_id)
        AND ancestor_id NOT IN (okdoi_head_id, company_zm_id);
    GET DIAGNOSTICS cleanup_count = ROW_COUNT;
    RAISE NOTICE '   ✓ Referral hierarchy: % deleted', cleanup_count;

    DELETE FROM referrals WHERE referred_id NOT IN (okdoi_head_id, company_zm_id);
    GET DIAGNOSTICS cleanup_count = ROW_COUNT;
    RAISE NOTICE '   ✓ Referrals: % deleted', cleanup_count;

    DELETE FROM referral_codes WHERE user_id NOT IN (okdoi_head_id, company_zm_id);
    GET DIAGNOSTICS cleanup_count = ROW_COUNT;
    RAISE NOTICE '   ✓ Referral codes: % deleted', cleanup_count;

    -- 5. Delete KYC and user management data
    RAISE NOTICE '';
    RAISE NOTICE '5. Cleaning KYC and user management data...';

    DELETE FROM kyc_status_history WHERE user_id NOT IN (okdoi_head_id, company_zm_id);
    GET DIAGNOSTICS cleanup_count = ROW_COUNT;
    RAISE NOTICE '   ✓ KYC status history: % deleted', cleanup_count;

    DELETE FROM kyc_submissions WHERE user_id NOT IN (okdoi_head_id, company_zm_id);
    GET DIAGNOSTICS cleanup_count = ROW_COUNT;
    RAISE NOTICE '   ✓ KYC submissions: % deleted', cleanup_count;

    DELETE FROM zonal_managers WHERE user_id NOT IN (okdoi_head_id, company_zm_id);
    GET DIAGNOSTICS cleanup_count = ROW_COUNT;
    RAISE NOTICE '   ✓ Zonal managers: % deleted', cleanup_count;

    DELETE FROM regional_sales_managers WHERE user_id NOT IN (okdoi_head_id, company_zm_id);
    GET DIAGNOSTICS cleanup_count = ROW_COUNT;
    RAISE NOTICE '   ✓ Regional sales managers: % deleted', cleanup_count;

    -- 6. Delete user wallets (except OKDOI Head and ZM)
    RAISE NOTICE '';
    RAISE NOTICE '6. Cleaning user wallets...';

    DELETE FROM user_wallets WHERE user_id NOT IN (okdoi_head_id, company_zm_id);
    GET DIAGNOSTICS cleanup_count = ROW_COUNT;
    RAISE NOTICE '   ✓ User wallets: % deleted', cleanup_count;

    -- 7. Finally, delete users (except OKDOI Head and ZM)
    RAISE NOTICE '';
    RAISE NOTICE '7. Cleaning users table...';

    DELETE FROM users WHERE id NOT IN (okdoi_head_id, company_zm_id);
    GET DIAGNOSTICS cleanup_count = ROW_COUNT;
    RAISE NOTICE '   ✓ Users: % deleted', cleanup_count;

    -- 8. Reset present pools to zero
    RAISE NOTICE '';
    RAISE NOTICE '8. Resetting present pools...';

    UPDATE present_pools SET
        total_allocated = 0,
        total_distributed = 0,
        available_balance = 0,
        last_updated = NOW();
    GET DIAGNOSTICS cleanup_count = ROW_COUNT;
    RAISE NOTICE '   ✓ Present pools reset: % pools', cleanup_count;

    -- 9. Reset OKDOI Head and ZM wallet balances to initial state
    RAISE NOTICE '';
    RAISE NOTICE '9. Resetting system account balances...';

    UPDATE user_wallets SET balance = 0.00, updated_at = NOW()
    WHERE user_id IN (okdoi_head_id, company_zm_id);
    GET DIAGNOSTICS cleanup_count = ROW_COUNT;
    RAISE NOTICE '   ✓ System wallets reset: % wallets', cleanup_count;

    RAISE NOTICE '';
    RAISE NOTICE '🎉 ===== CLEANUP COMPLETED SUCCESSFULLY! =====';
    RAISE NOTICE '';
    RAISE NOTICE '✅ PRESERVED SYSTEM DATA:';
    RAISE NOTICE '   • admin_settings, boost_packages, categories';
    RAISE NOTICE '   • cities, commission_structure, districts';
    RAISE NOTICE '   • kyc_document_types, shop_categories';
    RAISE NOTICE '   • shop_subcategories, subcategories';
    RAISE NOTICE '   • subscription_packages';
    RAISE NOTICE '';
    RAISE NOTICE '✅ PRESERVED SYSTEM ACCOUNTS:';
    RAISE NOTICE '   • OKDOI Head: <EMAIL>';
    RAISE NOTICE '   • Company ZM: <EMAIL>';
    RAISE NOTICE '';
    RAISE NOTICE '🧹 CLEANED USER DATA:';
    RAISE NOTICE '   • All user accounts (except system accounts)';
    RAISE NOTICE '   • All ads, transactions, shops, orders';
    RAISE NOTICE '   • All commissions, referrals, KYC data';
    RAISE NOTICE '   • All wallets and financial records';
    RAISE NOTICE '';
    RAISE NOTICE '🚀 Database is now clean and ready for fresh start!';

END $$;

COMMIT;
