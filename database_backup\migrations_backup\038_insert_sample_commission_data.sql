-- Insert sample commission data for testing
-- This migration adds sample commission transactions for testing the commission breakdown functionality

-- First, let's get some user IDs to work with
DO $$
DECLARE
    user_record RECORD;
    sample_user_id UUID;
    transaction_counter INTEGER := 1;
BEGIN
    -- Get the first few users from the database
    FOR user_record IN 
        SELECT id, user_type FROM users 
        WHERE user_type IS NOT NULL 
        LIMIT 3
    LOOP
        sample_user_id := user_record.id;
        
        -- Insert base commission types for all users
        INSERT INTO commission_transactions (
            transaction_id,
            user_id,
            beneficiary_id,
            commission_type,
            commission_level,
            package_value,
            commission_rate,
            commission_amount,
            currency,
            status,
            processed_at,
            metadata,
            created_at,
            updated_at
        ) VALUES 
        -- Direct Commission
        (
            'TXN_' || EXTRACT(EPOCH FROM NOW())::bigint || '_' || transaction_counter,
            sample_user_id,
            sample_user_id,
            'direct_commission',
            1,
            5000.00,
            0.10,
            500.00,
            'LKR',
            'processed',
            NOW(),
            '{"sample_data": true, "description": "Sample direct commission"}',
            NOW(),
            NOW()
        ),
        -- Level Commission
        (
            'TXN_' || EXTRACT(EPOCH FROM NOW())::bigint || '_' || (transaction_counter + 1),
            sample_user_id,
            sample_user_id,
            'level_commission',
            2,
            5000.00,
            0.02,
            100.00,
            'LKR',
            'processed',
            NOW(),
            '{"sample_data": true, "description": "Sample level commission"}',
            NOW(),
            NOW()
        ),
        -- Voucher Commission
        (
            'TXN_' || EXTRACT(EPOCH FROM NOW())::bigint || '_' || (transaction_counter + 2),
            sample_user_id,
            sample_user_id,
            'voucher',
            1,
            5000.00,
            0.01,
            50.00,
            'LKR',
            'processed',
            NOW(),
            '{"sample_data": true, "description": "Sample voucher commission"}',
            NOW(),
            NOW()
        ),
        -- Festival Bonus
        (
            'TXN_' || EXTRACT(EPOCH FROM NOW())::bigint || '_' || (transaction_counter + 3),
            sample_user_id,
            sample_user_id,
            'festival_bonus',
            1,
            5000.00,
            0.01,
            50.00,
            'LKR',
            'processed',
            NOW(),
            '{"sample_data": true, "description": "Sample festival bonus"}',
            NOW(),
            NOW()
        ),
        -- Saving Commission
        (
            'TXN_' || EXTRACT(EPOCH FROM NOW())::bigint || '_' || (transaction_counter + 4),
            sample_user_id,
            sample_user_id,
            'saving',
            1,
            5000.00,
            0.01,
            50.00,
            'LKR',
            'processed',
            NOW(),
            '{"sample_data": true, "description": "Sample saving commission"}',
            NOW(),
            NOW()
        ),
        -- Gift Center Commission
        (
            'TXN_' || EXTRACT(EPOCH FROM NOW())::bigint || '_' || (transaction_counter + 5),
            sample_user_id,
            sample_user_id,
            'gift_center',
            1,
            5000.00,
            0.004,
            20.00,
            'LKR',
            'processed',
            NOW(),
            '{"sample_data": true, "description": "Sample gift center commission"}',
            NOW(),
            NOW()
        ),
        -- Entertainment Commission
        (
            'TXN_' || EXTRACT(EPOCH FROM NOW())::bigint || '_' || (transaction_counter + 6),
            sample_user_id,
            sample_user_id,
            'entertainment',
            1,
            5000.00,
            0.002,
            10.00,
            'LKR',
            'processed',
            NOW(),
            '{"sample_data": true, "description": "Sample entertainment commission"}',
            NOW(),
            NOW()
        ),
        -- Medical Commission
        (
            'TXN_' || EXTRACT(EPOCH FROM NOW())::bigint || '_' || (transaction_counter + 7),
            sample_user_id,
            sample_user_id,
            'medical',
            1,
            5000.00,
            0.001,
            5.00,
            'LKR',
            'processed',
            NOW(),
            '{"sample_data": true, "description": "Sample medical commission"}',
            NOW(),
            NOW()
        ),
        -- Education Commission
        (
            'TXN_' || EXTRACT(EPOCH FROM NOW())::bigint || '_' || (transaction_counter + 8),
            sample_user_id,
            sample_user_id,
            'education',
            1,
            5000.00,
            0.001,
            5.00,
            'LKR',
            'processed',
            NOW(),
            '{"sample_data": true, "description": "Sample education commission"}',
            NOW(),
            NOW()
        ),
        -- Credit Commission
        (
            'TXN_' || EXTRACT(EPOCH FROM NOW())::bigint || '_' || (transaction_counter + 9),
            sample_user_id,
            sample_user_id,
            'credit',
            1,
            5000.00,
            0.001,
            5.00,
            'LKR',
            'processed',
            NOW(),
            '{"sample_data": true, "description": "Sample credit commission"}',
            NOW(),
            NOW()
        );

        -- Add role-specific commissions
        IF user_record.user_type = 'zonal_manager' THEN
            INSERT INTO commission_transactions (
                transaction_id,
                user_id,
                beneficiary_id,
                commission_type,
                commission_level,
                package_value,
                commission_rate,
                commission_amount,
                currency,
                status,
                processed_at,
                metadata,
                created_at,
                updated_at
            ) VALUES 
            -- ZM Bonus
            (
                'TXN_' || EXTRACT(EPOCH FROM NOW())::bigint || '_' || (transaction_counter + 10),
                sample_user_id,
                sample_user_id,
                'zm_bonus',
                1,
                5000.00,
                0.05,
                250.00,
                'LKR',
                'processed',
                NOW(),
                '{"sample_data": true, "description": "Sample ZM bonus"}',
                NOW(),
                NOW()
            ),
            -- ZM Petral Allowance
            (
                'TXN_' || EXTRACT(EPOCH FROM NOW())::bigint || '_' || (transaction_counter + 11),
                sample_user_id,
                sample_user_id,
                'zm_petral_allowance',
                1,
                5000.00,
                0.005,
                25.00,
                'LKR',
                'processed',
                NOW(),
                '{"sample_data": true, "description": "Sample ZM petral allowance"}',
                NOW(),
                NOW()
            ),
            -- ZM Leasing Facility
            (
                'TXN_' || EXTRACT(EPOCH FROM NOW())::bigint || '_' || (transaction_counter + 12),
                sample_user_id,
                sample_user_id,
                'zm_leasing_facility',
                1,
                5000.00,
                0.01,
                50.00,
                'LKR',
                'processed',
                NOW(),
                '{"sample_data": true, "description": "Sample ZM leasing facility"}',
                NOW(),
                NOW()
            ),
            -- ZM Phone Bill
            (
                'TXN_' || EXTRACT(EPOCH FROM NOW())::bigint || '_' || (transaction_counter + 13),
                sample_user_id,
                sample_user_id,
                'zm_phone_bill',
                1,
                5000.00,
                0.001,
                5.00,
                'LKR',
                'processed',
                NOW(),
                '{"sample_data": true, "description": "Sample ZM phone bill"}',
                NOW(),
                NOW()
            );
        END IF;

        IF user_record.user_type = 'rsm' THEN
            INSERT INTO commission_transactions (
                transaction_id,
                user_id,
                beneficiary_id,
                commission_type,
                commission_level,
                package_value,
                commission_rate,
                commission_amount,
                currency,
                status,
                processed_at,
                metadata,
                created_at,
                updated_at
            ) VALUES 
            -- RSM Bonus
            (
                'TXN_' || EXTRACT(EPOCH FROM NOW())::bigint || '_' || (transaction_counter + 14),
                sample_user_id,
                sample_user_id,
                'rsm_bonus',
                1,
                5000.00,
                0.05,
                250.00,
                'LKR',
                'processed',
                NOW(),
                '{"sample_data": true, "description": "Sample RSM bonus"}',
                NOW(),
                NOW()
            ),
            -- RSM Petral Allowance
            (
                'TXN_' || EXTRACT(EPOCH FROM NOW())::bigint || '_' || (transaction_counter + 15),
                sample_user_id,
                sample_user_id,
                'rsm_petral_allowance',
                1,
                5000.00,
                0.005,
                25.00,
                'LKR',
                'processed',
                NOW(),
                '{"sample_data": true, "description": "Sample RSM petral allowance"}',
                NOW(),
                NOW()
            ),
            -- RSM Leasing Facility
            (
                'TXN_' || EXTRACT(EPOCH FROM NOW())::bigint || '_' || (transaction_counter + 16),
                sample_user_id,
                sample_user_id,
                'rsm_leasing_facility',
                1,
                5000.00,
                0.01,
                50.00,
                'LKR',
                'processed',
                NOW(),
                '{"sample_data": true, "description": "Sample RSM leasing facility"}',
                NOW(),
                NOW()
            ),
            -- RSM Phone Bill
            (
                'TXN_' || EXTRACT(EPOCH FROM NOW())::bigint || '_' || (transaction_counter + 17),
                sample_user_id,
                sample_user_id,
                'rsm_phone_bill',
                1,
                5000.00,
                0.001,
                5.00,
                'LKR',
                'processed',
                NOW(),
                '{"sample_data": true, "description": "Sample RSM phone bill"}',
                NOW(),
                NOW()
            );
        END IF;

        transaction_counter := transaction_counter + 20;
    END LOOP;
END $$;

-- Add a comment to document this migration
COMMENT ON TABLE commission_transactions IS 'Commission transactions table with sample data for testing commission breakdown functionality';
