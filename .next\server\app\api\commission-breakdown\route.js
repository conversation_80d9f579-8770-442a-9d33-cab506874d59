"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/commission-breakdown/route";
exports.ids = ["app/api/commission-breakdown/route"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

module.exports = require("https");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("stream");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fcommission-breakdown%2Froute&page=%2Fapi%2Fcommission-breakdown%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcommission-breakdown%2Froute.ts&appDir=C%3A%5CUsers%5Cdream%5CDesktop%5Cokdoi%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cdream%5CDesktop%5Cokdoi&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fcommission-breakdown%2Froute&page=%2Fapi%2Fcommission-breakdown%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcommission-breakdown%2Froute.ts&appDir=C%3A%5CUsers%5Cdream%5CDesktop%5Cokdoi%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cdream%5CDesktop%5Cokdoi&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_dream_Desktop_okdoi_src_app_api_commission_breakdown_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/commission-breakdown/route.ts */ \"(rsc)/./src/app/api/commission-breakdown/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/commission-breakdown/route\",\n        pathname: \"/api/commission-breakdown\",\n        filename: \"route\",\n        bundlePath: \"app/api/commission-breakdown/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\api\\\\commission-breakdown\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_dream_Desktop_okdoi_src_app_api_commission_breakdown_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/commission-breakdown/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fcommission-breakdown%2Froute&page=%2Fapi%2Fcommission-breakdown%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcommission-breakdown%2Froute.ts&appDir=C%3A%5CUsers%5Cdream%5CDesktop%5Cokdoi%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cdream%5CDesktop%5Cokdoi&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/commission-breakdown/route.ts":
/*!***************************************************!*\
  !*** ./src/app/api/commission-breakdown/route.ts ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _barrel_optimize_names_createClient_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=createClient!=!@supabase/supabase-js */ \"(rsc)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n/* harmony import */ var _supabase_ssr__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @supabase/ssr */ \"(rsc)/./node_modules/@supabase/ssr/dist/module/index.js\");\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n\n\n\n\n// Create admin Supabase client with service role key\nconst supabaseAdmin = (0,_barrel_optimize_names_createClient_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_3__.createClient)(\"https://vnmydqbwjjufnxngpnqo.supabase.co\", process.env.SUPABASE_SERVICE_ROLE_KEY, {\n    auth: {\n        autoRefreshToken: false,\n        persistSession: false\n    }\n});\nasync function GET(request) {\n    try {\n        // Create Supabase client for auth using SSR\n        const cookieStore = (0,next_headers__WEBPACK_IMPORTED_MODULE_2__.cookies)();\n        const supabaseAuth = (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_1__.createServerClient)(\"https://vnmydqbwjjufnxngpnqo.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InZubXlkcWJ3amp1Zm54bmdwbnFvIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTYyODkyNjgsImV4cCI6MjA3MTg2NTI2OH0.23oAdwSQ11jasIhrtZf71oeC6ehBsgTda1iCRU8myCo\", {\n            cookies: {\n                get (name) {\n                    return cookieStore.get(name)?.value;\n                }\n            }\n        });\n        // Get the current user\n        const { data: { user }, error: authError } = await supabaseAuth.auth.getUser();\n        const { searchParams } = new URL(request.url);\n        const userId = searchParams.get(\"userId\");\n        // If no userId provided and no authenticated user, return error\n        if (!userId && (!user || authError)) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Unauthorized or missing userId\"\n            }, {\n                status: 401\n            });\n        }\n        // Use provided userId or authenticated user's ID\n        const targetUserId = userId || user.id;\n        // Validate UUID format\n        const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;\n        if (!uuidRegex.test(targetUserId)) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Invalid user ID format\"\n            }, {\n                status: 400\n            });\n        }\n        // Get commission transactions for the user (excluding gift system commissions from user view)\n        const { data: commissionTransactions, error: commissionError } = await supabaseAdmin.from(\"commission_transactions\").select(\"commission_type, commission_amount, metadata\").eq(\"beneficiary_id\", targetUserId).eq(\"status\", \"processed\");\n        if (commissionError) {\n            throw new Error(`Failed to fetch commission transactions: ${commissionError.message}`);\n        }\n        // Initialize commission breakdown with all categories\n        const commissionBreakdown = {\n            directCommission: 0,\n            levelCommission: 0,\n            voucherCommission: 0,\n            festivalBonus: 0,\n            savingCommission: 0,\n            giftCenterCommission: 0,\n            entertainmentCommission: 0,\n            medicalCommission: 0,\n            educationCommission: 0,\n            creditCommission: 0,\n            // ZM specific\n            zmBonuses: 0,\n            petralAllowanceZM: 0,\n            leasingFacilityZM: 0,\n            phoneBillZM: 0,\n            // RSM specific\n            rsmBonuses: 0,\n            petralAllowanceRSM: 0,\n            leasingFacilityRSM: 0,\n            phoneBillRSM: 0,\n            // OKDOI Head specific\n            okdoiHeadCommission: 0,\n            leftoverCommission: 0,\n            totalCommissions: 0\n        };\n        // Aggregate commission amounts by type (filter out gift system commissions for user view)\n        if (commissionTransactions && commissionTransactions.length > 0) {\n            commissionTransactions.forEach((transaction)=>{\n                // Skip gift system commissions and leftover commissions for user wallets\n                const isGiftSystemCommission = transaction.metadata?.gift_system === true || [\n                    \"present_user\",\n                    \"annual_present_user\",\n                    \"present_leader\",\n                    \"annual_present_leader\"\n                ].includes(transaction.commission_type);\n                const isLeftoverCommission = transaction.commission_type === \"leftover_commission\";\n                if (isGiftSystemCommission || isLeftoverCommission) {\n                    return; // Skip gift system and leftover commissions in user wallet view\n                }\n                const amount = parseFloat(transaction.commission_amount) || 0;\n                switch(transaction.commission_type){\n                    case \"direct\":\n                    case \"direct_commission\":\n                        commissionBreakdown.directCommission += amount;\n                        break;\n                    case \"level_commission\":\n                    case \"sales\":\n                        commissionBreakdown.levelCommission += amount;\n                        break;\n                    case \"voucher\":\n                        commissionBreakdown.voucherCommission += amount;\n                        break;\n                    case \"festival_bonus\":\n                        commissionBreakdown.festivalBonus += amount;\n                        break;\n                    case \"saving\":\n                        commissionBreakdown.savingCommission += amount;\n                        break;\n                    case \"gift_center\":\n                        commissionBreakdown.giftCenterCommission += amount;\n                        break;\n                    case \"entertainment\":\n                        commissionBreakdown.entertainmentCommission += amount;\n                        break;\n                    case \"medical\":\n                        commissionBreakdown.medicalCommission += amount;\n                        break;\n                    case \"education\":\n                        commissionBreakdown.educationCommission += amount;\n                        break;\n                    case \"credit\":\n                        commissionBreakdown.creditCommission += amount;\n                        break;\n                    case \"okdoi_head_universal\":\n                    case \"okdoi_head_commission\":\n                    case \"okdoi_head\":\n                        commissionBreakdown.okdoiHeadCommission += amount;\n                        break;\n                    case \"leftover_commission\":\n                    case \"leftover_credit\":\n                    case \"leftover_education\":\n                    case \"leftover_entertainment\":\n                    case \"leftover_festival_bonus\":\n                    case \"leftover_gift_center\":\n                    case \"leftover_medical\":\n                    case \"leftover_sales\":\n                    case \"leftover_saving\":\n                    case \"leftover_voucher\":\n                        commissionBreakdown.leftoverCommission += amount;\n                        break;\n                    // ZM specific commissions\n                    case \"zm_bonus\":\n                        commissionBreakdown.zmBonuses += amount;\n                        break;\n                    case \"zm_petral_allowance\":\n                        commissionBreakdown.petralAllowanceZM += amount;\n                        break;\n                    case \"zm_leasing_facility\":\n                        commissionBreakdown.leasingFacilityZM += amount;\n                        break;\n                    case \"zm_phone_bill\":\n                        commissionBreakdown.phoneBillZM += amount;\n                        break;\n                    // RSM specific commissions\n                    case \"rsm_bonus\":\n                        commissionBreakdown.rsmBonuses += amount;\n                        break;\n                    case \"rsm_petral_allowance\":\n                        commissionBreakdown.petralAllowanceRSM += amount;\n                        break;\n                    case \"rsm_leasing_facility\":\n                        commissionBreakdown.leasingFacilityRSM += amount;\n                        break;\n                    case \"rsm_phone_bill\":\n                        commissionBreakdown.phoneBillRSM += amount;\n                        break;\n                    // Handle unified structure commissions\n                    case \"unified_structure\":\n                        // These are typically level commissions\n                        commissionBreakdown.levelCommission += amount;\n                        break;\n                    case \"unified_structure_unallocated\":\n                        // These go to OKDOI Head\n                        commissionBreakdown.okdoiHeadCommission += amount;\n                        break;\n                    default:\n                        // Log unknown commission types for debugging\n                        console.warn(`Unknown commission type: ${transaction.commission_type}`);\n                        break;\n                }\n            });\n            // Calculate total commissions\n            commissionBreakdown.totalCommissions = Object.values(commissionBreakdown).filter((value, index, array)=>index < array.length - 1) // Exclude totalCommissions itself\n            .reduce((sum, value)=>sum + (typeof value === \"number\" ? value : 0), 0);\n        }\n        // For admin requests, also include gift system commissions separately\n        const url = new URL(request.url);\n        const includeGiftSystem = url.searchParams.get(\"includeGiftSystem\") === \"true\";\n        if (includeGiftSystem) {\n            // Get gift system commissions separately for admin view\n            const { data: giftSystemTransactions, error: giftError } = await supabaseAdmin.from(\"commission_transactions\").select(\"commission_type, commission_amount\").eq(\"beneficiary_id\", targetUserId).eq(\"status\", \"processed\").in(\"commission_type\", [\n                \"present_user\",\n                \"annual_present_user\",\n                \"present_leader\",\n                \"annual_present_leader\"\n            ]);\n            if (!giftError && giftSystemTransactions) {\n                const giftSystemBreakdown = {\n                    presentUser: 0,\n                    annualPresentUser: 0,\n                    presentLeader: 0,\n                    annualPresentLeader: 0,\n                    totalGiftSystem: 0\n                };\n                giftSystemTransactions.forEach((transaction)=>{\n                    const amount = parseFloat(transaction.commission_amount) || 0;\n                    switch(transaction.commission_type){\n                        case \"present_user\":\n                            giftSystemBreakdown.presentUser += amount;\n                            break;\n                        case \"annual_present_user\":\n                            giftSystemBreakdown.annualPresentUser += amount;\n                            break;\n                        case \"present_leader\":\n                            giftSystemBreakdown.presentLeader += amount;\n                            break;\n                        case \"annual_present_leader\":\n                            giftSystemBreakdown.annualPresentLeader += amount;\n                            break;\n                    }\n                    giftSystemBreakdown.totalGiftSystem += amount;\n                });\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    success: true,\n                    data: {\n                        ...commissionBreakdown,\n                        giftSystemCommissions: giftSystemBreakdown\n                    }\n                });\n            }\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: commissionBreakdown\n        });\n    } catch (error) {\n        console.error(\"GET /api/commission-breakdown error:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: error instanceof Error ? error.message : \"Failed to fetch commission breakdown\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/commission-breakdown/route.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/whatwg-url","vendor-chunks/webidl-conversions","vendor-chunks/cookie"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fcommission-breakdown%2Froute&page=%2Fapi%2Fcommission-breakdown%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcommission-breakdown%2Froute.ts&appDir=C%3A%5CUsers%5Cdream%5CDesktop%5Cokdoi%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cdream%5CDesktop%5Cokdoi&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();