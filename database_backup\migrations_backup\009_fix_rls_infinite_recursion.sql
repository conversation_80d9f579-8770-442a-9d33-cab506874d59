-- Fix infinite recursion in RLS policies by using SECURITY DEFINER function
-- This addresses the issue where RLS policies that query the same table cause infinite recursion

-- Drop existing problematic policies
DROP POLICY IF EXISTS "Users can view own profile or admins can view all" ON users;
DROP POLICY IF EXISTS "Users can update own profile or admins can update any" ON users;

-- Drop and recreate the is_admin function with proper SECURITY DEFINER
-- This function runs with elevated privileges and bypasses RLS
DROP FUNCTION IF EXISTS is_admin(uuid);

CREATE OR REPLACE FUNCTION is_admin(user_id uuid DEFAULT auth.uid())
RETURNS boolean AS
$$
  SELECT COALESCE(
    (SELECT role = 'admin' OR is_super_admin = true
     FROM users
     WHERE id = user_id),
    false
  )
$$ STABLE LANGUAGE sql SECURITY DEFINER;

-- Create simple RLS policies using the security definer function
CREATE POLICY "Users can view own profile or admins can view all" ON users
  FOR SELECT
  USING (
    auth.uid() = id OR is_admin()
  );

CREATE POLICY "Users can update own profile or admins can update any" ON users
  FOR UPDATE
  USING (
    auth.uid() = id OR is_admin()
  );

CREATE POLICY "Admins can insert users" ON users
  FOR INSERT
  WITH CHECK (is_admin());

CREATE POLICY "Admins can delete users" ON users
  FOR DELETE
  USING (is_admin());
