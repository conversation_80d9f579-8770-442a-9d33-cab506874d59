import { useCallback } from 'react'
import html2canvas from 'html2canvas'
import jsPDF from 'jspdf'

export interface ExportOptions {
  filename?: string
  quality?: number
  scale?: number
  backgroundColor?: string
  width?: number
  height?: number
}

export const useTreeExport = () => {
  const exportTree = useCallback(async (
    elementId: string,
    format: 'png' | 'pdf',
    options: ExportOptions = {}
  ) => {
    const {
      filename = `referral-tree-${new Date().toISOString().split('T')[0]}`,
      quality = 1,
      scale = 2,
      backgroundColor = '#ffffff',
      width,
      height
    } = options

    try {
      const element = document.getElementById(elementId)
      if (!element) {
        throw new Error(`Element with id "${elementId}" not found`)
      }

      // Temporarily expand the tree container to capture full content
      const originalStyle = {
        overflow: element.style.overflow,
        height: element.style.height,
        maxHeight: element.style.maxHeight
      }

      // Set styles for full capture
      element.style.overflow = 'visible'
      element.style.height = 'auto'
      element.style.maxHeight = 'none'

      // Wait for any animations to complete
      await new Promise(resolve => setTimeout(resolve, 500))

      // Capture the element
      const canvas = await html2canvas(element, {
        scale,
        useCORS: true,
        allowTaint: true,
        backgroundColor,
        width: width || element.scrollWidth,
        height: height || element.scrollHeight,
        scrollX: 0,
        scrollY: 0,
        windowWidth: element.scrollWidth,
        windowHeight: element.scrollHeight,
        onclone: (clonedDoc) => {
          // Ensure all styles are preserved in the clone
          const clonedElement = clonedDoc.getElementById(elementId)
          if (clonedElement) {
            clonedElement.style.transform = 'none'
            clonedElement.style.overflow = 'visible'
            clonedElement.style.height = 'auto'
            clonedElement.style.maxHeight = 'none'
          }
        }
      })

      // Restore original styles
      element.style.overflow = originalStyle.overflow
      element.style.height = originalStyle.height
      element.style.maxHeight = originalStyle.maxHeight

      if (format === 'png') {
        // Export as PNG
        const link = document.createElement('a')
        link.download = `${filename}.png`
        link.href = canvas.toDataURL('image/png', quality)
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
      } else if (format === 'pdf') {
        // Export as PDF
        const imgData = canvas.toDataURL('image/png', quality)
        const imgWidth = canvas.width
        const imgHeight = canvas.height

        // Calculate PDF dimensions (A4 landscape for better tree visibility)
        const pdfWidth = 297 // A4 landscape width in mm
        const pdfHeight = 210 // A4 landscape height in mm
        
        // Calculate scaling to fit the image in PDF
        const ratio = Math.min(pdfWidth / (imgWidth * 0.264583), pdfHeight / (imgHeight * 0.264583))
        const scaledWidth = imgWidth * 0.264583 * ratio
        const scaledHeight = imgHeight * 0.264583 * ratio

        // Center the image in the PDF
        const x = (pdfWidth - scaledWidth) / 2
        const y = (pdfHeight - scaledHeight) / 2

        const pdf = new jsPDF('landscape', 'mm', 'a4')
        
        // Add title
        pdf.setFontSize(16)
        pdf.text('OKDOI Referral Tree', pdfWidth / 2, 15, { align: 'center' })
        
        // Add date
        pdf.setFontSize(10)
        pdf.text(`Generated on: ${new Date().toLocaleDateString()}`, pdfWidth / 2, 25, { align: 'center' })

        // Add the tree image
        pdf.addImage(imgData, 'PNG', x, y + 10, scaledWidth, scaledHeight - 10)
        
        // Save the PDF
        pdf.save(`${filename}.pdf`)
      }

      return true
    } catch (error) {
      console.error('Export failed:', error)
      throw error
    }
  }, [])

  const exportTreeAsImage = useCallback(async (elementId: string, options?: ExportOptions) => {
    return exportTree(elementId, 'png', options)
  }, [exportTree])

  const exportTreeAsPDF = useCallback(async (elementId: string, options?: ExportOptions) => {
    return exportTree(elementId, 'pdf', options)
  }, [exportTree])

  return {
    exportTree,
    exportTreeAsImage,
    exportTreeAsPDF
  }
}
