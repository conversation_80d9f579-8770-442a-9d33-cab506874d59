-- Add admin-related columns to users table
ALTER TABLE users ADD COLUMN IF NOT EXISTS role VARCHAR(20) DEFAULT 'user';
ALTER TABLE users ADD COLUMN IF NOT EXISTS is_super_admin BOOLEAN DEFAULT FALSE;
ALTER TABLE users ADD COLUMN IF NOT EXISTS banned_until TIM<PERSON><PERSON>MP WITH TIME ZONE;

-- Create index for role column for better performance
CREATE INDEX IF NOT EXISTS idx_users_role ON users(role);
CREATE INDEX IF NOT EXISTS idx_users_is_super_admin ON users(is_super_admin);

-- Add constraint to ensure role is valid
ALTER TABLE users ADD CONSTRAINT check_user_role 
    CHECK (role IN ('user', 'admin', 'moderator'));
