**Commission Distribution Explained by referral ranks.**

**1.Normal user**  
Maximum 10 level network depth. Here we count level 10 with the user, 1st level is the user and 9 levels down the network. Total 10 Levels. 

The commission categories for users as below. The commission is distributed in the same percentage for each level. Including the user itself, except the Direct commission. So all of the other 9 commission types should also be credited to the user itself. 

Direct Commission  
Sales Commission  
Voucher  
Festival Bonus  
Saving  
Gift Center   
Entertainment   
Medical  
Education  
Credit

And Gift system commissions “present User” and "Annual Present user” commission types should get with only Direct sales (Direct referrals) within Level 10\.

**2\. RSM**  
RSM is mostly like a user. Only the benefit is extended commission categories beyond level 10\. So RSM gets unlimited network depth. Normal commission structure as a user. Additionally RSM get Special RSM commissions: RSM Bonus, Petrol Alounce, Leasing facility and Phone & Bill from the entire network.

And Gift system commissions “present Leader” and "Annual Present Leader” commission types get from the entire network. 

**3\. ZM**  
ZM is mostly like a RSM user. Extended commission categories beyond level 10\. So ZM gets unlimited network depth. Normal commission structure as a user/ Rsm. Additionally RSM gets Special ZM commissions: ZM Bonus, Petrol Alounce, Leasing facility and Phone & Bill from the entire network.

And Gift system commissions “present Leader” and "Annual Present Leader” commission types get from the entire network. 

**4\. OKDOI Head**  
Okdoi Head is Top of the network. Currently we have Okdoi head. I need you to remove the head and user account. Then create a new account using below credentials and assign it as OKDOI head.   
**Email: <EMAIL>**  
**Password: Company432OK**

And Okdoi head need a ZM. Create a ZM account using below credentials and assign it as ZM. And this ZM account links directly to the OKDOI Head.   
**Email: <EMAIL>**  
**Password: ZmOkdoi455ER**

And direct register users should always add under this ZM account. That means users registered directly on our app without referral a link should add to this ZM account. 

Please deep check and find direct registered users and add existing users under this newly created ZM account.

Every ZM account should be connected to the Okdoi head, that means registered using Okdoi head’s referral link. Otherwise that user cannot upgrade to ZM. 

Okdoi head should get a unique commission from any package subscription in the entire network. The commission rate is below. Add this Okdoi head commission management to the commission structure in the admin panel.

2000 package: 2.50%  
5000 package: 2%  
10000 package: 2%  
50000 package: 2%

Also there are no 10 levels to distribute commissions in any package subscriptions on the network, the rest of commissions should add to the Okdoi head. That means as you already know we distribute common commissions for 10 levels, and in some cases there is no full 10 levels to distribute, for example there is 3 levels out of 10, and the rest of  the 7 commissions total should add to the Okdoi head wallet under a unique wallet commission category named “Leftover Commissions”.

Please completely understand this commission distribution and make the changes to admin/commission-structure according to this.

\*\*Do not make any changes on Referral placement logic since it is working correctly.  
\*\*Create a seed script to seed this Okdoi head and Okdoi ZM.