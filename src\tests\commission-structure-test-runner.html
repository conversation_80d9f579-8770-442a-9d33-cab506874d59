<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Commission Structure Tests</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            border-radius: 8px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-controls {
            text-align: center;
            margin-bottom: 30px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 0 10px;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .loading {
            text-align: center;
            color: #666;
            font-style: italic;
        }
        .results {
            margin-top: 20px;
        }
        .summary {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .summary h3 {
            margin-top: 0;
            color: #495057;
        }
        .stat {
            display: inline-block;
            margin: 10px 20px 10px 0;
            font-weight: bold;
        }
        .passed { color: #28a745; }
        .failed { color: #dc3545; }
        .total { color: #6c757d; }
        .test-details {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            overflow: hidden;
        }
        .test-item {
            padding: 12px 16px;
            border-bottom: 1px solid #dee2e6;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .test-item:last-child {
            border-bottom: none;
        }
        .test-item.passed {
            background-color: #d4edda;
        }
        .test-item.failed {
            background-color: #f8d7da;
        }
        .test-name {
            font-weight: 500;
        }
        .test-status {
            font-weight: bold;
            font-size: 18px;
        }
        .test-error {
            color: #721c24;
            font-size: 14px;
            margin-top: 4px;
        }
        .console-output {
            background: #1e1e1e;
            color: #d4d4d4;
            padding: 20px;
            border-radius: 6px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            line-height: 1.4;
            max-height: 400px;
            overflow-y: auto;
            margin-top: 20px;
        }
        .api-status {
            background: #e9ecef;
            border-radius: 6px;
            padding: 15px;
            margin-bottom: 20px;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-online { background-color: #28a745; }
        .status-offline { background-color: #dc3545; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Commission Structure Comprehensive Tests</h1>
        
        <div class="api-status">
            <h3>API Status Check</h3>
            <div id="api-status">
                <span class="status-indicator status-offline"></span>
                Checking API availability...
            </div>
        </div>
        
        <div class="test-controls">
            <button id="run-tests" onclick="runTests()">Run All Tests</button>
            <button id="clear-results" onclick="clearResults()">Clear Results</button>
        </div>
        
        <div id="loading" class="loading" style="display: none;">
            Running tests... Please wait.
        </div>
        
        <div id="results" class="results" style="display: none;">
            <div class="summary">
                <h3>📊 Test Summary</h3>
                <div class="stat total">Total: <span id="total-tests">0</span></div>
                <div class="stat passed">Passed: <span id="passed-tests">0</span></div>
                <div class="stat failed">Failed: <span id="failed-tests">0</span></div>
                <div class="stat">Success Rate: <span id="success-rate">0%</span></div>
            </div>
            
            <div class="test-details" id="test-details">
                <!-- Test results will be populated here -->
            </div>
        </div>
        
        <div id="console-output" class="console-output" style="display: none;">
            <!-- Console output will be shown here -->
        </div>
    </div>

    <script>
        let originalConsoleLog = console.log;
        let consoleOutput = [];
        
        // Override console.log to capture output
        console.log = function(...args) {
            consoleOutput.push(args.join(' '));
            originalConsoleLog.apply(console, args);
            updateConsoleDisplay();
        };
        
        function updateConsoleDisplay() {
            const consoleDiv = document.getElementById('console-output');
            consoleDiv.innerHTML = consoleOutput.join('\n');
            consoleDiv.style.display = consoleOutput.length > 0 ? 'block' : 'none';
            consoleDiv.scrollTop = consoleDiv.scrollHeight;
        }
        
        async function checkAPIStatus() {
            try {
                const response = await fetch('http://localhost:3000/api/admin/unified-commission-structure');
                const statusDiv = document.getElementById('api-status');
                
                if (response.ok) {
                    statusDiv.innerHTML = '<span class="status-indicator status-online"></span>API is online and responding';
                    return true;
                } else {
                    statusDiv.innerHTML = '<span class="status-indicator status-offline"></span>API responded with error: ' + response.status;
                    return false;
                }
            } catch (error) {
                const statusDiv = document.getElementById('api-status');
                statusDiv.innerHTML = '<span class="status-indicator status-offline"></span>API is offline or unreachable: ' + error.message;
                return false;
            }
        }
        
        async function runTests() {
            const runButton = document.getElementById('run-tests');
            const loading = document.getElementById('loading');
            const results = document.getElementById('results');
            
            // Clear previous results
            consoleOutput = [];
            updateConsoleDisplay();
            
            // Check API status first
            const apiOnline = await checkAPIStatus();
            if (!apiOnline) {
                alert('API is not available. Please make sure the development server is running on http://localhost:3000');
                return;
            }
            
            runButton.disabled = true;
            loading.style.display = 'block';
            results.style.display = 'none';
            
            try {
                // Run the tests (inline implementation)
                await runCommissionStructureTests();
                
                // Update UI with results
                updateResultsDisplay();
                results.style.display = 'block';
                
            } catch (error) {
                console.log('❌ Test execution failed: ' + error.message);
                alert('Test execution failed: ' + error.message);
            } finally {
                runButton.disabled = false;
                loading.style.display = 'none';
            }
        }
        
        function clearResults() {
            document.getElementById('results').style.display = 'none';
            consoleOutput = [];
            updateConsoleDisplay();
        }
        
        function updateResultsDisplay() {
            if (!window.testResults) return;
            
            const results = window.testResults;
            
            // Update summary
            document.getElementById('total-tests').textContent = results.total;
            document.getElementById('passed-tests').textContent = results.passed;
            document.getElementById('failed-tests').textContent = results.failed;
            document.getElementById('success-rate').textContent = 
                ((results.passed / results.total) * 100).toFixed(1) + '%';
            
            // Update test details
            const detailsDiv = document.getElementById('test-details');
            detailsDiv.innerHTML = '';
            
            results.details.forEach(test => {
                const testDiv = document.createElement('div');
                testDiv.className = `test-item ${test.passed ? 'passed' : 'failed'}`;
                
                const nameDiv = document.createElement('div');
                nameDiv.className = 'test-name';
                nameDiv.textContent = test.testName;
                if (test.details && !test.passed) {
                    const errorDiv = document.createElement('div');
                    errorDiv.className = 'test-error';
                    errorDiv.textContent = test.details;
                    nameDiv.appendChild(errorDiv);
                }
                
                const statusDiv = document.createElement('div');
                statusDiv.className = 'test-status';
                statusDiv.textContent = test.passed ? '✅' : '❌';
                
                testDiv.appendChild(nameDiv);
                testDiv.appendChild(statusDiv);
                detailsDiv.appendChild(testDiv);
            });
        }
        
        // Initialize on page load
        window.addEventListener('load', function() {
            checkAPIStatus();
        });
        
        // Inline test implementation (simplified version)
        async function runCommissionStructureTests() {
            window.testResults = { passed: 0, failed: 0, total: 0, details: [] };
            
            console.log('🚀 Starting Commission Structure Comprehensive Tests...');
            console.log('=' .repeat(60));
            
            await testAPIEndpoints();
            await testCommissionCalculations();
            
            console.log('\n' + '='.repeat(60));
            console.log('📊 TEST SUMMARY');
            console.log('='.repeat(60));
            console.log(`Total Tests: ${window.testResults.total}`);
            console.log(`Passed: ${window.testResults.passed} ✅`);
            console.log(`Failed: ${window.testResults.failed} ❌`);
            console.log(`Success Rate: ${((window.testResults.passed / window.testResults.total) * 100).toFixed(1)}%`);
        }
        
        function logTest(testName, passed, details = '') {
            window.testResults.total++;
            if (passed) {
                window.testResults.passed++;
                console.log(`✅ ${testName}`);
            } else {
                window.testResults.failed++;
                console.log(`❌ ${testName} - ${details}`);
            }
            window.testResults.details.push({ testName, passed, details });
        }
        
        async function testAPIEndpoints() {
            console.log('\n🧪 Testing API Endpoints...');
            
            try {
                const response = await fetch('http://localhost:3000/api/admin/unified-commission-structure');
                const data = await response.json();
                
                logTest('Unified Commission Structure API responds', response.ok && data.success);
                
                if (response.ok && data.success) {
                    const structures = data.data;
                    logTest('Returns correct number of structures (4)', structures.length === 4, `Expected 4, got ${structures.length}`);
                    
                    const packageValues = structures.map(s => s.package_value).sort((a, b) => a - b);
                    const expectedValues = [2000, 5000, 10000, 15000];
                    logTest('All expected package values present', 
                        JSON.stringify(packageValues) === JSON.stringify(expectedValues),
                        `Expected ${expectedValues}, got ${packageValues}`);
                }
                
                const packagesResponse = await fetch('http://localhost:3000/api/admin/available-packages');
                const packagesData = await packagesResponse.json();
                
                logTest('Available Packages API responds', packagesResponse.ok && packagesData.success);
                
            } catch (error) {
                logTest('API Endpoints Test', false, error.message);
            }
        }
        
        async function testCommissionCalculations() {
            console.log('\n🧮 Testing Commission Calculations...');
            
            try {
                const response = await fetch('http://localhost:3000/api/admin/unified-commission-structure');
                const data = await response.json();
                
                if (response.ok && data.success) {
                    const structures = data.data;
                    
                    for (const structure of structures) {
                        const rates = [
                            structure.direct_commission_rate,
                            structure.level_commission_rate,
                            structure.present_user_rate,
                            structure.present_leader_rate
                        ];
                        
                        const validRates = rates.every(rate => rate >= 0 && rate <= 1);
                        logTest(`Package ${structure.package_value} has valid commission rates`, validRates);
                    }
                }
            } catch (error) {
                logTest('Commission Calculations Test', false, error.message);
            }
        }
    </script>
</body>
</html>
