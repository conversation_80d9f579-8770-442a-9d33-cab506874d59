'use client'

import { motion } from 'framer-motion'
import { Users, Target, Award, Globe, Heart, Shield, Zap, TrendingUp } from 'lucide-react'
import Header from '@/components/layout/Header'
import Footer from '@/components/layout/Footer'
import { PremiumCard, FadeIn, SlideInUp, StaggerContainer, StaggerItem } from '@/components/ui/premium'

export default function AboutPage() {
  const stats = [
    { label: 'Active Users', value: '50,000+', icon: Users },
    { label: 'Ads Posted', value: '100,000+', icon: Target },
    { label: 'Successful Deals', value: '25,000+', icon: Award },
    { label: 'Cities Covered', value: '24', icon: Globe }
  ]

  const values = [
    {
      icon: Heart,
      title: 'Community First',
      description: 'We believe in building a trusted community where buyers and sellers can connect with confidence.'
    },
    {
      icon: Shield,
      title: 'Trust & Safety',
      description: 'Your security is our priority. We implement robust measures to ensure safe transactions.'
    },
    {
      icon: Zap,
      title: 'Innovation',
      description: 'We continuously evolve our platform with cutting-edge features to enhance your experience.'
    },
    {
      icon: TrendingUp,
      title: 'Growth',
      description: 'We help individuals and businesses grow by providing the best marketplace experience in Sri Lanka.'
    }
  ]

  const team = [
    {
      name: 'OKDOI Team',
      role: 'Founders & Developers',
      description: 'A passionate team dedicated to revolutionizing the classified ads experience in Sri Lanka.'
    }
  ]

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <main className="py-8 pt-28">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Hero Section */}
          <FadeIn>
            <div className="text-center mb-16">
              <motion.h1 
                className="text-4xl lg:text-6xl font-bold text-gray-900 mb-6"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6 }}
              >
                About <span className="text-primary-blue">OKDOI</span>
              </motion.h1>
              <motion.p 
                className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.2 }}
              >
                Sri Lanka's premier marketplace for buying and selling everything. 
                We connect communities, enable commerce, and create opportunities for everyone.
              </motion.p>
            </div>
          </FadeIn>

          {/* Stats Section */}
          <SlideInUp delay={0.4}>
            <PremiumCard variant="premium" className="mb-16">
              <div className="grid grid-cols-2 lg:grid-cols-4 gap-8">
                {stats.map((stat, index) => (
                  <StaggerItem key={stat.label} index={index}>
                    <div className="text-center">
                      <motion.div
                        className="inline-flex items-center justify-center w-16 h-16 bg-primary-blue/10 text-primary-blue mb-4 mx-auto"
                        whileHover={{ scale: 1.1, rotate: 5 }}
                        transition={{ duration: 0.2 }}
                      >
                        <stat.icon className="h-8 w-8" />
                      </motion.div>
                      <div className="text-3xl font-bold text-gray-900 mb-2">{stat.value}</div>
                      <div className="text-gray-600">{stat.label}</div>
                    </div>
                  </StaggerItem>
                ))}
              </div>
            </PremiumCard>
          </SlideInUp>

          {/* Mission Section */}
          <div className="grid lg:grid-cols-2 gap-12 mb-16">
            <FadeIn>
              <PremiumCard variant="glass" className="h-full">
                <h2 className="text-3xl font-bold text-gray-900 mb-6">Our Mission</h2>
                <p className="text-gray-600 text-lg leading-relaxed mb-6">
                  To create Sri Lanka's most trusted and user-friendly marketplace where anyone can 
                  buy, sell, and discover amazing deals. We're committed to empowering individuals 
                  and businesses by providing a platform that's safe, efficient, and accessible to all.
                </p>
                <p className="text-gray-600 text-lg leading-relaxed">
                  From classified ads to e-commerce shops, from referral opportunities to community 
                  building - OKDOI is more than just a marketplace. We're a platform that grows with you.
                </p>
              </PremiumCard>
            </FadeIn>

            <FadeIn delay={0.2}>
              <PremiumCard variant="glass" className="h-full">
                <h2 className="text-3xl font-bold text-gray-900 mb-6">Our Vision</h2>
                <p className="text-gray-600 text-lg leading-relaxed mb-6">
                  To become the go-to platform for all buying and selling needs in Sri Lanka, 
                  fostering economic growth and creating opportunities for everyone in our community.
                </p>
                <p className="text-gray-600 text-lg leading-relaxed">
                  We envision a future where every Sri Lankan has access to a thriving digital 
                  marketplace that connects them with opportunities, whether they're looking to 
                  buy their dream car, sell handmade crafts, or build a successful online business.
                </p>
              </PremiumCard>
            </FadeIn>
          </div>

          {/* Values Section */}
          <SlideInUp>
            <div className="mb-16">
              <div className="text-center mb-12">
                <h2 className="text-3xl font-bold text-gray-900 mb-4">Our Values</h2>
                <p className="text-xl text-gray-600">The principles that guide everything we do</p>
              </div>
              
              <StaggerContainer>
                <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
                  {values.map((value, index) => (
                    <StaggerItem key={value.title} index={index}>
                      <PremiumCard variant="glass" className="text-center h-full">
                        <motion.div
                          className="inline-flex items-center justify-center w-16 h-16 bg-primary-blue/10 text-primary-blue mb-6 mx-auto"
                          whileHover={{ scale: 1.1, rotate: 5 }}
                          transition={{ duration: 0.2 }}
                        >
                          <value.icon className="h-8 w-8" />
                        </motion.div>
                        <h3 className="text-xl font-bold text-gray-900 mb-4">{value.title}</h3>
                        <p className="text-gray-600 leading-relaxed">{value.description}</p>
                      </PremiumCard>
                    </StaggerItem>
                  ))}
                </div>
              </StaggerContainer>
            </div>
          </SlideInUp>

          {/* What We Offer Section */}
          <FadeIn>
            <PremiumCard variant="premium" className="mb-16">
              <div className="text-center mb-12">
                <h2 className="text-3xl font-bold text-gray-900 mb-4">What We Offer</h2>
                <p className="text-xl text-gray-600">Everything you need in one platform</p>
              </div>
              
              <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                <div className="text-center">
                  <div className="text-4xl mb-4">📱</div>
                  <h3 className="text-xl font-bold text-gray-900 mb-2">Classified Ads</h3>
                  <p className="text-gray-600">Post and browse classified ads across all categories</p>
                </div>
                <div className="text-center">
                  <div className="text-4xl mb-4">🏪</div>
                  <h3 className="text-xl font-bold text-gray-900 mb-2">E-Commerce Shops</h3>
                  <p className="text-gray-600">Create your own online shop and sell products</p>
                </div>
                <div className="text-center">
                  <div className="text-4xl mb-4">🤝</div>
                  <h3 className="text-xl font-bold text-gray-900 mb-2">Referral System</h3>
                  <p className="text-gray-600">Earn commissions by referring new users</p>
                </div>
                <div className="text-center">
                  <div className="text-4xl mb-4">💰</div>
                  <h3 className="text-xl font-bold text-gray-900 mb-2">Digital Wallet</h3>
                  <p className="text-gray-600">Secure payment system for all transactions</p>
                </div>
                <div className="text-center">
                  <div className="text-4xl mb-4">🚀</div>
                  <h3 className="text-xl font-bold text-gray-900 mb-2">Ad Boosting</h3>
                  <p className="text-gray-600">Promote your ads for better visibility</p>
                </div>
                <div className="text-center">
                  <div className="text-4xl mb-4">💬</div>
                  <h3 className="text-xl font-bold text-gray-900 mb-2">Chat System</h3>
                  <p className="text-gray-600">Connect directly with buyers and sellers</p>
                </div>
              </div>
            </PremiumCard>
          </FadeIn>

          {/* Contact CTA */}
          <FadeIn>
            <PremiumCard variant="gradient" className="text-center text-white">
              <h2 className="text-3xl font-bold mb-4">Ready to Join OKDOI?</h2>
              <p className="text-xl mb-8 opacity-90">
                Start buying, selling, and growing with Sri Lanka's premier marketplace
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <motion.a
                  href="/auth/signup"
                  className="bg-white text-primary-blue px-8 py-3 font-semibold hover:bg-gray-100 transition-colors"
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  Get Started Today
                </motion.a>
                <motion.a
                  href="/contact"
                  className="border-2 border-white text-white px-8 py-3 font-semibold hover:bg-white hover:text-primary-blue transition-colors"
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  Contact Us
                </motion.a>
              </div>
            </PremiumCard>
          </FadeIn>
        </div>
      </main>
      
      <Footer />
    </div>
  )
}
