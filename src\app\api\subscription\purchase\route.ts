import { NextRequest, NextResponse } from 'next/server'
import { supabase, supabaseAdmin } from '@/lib/supabase'
import { CommissionSystemService } from '@/lib/services/commissionSystem'
import { PresentAllocationService } from '@/lib/services/presentAllocationService'

export async function POST(request: NextRequest) {
  try {
    // Get the authorization header
    const authHeader = request.headers.get('authorization')
    if (!authHeader) {
      return NextResponse.json({ error: 'Authorization header required' }, { status: 401 })
    }

    // Verify the user token
    const token = authHeader.replace('Bearer ', '')
    const { data: { user }, error: authError } = await supabase.auth.getUser(token)

    if (authError || !user) {
      return NextResponse.json({ error: 'Invalid token' }, { status: 401 })
    }

    // Get request body
    const body = await request.json()
    const { packageId, walletId } = body

    if (!packageId || !walletId) {
      return NextResponse.json({ 
        error: 'Package ID and Wallet ID are required' 
      }, { status: 400 })
    }

    console.log(`Processing subscription purchase for user ${user.id}, package ${packageId}`)

    // Get package details using admin client
    const { data: package_data, error: packageError } = await supabaseAdmin
      .from('subscription_packages')
      .select('*')
      .eq('id', packageId)
      .eq('is_active', true)
      .single()

    if (packageError || !package_data) {
      return NextResponse.json({ 
        error: 'Subscription package not found or inactive' 
      }, { status: 404 })
    }

    // Check wallet balance using admin client
    const { data: wallet, error: walletError } = await supabaseAdmin
      .from('user_wallets')
      .select('balance')
      .eq('id', walletId)
      .eq('user_id', user.id)
      .single()

    if (walletError || !wallet) {
      return NextResponse.json({ 
        error: 'Wallet not found' 
      }, { status: 404 })
    }

    if (wallet.balance < package_data.price) {
      return NextResponse.json({ 
        error: 'Insufficient wallet balance' 
      }, { status: 400 })
    }

    // Calculate expiration date
    const expiresAt = new Date()
    expiresAt.setDate(expiresAt.getDate() + package_data.duration_days)

    // Create subscription using admin client
    const { data: subscription, error: subscriptionError } = await supabaseAdmin
      .from('user_subscriptions')
      .insert({
        user_id: user.id,
        package_id: packageId,
        expires_at: expiresAt.toISOString()
      })
      .select(`
        *,
        package:subscription_packages(*)
      `)
      .single()

    if (subscriptionError) {
      console.error('Failed to create subscription:', subscriptionError)
      return NextResponse.json({ 
        error: `Failed to create subscription: ${subscriptionError.message}` 
      }, { status: 500 })
    }

    // Deduct from wallet using admin client
    const { error: walletUpdateError } = await supabaseAdmin.rpc('update_wallet_balance', {
      p_wallet_id: walletId,
      p_amount: package_data.price,
      p_transaction_type: 'purchase',
      p_description: `Subscription purchase: ${package_data.name}`,
      p_reference_id: subscription.id,
      p_reference_type: 'subscription',
      p_metadata: {
        package_id: packageId,
        package_name: package_data.name,
        package_price: package_data.price,
        duration_days: package_data.duration_days
      }
    })

    if (walletUpdateError) {
      console.error('Failed to process payment:', walletUpdateError)
      
      // Rollback subscription creation
      await supabaseAdmin
        .from('user_subscriptions')
        .delete()
        .eq('id', subscription.id)

      return NextResponse.json({ 
        error: `Failed to process payment: ${walletUpdateError.message}` 
      }, { status: 500 })
    }

    console.log(`Subscription ${subscription.id} created successfully, processing commissions...`)

    // Process commission distribution (server-side with admin client)
    try {
      const commissionResult = await CommissionSystemService.distributeCommissions(
        user.id,
        subscription.id,
        package_data.price
      )
      console.log(`Commission distribution completed for subscription ${subscription.id}:`, commissionResult)
    } catch (commissionError) {
      console.error('Failed to distribute commissions for subscription:', commissionError)
      // Don't fail the subscription purchase, but log the error
      // Commissions can be processed later if needed
    }

    // Process present allocations (server-side with admin client)
    try {
      await PresentAllocationService.allocatePresentsFromPurchase(
        user.id,
        package_data.price,
        subscription.id,
        `SUB_${subscription.id}`
      )
      console.log(`Present allocation completed for subscription ${subscription.id}`)
    } catch (presentError) {
      console.error('Present allocation failed:', presentError)
      // Don't fail the subscription purchase if present allocation fails
    }

    return NextResponse.json({
      success: true,
      message: 'Subscription purchased successfully',
      data: subscription
    })

  } catch (error) {
    console.error('Error in subscription purchase API:', error)
    return NextResponse.json({
      error: error instanceof Error ? error.message : 'Internal server error'
    }, { status: 500 })
  }
}
