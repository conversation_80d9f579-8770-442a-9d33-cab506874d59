-- Revert Migration: Undo problematic changes from fix_database_issues.sql
-- This migration safely reverts changes that broke the application

-- =====================================================
-- 1. REVERT PROBLEMATIC RLS POLICIES
-- =====================================================

-- Drop potentially conflicting admin_settings policies
DROP POLICY IF EXISTS "Admin users can read admin settings" ON admin_settings;
DROP POLICY IF EXISTS "Admin users can insert admin settings" ON admin_settings;
DROP POLICY IF EXISTS "Admin users can update admin settings" ON admin_settings;
DROP POLICY IF EXISTS "Admin users can delete admin settings" ON admin_settings;

-- Drop potentially conflicting users policies
DROP POLICY IF EXISTS "Users can view own profile" ON users;
DROP POLICY IF EXISTS "Users can update own profile" ON users;
DROP POLICY IF EXISTS "Public user info viewable" ON users;
DROP POLICY IF EXISTS "Admins can view all users" ON users;
DROP POLICY IF EXISTS "Public profiles are viewable by everyone" ON users;

-- Drop potentially conflicting KYC policies
DROP POLICY IF EXISTS "Users can view own KYC submissions" ON kyc_submissions;
DROP POLICY IF EXISTS "Users can insert own KYC submissions" ON kyc_submissions;
DROP POLICY IF EXISTS "Users can update own pending KYC submissions" ON kyc_submissions;
DROP POLICY IF EXISTS "Admins can view all KYC submissions" ON kyc_submissions;
DROP POLICY IF EXISTS "Admins can update all KYC submissions" ON kyc_submissions;
DROP POLICY IF EXISTS "Users can view own KYC history" ON kyc_status_history;
DROP POLICY IF EXISTS "Admins can view all KYC history" ON kyc_status_history;
DROP POLICY IF EXISTS "System can insert KYC history" ON kyc_status_history;
DROP POLICY IF EXISTS "Everyone can read document types" ON kyc_document_types;

-- =====================================================
-- 2. RESTORE SAFE ADMIN SETTINGS POLICIES
-- =====================================================

-- Create safe admin settings policies that don't cause infinite recursion
CREATE POLICY "Enable read access for admin settings" ON admin_settings
    FOR SELECT USING (true);

CREATE POLICY "Enable insert for admin settings" ON admin_settings
    FOR INSERT WITH CHECK (true);

CREATE POLICY "Enable update for admin settings" ON admin_settings
    FOR UPDATE USING (true);

CREATE POLICY "Enable delete for admin settings" ON admin_settings
    FOR DELETE USING (true);

-- =====================================================
-- 3. RESTORE SAFE USERS POLICIES
-- =====================================================

-- Create safe users policies
CREATE POLICY "Enable read access for users" ON users
    FOR SELECT USING (true);

CREATE POLICY "Enable insert for users" ON users
    FOR INSERT WITH CHECK (true);

CREATE POLICY "Enable update for users" ON users
    FOR UPDATE USING (true);

-- =====================================================
-- 4. RESTORE SAFE KYC POLICIES (IF TABLES EXIST)
-- =====================================================

-- Only create KYC policies if tables exist
DO $$
BEGIN
    -- Check if kyc_submissions table exists
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'kyc_submissions') THEN
        -- Create safe KYC policies
        EXECUTE 'CREATE POLICY "Enable read access for kyc_submissions" ON kyc_submissions FOR SELECT USING (true)';
        EXECUTE 'CREATE POLICY "Enable insert for kyc_submissions" ON kyc_submissions FOR INSERT WITH CHECK (true)';
        EXECUTE 'CREATE POLICY "Enable update for kyc_submissions" ON kyc_submissions FOR UPDATE USING (true)';
    END IF;

    -- Check if kyc_status_history table exists
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'kyc_status_history') THEN
        EXECUTE 'CREATE POLICY "Enable read access for kyc_status_history" ON kyc_status_history FOR SELECT USING (true)';
        EXECUTE 'CREATE POLICY "Enable insert for kyc_status_history" ON kyc_status_history FOR INSERT WITH CHECK (true)';
    END IF;

    -- Check if kyc_document_types table exists
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'kyc_document_types') THEN
        EXECUTE 'CREATE POLICY "Enable read access for kyc_document_types" ON kyc_document_types FOR SELECT USING (true)';
    END IF;
END $$;

-- =====================================================
-- 5. REMOVE PROBLEMATIC TRIGGERS AND FUNCTIONS
-- =====================================================

-- Drop potentially problematic triggers
DROP TRIGGER IF EXISTS assign_referral_code_trigger ON users;
DROP TRIGGER IF EXISTS update_user_kyc_status_trigger ON kyc_submissions;

-- Drop potentially problematic functions
DROP FUNCTION IF EXISTS assign_referral_code();
DROP FUNCTION IF EXISTS update_user_kyc_status();
DROP FUNCTION IF EXISTS generate_referral_code();

-- =====================================================
-- 6. REMOVE PROBLEMATIC CONSTRAINTS
-- =====================================================

-- Remove constraints that might be causing issues
DO $$
BEGIN
    -- Remove check constraints that might be too restrictive
    IF EXISTS (SELECT 1 FROM information_schema.table_constraints WHERE constraint_name = 'check_user_role' AND table_name = 'users') THEN
        ALTER TABLE users DROP CONSTRAINT check_user_role;
    END IF;

    IF EXISTS (SELECT 1 FROM information_schema.table_constraints WHERE constraint_name = 'check_user_type' AND table_name = 'users') THEN
        ALTER TABLE users DROP CONSTRAINT check_user_type;
    END IF;

    IF EXISTS (SELECT 1 FROM information_schema.table_constraints WHERE constraint_name = 'check_kyc_status' AND table_name = 'users') THEN
        ALTER TABLE users DROP CONSTRAINT check_kyc_status;
    END IF;
END $$;

-- =====================================================
-- 7. ENSURE ADMIN SETTINGS TABLE IS PROPERLY CONFIGURED
-- =====================================================

-- Ensure admin_settings table exists with basic structure
CREATE TABLE IF NOT EXISTS admin_settings (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    key VARCHAR(100) NOT NULL UNIQUE,
    value JSONB NOT NULL,
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Ensure basic admin settings exist
INSERT INTO admin_settings (key, value, description) VALUES
('site_name', '"OKDOI"', 'Name of the website'),
('site_description', '"Premium Marketplace for Everything"', 'Description of the website'),
('admin_email', '"<EMAIL>"', 'Administrator email address'),
('allow_registration', 'true', 'Whether new user registration is allowed'),
('require_email_verification', 'false', 'Whether email verification is required for new users'),
('auto_approve_ads', 'false', 'Whether ads are automatically approved'),
('max_images_per_ad', '10', 'Maximum number of images per ad'),
('ad_expiry_days', '30', 'Number of days before ads expire'),
('enable_notifications', 'true', 'Whether notifications are enabled'),
('maintenance_mode', 'false', 'Whether the site is in maintenance mode'),
('enable_analytics', 'true', 'Whether analytics are enabled'),
('enable_referrals', 'true', 'Whether referral system is enabled'),
('free_ads_limit', '2', 'Number of free ads new users can post without a subscription')
ON CONFLICT (key) DO NOTHING;

-- =====================================================
-- 8. ENSURE USERS TABLE HAS BASIC REQUIRED COLUMNS
-- =====================================================

-- Add only essential columns without constraints
ALTER TABLE users ADD COLUMN IF NOT EXISTS role VARCHAR(20) DEFAULT 'user';
ALTER TABLE users ADD COLUMN IF NOT EXISTS is_super_admin BOOLEAN DEFAULT FALSE;
ALTER TABLE users ADD COLUMN IF NOT EXISTS referral_code VARCHAR(20);

-- Create basic indexes
CREATE INDEX IF NOT EXISTS idx_users_role ON users(role);
CREATE INDEX IF NOT EXISTS idx_users_referral_code ON users(referral_code);

-- =====================================================
-- 9. CLEAN UP DUPLICATE POLICIES
-- =====================================================

-- Function to clean up duplicate policies
CREATE OR REPLACE FUNCTION cleanup_duplicate_policies()
RETURNS void AS $$
DECLARE
    policy_record RECORD;
BEGIN
    -- Get all policies and drop duplicates
    FOR policy_record IN 
        SELECT schemaname, tablename, policyname
        FROM pg_policies 
        WHERE schemaname = 'public'
    LOOP
        BEGIN
            EXECUTE format('DROP POLICY IF EXISTS %I ON %I.%I', 
                policy_record.policyname, 
                policy_record.schemaname, 
                policy_record.tablename);
        EXCEPTION WHEN OTHERS THEN
            -- Ignore errors when dropping policies
            NULL;
        END;
    END LOOP;
END;
$$ LANGUAGE plpgsql;

-- Run cleanup
SELECT cleanup_duplicate_policies();

-- Drop the cleanup function
DROP FUNCTION cleanup_duplicate_policies();

-- =====================================================
-- 10. RECREATE MINIMAL WORKING POLICIES
-- =====================================================

-- Recreate minimal policies for admin_settings
CREATE POLICY "admin_settings_select_policy" ON admin_settings FOR SELECT USING (true);
CREATE POLICY "admin_settings_insert_policy" ON admin_settings FOR INSERT WITH CHECK (true);
CREATE POLICY "admin_settings_update_policy" ON admin_settings FOR UPDATE USING (true);
CREATE POLICY "admin_settings_delete_policy" ON admin_settings FOR DELETE USING (true);

-- Recreate minimal policies for users
CREATE POLICY "users_select_policy" ON users FOR SELECT USING (true);
CREATE POLICY "users_insert_policy" ON users FOR INSERT WITH CHECK (true);
CREATE POLICY "users_update_policy" ON users FOR UPDATE USING (true);

-- =====================================================
-- COMPLETION
-- =====================================================

COMMENT ON TABLE admin_settings IS 'Admin settings table - Reverted to safe configuration';
COMMENT ON TABLE users IS 'Users table - Reverted to safe configuration';
