-- Add total_views field to vendor_shops table
-- This will track the sum of all product views for the shop

-- Add total_views column to vendor_shops
ALTER TABLE vendor_shops 
ADD COLUMN total_views INTEGER DEFAULT 0;

-- Create function to calculate and update shop total views
CREATE OR REPLACE FUNCTION update_shop_total_views(shop_id_param UUID)
RETURNS INTEGER AS $$
DECLARE
    total_views_count INTEGER;
BEGIN
    -- Calculate total views from all products in the shop
    SELECT COALESCE(SUM(views), 0) INTO total_views_count
    FROM shop_products 
    WHERE shop_id = shop_id_param;
    
    -- Update the shop with the calculated total views
    UPDATE vendor_shops 
    SET 
        total_views = total_views_count,
        updated_at = NOW()
    WHERE id = shop_id_param;
    
    RETURN total_views_count;
END;
$$ LANGUAGE plpgsql;

-- Create trigger function to update shop views when product views change
CREATE OR REPLACE FUNCTION update_shop_views_on_product_change()
RETURNS TRIGGER AS $$
BEGIN
    -- Update shop total views for the affected shop
    PERFORM update_shop_total_views(COALESCE(NEW.shop_id, OLD.shop_id));
    
    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- Create trigger to automatically update shop views when product views change
DROP TRIGGER IF EXISTS update_shop_views_on_product_views_trigger ON shop_products;
CREATE TRIGGER update_shop_views_on_product_views_trigger
    AFTER UPDATE OF views ON shop_products
    FOR EACH ROW
    EXECUTE FUNCTION update_shop_views_on_product_change();

-- Initialize total_views for existing shops
UPDATE vendor_shops 
SET total_views = (
    SELECT COALESCE(SUM(views), 0) 
    FROM shop_products 
    WHERE shop_products.shop_id = vendor_shops.id
);

-- Add comment to document the field
COMMENT ON COLUMN vendor_shops.total_views 
IS 'Total views across all products in the shop';
