-- OKDOI Referral & Commission System Migration
-- Creates comprehensive multi-level referral and commission distribution system

-- =====================================================
-- 1. EXTEND USERS TABLE WITH REFERRAL HIERARCHY
-- =====================================================

-- Add referral hierarchy columns to users table
ALTER TABLE users ADD COLUMN IF NOT EXISTS user_type VARCHAR(20) DEFAULT 'user' CHECK (user_type IN ('okdoi_head', 'zonal_manager', 'rsm', 'user'));
ALTER TABLE users ADD COLUMN IF NOT EXISTS referral_code VARCHAR(20) UNIQUE;
ALTER TABLE users ADD COLUMN IF NOT EXISTS referred_by_id UUID REFERENCES users(id);
ALTER TABLE users ADD COLUMN IF NOT EXISTS referral_level INTEGER DEFAULT 0;
ALTER TABLE users ADD COLUMN IF NOT EXISTS referral_path TEXT; -- Stores the full path from OKDOI Head
ALTER TABLE users ADD COLUMN IF NOT EXISTS direct_referrals_count INTEGER DEFAULT 0;
ALTER TABLE users ADD COLUMN IF NOT EXISTS total_downline_count INTEGER DEFAULT 0;
ALTER TABLE users ADD COLUMN IF NOT EXISTS total_commission_earned DECIMAL(12,2) DEFAULT 0.00;
ALTER TABLE users ADD COLUMN IF NOT EXISTS is_referral_active BOOLEAN DEFAULT TRUE;

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_users_user_type ON users(user_type);
CREATE INDEX IF NOT EXISTS idx_users_referral_code ON users(referral_code);
CREATE INDEX IF NOT EXISTS idx_users_referred_by_id ON users(referred_by_id);
CREATE INDEX IF NOT EXISTS idx_users_referral_level ON users(referral_level);

-- =====================================================
-- 2. REFERRAL HIERARCHY TABLE
-- =====================================================

-- Table to track the complete referral hierarchy
CREATE TABLE referral_hierarchy (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    ancestor_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    level_difference INTEGER NOT NULL CHECK (level_difference > 0),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, ancestor_id)
);

CREATE INDEX idx_referral_hierarchy_user_id ON referral_hierarchy(user_id);
CREATE INDEX idx_referral_hierarchy_ancestor_id ON referral_hierarchy(ancestor_id);
CREATE INDEX idx_referral_hierarchy_level ON referral_hierarchy(level_difference);

-- =====================================================
-- 3. COMMISSION STRUCTURE CONFIGURATION
-- =====================================================

-- Table to store commission rates and structure
CREATE TABLE commission_structure (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    package_value DECIMAL(12,2) NOT NULL,
    commission_type VARCHAR(50) NOT NULL,
    level_1_rate DECIMAL(5,4) DEFAULT 0,
    level_2_rate DECIMAL(5,4) DEFAULT 0,
    level_3_rate DECIMAL(5,4) DEFAULT 0,
    level_4_rate DECIMAL(5,4) DEFAULT 0,
    level_5_rate DECIMAL(5,4) DEFAULT 0,
    level_6_rate DECIMAL(5,4) DEFAULT 0,
    level_7_rate DECIMAL(5,4) DEFAULT 0,
    level_8_rate DECIMAL(5,4) DEFAULT 0,
    level_9_rate DECIMAL(5,4) DEFAULT 0,
    level_10_rate DECIMAL(5,4) DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX idx_commission_structure_package_value ON commission_structure(package_value);
CREATE INDEX idx_commission_structure_type ON commission_structure(commission_type);

-- =====================================================
-- 4. COMMISSION TRANSACTIONS TABLE
-- =====================================================

-- Table to track all commission transactions
CREATE TABLE commission_transactions (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    transaction_id VARCHAR(50) UNIQUE NOT NULL,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    beneficiary_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    subscription_purchase_id UUID REFERENCES user_subscriptions(id),
    commission_type VARCHAR(50) NOT NULL,
    commission_level INTEGER NOT NULL CHECK (commission_level BETWEEN 1 AND 10),
    package_value DECIMAL(12,2) NOT NULL,
    commission_rate DECIMAL(5,4) NOT NULL,
    commission_amount DECIMAL(12,2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'LKR',
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'processed', 'failed', 'cancelled')),
    processed_at TIMESTAMP WITH TIME ZONE,
    wallet_transaction_id UUID REFERENCES wallet_transactions(id),
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX idx_commission_transactions_user_id ON commission_transactions(user_id);
CREATE INDEX idx_commission_transactions_beneficiary_id ON commission_transactions(beneficiary_id);
CREATE INDEX idx_commission_transactions_subscription_id ON commission_transactions(subscription_purchase_id);
CREATE INDEX idx_commission_transactions_status ON commission_transactions(status);
CREATE INDEX idx_commission_transactions_created_at ON commission_transactions(created_at);

-- =====================================================
-- 5. REFERRAL PLACEMENT TRACKING
-- =====================================================

-- Table to track referral placements and manage the 3-user limit
CREATE TABLE referral_placements (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    parent_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    child_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    position INTEGER NOT NULL CHECK (position BETWEEN 1 AND 3),
    placement_type VARCHAR(20) DEFAULT 'direct' CHECK (placement_type IN ('direct', 'spillover')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(parent_id, position),
    UNIQUE(child_id) -- Each user can only have one direct parent
);

CREATE INDEX idx_referral_placements_parent_id ON referral_placements(parent_id);
CREATE INDEX idx_referral_placements_child_id ON referral_placements(child_id);

-- =====================================================
-- 6. ZONAL MANAGER MANAGEMENT
-- =====================================================

-- Table to manage Zonal Managers and their territories
CREATE TABLE zonal_managers (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE UNIQUE,
    zone_name VARCHAR(100) NOT NULL,
    zone_description TEXT,
    assigned_districts JSONB DEFAULT '[]', -- Array of district IDs
    is_active BOOLEAN DEFAULT TRUE,
    created_by UUID REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX idx_zonal_managers_user_id ON zonal_managers(user_id);
CREATE INDEX idx_zonal_managers_is_active ON zonal_managers(is_active);

-- =====================================================
-- 7. RSM MANAGEMENT
-- =====================================================

-- Table to manage Regional Sales Managers
CREATE TABLE regional_sales_managers (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE UNIQUE,
    zonal_manager_id UUID REFERENCES zonal_managers(id),
    region_name VARCHAR(100),
    is_active BOOLEAN DEFAULT TRUE,
    upgraded_by UUID REFERENCES users(id),
    upgraded_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX idx_rsm_user_id ON regional_sales_managers(user_id);
CREATE INDEX idx_rsm_zonal_manager_id ON regional_sales_managers(zonal_manager_id);
CREATE INDEX idx_rsm_is_active ON regional_sales_managers(is_active);

-- =====================================================
-- 8. DATABASE FUNCTIONS
-- =====================================================

-- Function to generate unique referral codes
CREATE OR REPLACE FUNCTION generate_referral_code()
RETURNS VARCHAR(20) AS $$
DECLARE
    code VARCHAR(20);
    exists_check INTEGER;
BEGIN
    LOOP
        -- Generate 8-character alphanumeric code
        code := 'REF' || UPPER(SUBSTRING(MD5(RANDOM()::TEXT) FROM 1 FOR 8));

        -- Check if code already exists
        SELECT COUNT(*) INTO exists_check FROM users WHERE referral_code = code;

        -- Exit loop if code is unique
        EXIT WHEN exists_check = 0;
    END LOOP;

    RETURN code;
END;
$$ LANGUAGE plpgsql;

-- Function to find next available placement position
CREATE OR REPLACE FUNCTION find_next_placement_position(parent_user_id UUID)
RETURNS TABLE(parent_id UUID, position INTEGER) AS $$
DECLARE
    current_parent UUID := parent_user_id;
    pos INTEGER;
    queue UUID[];
    next_user UUID;
BEGIN
    -- Initialize queue with the original parent
    queue := ARRAY[current_parent];

    WHILE array_length(queue, 1) > 0 LOOP
        -- Get next user from queue
        next_user := queue[1];
        queue := queue[2:];

        -- Check for available positions (1, 2, 3)
        FOR pos IN 1..3 LOOP
            IF NOT EXISTS (
                SELECT 1 FROM referral_placements
                WHERE parent_id = next_user AND position = pos
            ) THEN
                -- Found available position
                RETURN QUERY SELECT next_user, pos;
                RETURN;
            END IF;
        END LOOP;

        -- Add children to queue for next level search
        queue := queue || ARRAY(
            SELECT child_id FROM referral_placements
            WHERE parent_id = next_user
            ORDER BY position
        );
    END LOOP;

    -- No position found (shouldn't happen in normal operation)
    RETURN;
END;
$$ LANGUAGE plpgsql;

-- Function to place user in referral hierarchy
CREATE OR REPLACE FUNCTION place_user_in_hierarchy(
    new_user_id UUID,
    referrer_id UUID
) RETURNS VOID AS $$
DECLARE
    placement_info RECORD;
    referrer_level INTEGER;
    new_level INTEGER;
    ancestor_record RECORD;
BEGIN
    -- Get referrer's level
    SELECT referral_level INTO referrer_level FROM users WHERE id = referrer_id;
    new_level := referrer_level + 1;

    -- Find placement position
    SELECT * INTO placement_info FROM find_next_placement_position(referrer_id);

    IF placement_info.parent_id IS NULL THEN
        RAISE EXCEPTION 'No placement position found for user %', new_user_id;
    END IF;

    -- Update user's referral information
    UPDATE users SET
        referred_by_id = placement_info.parent_id,
        referral_level = new_level,
        referral_path = (
            SELECT COALESCE(referral_path, '') || '/' || placement_info.parent_id::TEXT
            FROM users WHERE id = placement_info.parent_id
        ) || '/' || new_user_id::TEXT
    WHERE id = new_user_id;

    -- Create placement record
    INSERT INTO referral_placements (parent_id, child_id, position, placement_type)
    VALUES (
        placement_info.parent_id,
        new_user_id,
        placement_info.position,
        CASE WHEN placement_info.parent_id = referrer_id THEN 'direct' ELSE 'spillover' END
    );

    -- Create hierarchy records for all ancestors
    FOR ancestor_record IN
        SELECT id, referral_level FROM users
        WHERE id IN (
            SELECT unnest(string_to_array(trim(both '/' from (
                SELECT referral_path FROM users WHERE id = placement_info.parent_id
            )), '/'))::UUID[]
        )
        UNION
        SELECT placement_info.parent_id, (SELECT referral_level FROM users WHERE id = placement_info.parent_id)
    LOOP
        INSERT INTO referral_hierarchy (user_id, ancestor_id, level_difference)
        VALUES (new_user_id, ancestor_record.id, new_level - ancestor_record.referral_level);
    END LOOP;

    -- Update referral counts
    UPDATE users SET
        direct_referrals_count = direct_referrals_count + 1
    WHERE id = referrer_id;

    -- Update total downline counts for all ancestors
    UPDATE users SET
        total_downline_count = total_downline_count + 1
    WHERE id IN (
        SELECT ancestor_id FROM referral_hierarchy WHERE user_id = new_user_id
    );
END;
$$ LANGUAGE plpgsql;

-- Function to calculate and distribute commissions
CREATE OR REPLACE FUNCTION calculate_commission_distribution(
    purchaser_id UUID,
    package_id UUID,
    package_amount DECIMAL(12,2)
) RETURNS VOID AS $$
DECLARE
    commission_record RECORD;
    beneficiary_record RECORD;
    commission_amount DECIMAL(12,2);
    transaction_id_val VARCHAR(50);
    okdoi_head_id UUID;
    total_distributed DECIMAL(12,2) := 0;
    remaining_commission DECIMAL(12,2);
BEGIN
    -- Generate unique transaction ID
    transaction_id_val := 'COM-' || TO_CHAR(NOW(), 'YYYYMMDD') || '-' || UPPER(SUBSTRING(MD5(RANDOM()::TEXT) FROM 1 FOR 8));

    -- Get OKDOI Head user ID
    SELECT id INTO okdoi_head_id FROM users WHERE user_type = 'okdoi_head' LIMIT 1;

    -- Get commission structure for this package value
    FOR commission_record IN
        SELECT * FROM commission_structure
        WHERE package_value <= package_amount
        ORDER BY package_value DESC
        LIMIT 1
    LOOP
        -- Distribute commissions to 10 levels
        FOR i IN 1..10 LOOP
            -- Get beneficiary at this level
            SELECT u.* INTO beneficiary_record
            FROM users u
            JOIN referral_hierarchy rh ON rh.ancestor_id = u.id
            WHERE rh.user_id = purchaser_id AND rh.level_difference = i
            LIMIT 1;

            -- Calculate commission amount for this level
            commission_amount := CASE i
                WHEN 1 THEN package_amount * commission_record.level_1_rate
                WHEN 2 THEN package_amount * commission_record.level_2_rate
                WHEN 3 THEN package_amount * commission_record.level_3_rate
                WHEN 4 THEN package_amount * commission_record.level_4_rate
                WHEN 5 THEN package_amount * commission_record.level_5_rate
                WHEN 6 THEN package_amount * commission_record.level_6_rate
                WHEN 7 THEN package_amount * commission_record.level_7_rate
                WHEN 8 THEN package_amount * commission_record.level_8_rate
                WHEN 9 THEN package_amount * commission_record.level_9_rate
                WHEN 10 THEN package_amount * commission_record.level_10_rate
                ELSE 0
            END;

            IF commission_amount > 0 THEN
                IF beneficiary_record.id IS NOT NULL THEN
                    -- Create commission transaction for actual beneficiary
                    INSERT INTO commission_transactions (
                        transaction_id, user_id, beneficiary_id, subscription_purchase_id,
                        commission_type, commission_level, package_value, commission_rate,
                        commission_amount, status
                    ) VALUES (
                        transaction_id_val || '-L' || i, purchaser_id, beneficiary_record.id, package_id,
                        commission_record.commission_type, i, package_amount,
                        CASE i
                            WHEN 1 THEN commission_record.level_1_rate
                            WHEN 2 THEN commission_record.level_2_rate
                            WHEN 3 THEN commission_record.level_3_rate
                            WHEN 4 THEN commission_record.level_4_rate
                            WHEN 5 THEN commission_record.level_5_rate
                            WHEN 6 THEN commission_record.level_6_rate
                            WHEN 7 THEN commission_record.level_7_rate
                            WHEN 8 THEN commission_record.level_8_rate
                            WHEN 9 THEN commission_record.level_9_rate
                            WHEN 10 THEN commission_record.level_10_rate
                        END,
                        commission_amount, 'pending'
                    );

                    total_distributed := total_distributed + commission_amount;
                ELSE
                    -- No beneficiary at this level, will go to OKDOI Head
                    IF okdoi_head_id IS NOT NULL THEN
                        INSERT INTO commission_transactions (
                            transaction_id, user_id, beneficiary_id, subscription_purchase_id,
                            commission_type, commission_level, package_value, commission_rate,
                            commission_amount, status, metadata
                        ) VALUES (
                            transaction_id_val || '-L' || i || '-HEAD', purchaser_id, okdoi_head_id, package_id,
                            commission_record.commission_type || '_unallocated', i, package_amount,
                            CASE i
                                WHEN 1 THEN commission_record.level_1_rate
                                WHEN 2 THEN commission_record.level_2_rate
                                WHEN 3 THEN commission_record.level_3_rate
                                WHEN 4 THEN commission_record.level_4_rate
                                WHEN 5 THEN commission_record.level_5_rate
                                WHEN 6 THEN commission_record.level_6_rate
                                WHEN 7 THEN commission_record.level_7_rate
                                WHEN 8 THEN commission_record.level_8_rate
                                WHEN 9 THEN commission_record.level_9_rate
                                WHEN 10 THEN commission_record.level_10_rate
                            END,
                            commission_amount, 'pending',
                            '{"reason": "unallocated_level", "original_level": ' || i || '}'
                        );

                        total_distributed := total_distributed + commission_amount;
                    END IF;
                END IF;
            END IF;
        END LOOP;
    END LOOP;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- 9. INSERT INITIAL COMMISSION STRUCTURE DATA
-- =====================================================

-- Insert commission structure based on the provided spreadsheet
INSERT INTO commission_structure (package_value, commission_type, level_1_rate, level_2_rate, level_3_rate, level_4_rate, level_5_rate, level_6_rate, level_7_rate, level_8_rate, level_9_rate, level_10_rate) VALUES
-- Direct Commission rates
(2000, 'direct_commission', 0.10, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02),
(5000, 'direct_commission', 0.25, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02),
(10000, 'direct_commission', 0.25, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02),
(50000, 'direct_commission', 0.25, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02),

-- Level Commission (2% for all levels)
(2000, 'level_commission', 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02),
(5000, 'level_commission', 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02),
(10000, 'level_commission', 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02),
(50000, 'level_commission', 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02),

-- RSM Bonus
(2000, 'rsm_bonus', 0.025, 0.025, 0.025, 0.025, 0.025, 0.025, 0.025, 0.025, 0.025, 0.025),
(5000, 'rsm_bonus', 0.025, 0.025, 0.025, 0.025, 0.025, 0.025, 0.025, 0.025, 0.025, 0.025),
(10000, 'rsm_bonus', 0.028, 0.028, 0.028, 0.028, 0.028, 0.028, 0.028, 0.028, 0.028, 0.028),
(50000, 'rsm_bonus', 0.028, 0.028, 0.028, 0.028, 0.028, 0.028, 0.028, 0.028, 0.028, 0.028),

-- ZM Bonus
(2000, 'zm_bonus', 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02),
(5000, 'zm_bonus', 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02),
(10000, 'zm_bonus', 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02),
(50000, 'zm_bonus', 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02),

-- OKDOI Head Commission
(2000, 'okdoi_head', 0.025, 0.025, 0.025, 0.025, 0.025, 0.025, 0.025, 0.025, 0.025, 0.025),
(5000, 'okdoi_head', 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02),
(10000, 'okdoi_head', 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02),
(50000, 'okdoi_head', 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02, 0.02);

-- =====================================================
-- 10. ROW LEVEL SECURITY POLICIES
-- =====================================================

-- Enable RLS on all tables
ALTER TABLE referral_hierarchy ENABLE ROW LEVEL SECURITY;
ALTER TABLE commission_structure ENABLE ROW LEVEL SECURITY;
ALTER TABLE commission_transactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE referral_placements ENABLE ROW LEVEL SECURITY;
ALTER TABLE zonal_managers ENABLE ROW LEVEL SECURITY;
ALTER TABLE regional_sales_managers ENABLE ROW LEVEL SECURITY;

-- Referral hierarchy policies
CREATE POLICY "Users can view their own referral hierarchy" ON referral_hierarchy
    FOR SELECT USING (
        user_id = auth.uid() OR
        ancestor_id = auth.uid() OR
        EXISTS (SELECT 1 FROM users WHERE id = auth.uid() AND role = 'admin')
    );

-- Commission structure policies (public read for active structures)
CREATE POLICY "Public can view active commission structures" ON commission_structure
    FOR SELECT USING (is_active = true);

CREATE POLICY "Admins can manage commission structures" ON commission_structure
    FOR ALL USING (EXISTS (SELECT 1 FROM users WHERE id = auth.uid() AND role = 'admin'));

-- Commission transactions policies
CREATE POLICY "Users can view their commission transactions" ON commission_transactions
    FOR SELECT USING (
        beneficiary_id = auth.uid() OR
        user_id = auth.uid() OR
        EXISTS (SELECT 1 FROM users WHERE id = auth.uid() AND role = 'admin')
    );

-- Referral placements policies
CREATE POLICY "Users can view their referral placements" ON referral_placements
    FOR SELECT USING (
        parent_id = auth.uid() OR
        child_id = auth.uid() OR
        EXISTS (SELECT 1 FROM users WHERE id = auth.uid() AND role = 'admin')
    );

-- Zonal managers policies
CREATE POLICY "Public can view active zonal managers" ON zonal_managers
    FOR SELECT USING (is_active = true);

CREATE POLICY "Admins can manage zonal managers" ON zonal_managers
    FOR ALL USING (EXISTS (SELECT 1 FROM users WHERE id = auth.uid() AND role = 'admin'));

-- RSM policies
CREATE POLICY "Public can view active RSMs" ON regional_sales_managers
    FOR SELECT USING (is_active = true);

CREATE POLICY "Admins can manage RSMs" ON regional_sales_managers
    FOR ALL USING (EXISTS (SELECT 1 FROM users WHERE id = auth.uid() AND role = 'admin'));

-- =====================================================
-- 11. TRIGGERS
-- =====================================================

-- Trigger to automatically generate referral codes for all users
CREATE OR REPLACE FUNCTION auto_generate_referral_code()
RETURNS TRIGGER AS $$
BEGIN
    -- Generate referral code for all users if not provided
    IF NEW.referral_code IS NULL THEN
        NEW.referral_code := generate_referral_code();
    END IF;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_auto_generate_referral_code
    BEFORE INSERT OR UPDATE ON users
    FOR EACH ROW
    EXECUTE FUNCTION auto_generate_referral_code();

-- Trigger to update timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_commission_structure_updated_at
    BEFORE UPDATE ON commission_structure
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER trigger_commission_transactions_updated_at
    BEFORE UPDATE ON commission_transactions
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER trigger_zonal_managers_updated_at
    BEFORE UPDATE ON zonal_managers
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER trigger_regional_sales_managers_updated_at
    BEFORE UPDATE ON regional_sales_managers
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();
