import Link from 'next/link'
import Image from 'next/image'
import { MapPin, Clock, Heart, Zap } from 'lucide-react'
import Card, { CardContent } from '@/components/ui/card'
import { AdWithDetails } from '@/types'
import { formatCurrency, formatTimeAgo } from '@/lib/utils'
import { FavoritesService } from '@/lib/services/favorites'
import { useAuth } from '@/contexts/AuthContext'
import { useState, useEffect } from 'react'

// Helper function to get key category fields for display
const getKeyFieldsForDisplay = (categoryFields: Record<string, any>, categoryName?: string): string[] => {
  if (!categoryFields || Object.keys(categoryFields).length === 0) return []

  const keyFields: string[] = []

  // Mobile phones
  if (categoryName?.toLowerCase().includes('mobile')) {
    if (categoryFields.brand) keyFields.push(categoryFields.brand)
    if (categoryFields.model) keyFields.push(categoryFields.model)
    if (categoryFields.ram) keyFields.push(`${categoryFields.ram} RAM`)
    if (categoryFields.storage) keyFields.push(`${categoryFields.storage} Storage`)
  }

  // Cars
  else if (categoryName?.toLowerCase().includes('car') || categoryName?.toLowerCase().includes('vehicle')) {
    if (categoryFields.brand) keyFields.push(categoryFields.brand)
    if (categoryFields.model) keyFields.push(categoryFields.model)
    if (categoryFields.year) keyFields.push(categoryFields.year.toString())
    if (categoryFields.fuel_type) keyFields.push(categoryFields.fuel_type)
  }

  // Property
  else if (categoryName?.toLowerCase().includes('property') || categoryName?.toLowerCase().includes('house')) {
    if (categoryFields.bedrooms) keyFields.push(`${categoryFields.bedrooms} bed`)
    if (categoryFields.bathrooms) keyFields.push(`${categoryFields.bathrooms} bath`)
    if (categoryFields.property_size) keyFields.push(`${categoryFields.property_size} sq ft`)
    if (categoryFields.furnished_status) keyFields.push(categoryFields.furnished_status.replace('_', ' '))
  }

  // Jobs
  else if (categoryName?.toLowerCase().includes('job')) {
    if (categoryFields.employment_type) keyFields.push(categoryFields.employment_type.replace('_', ' '))
    if (categoryFields.experience_level) keyFields.push(categoryFields.experience_level.replace('_', ' '))
    if (categoryFields.work_location) keyFields.push(categoryFields.work_location.replace('_', ' '))
  }

  return keyFields.slice(0, 3) // Limit to 3 key fields
}

interface AdCardProps {
  ad: AdWithDetails
  showFeaturedBadge?: boolean
  viewMode?: 'grid' | 'list'
  showViewCount?: boolean // New prop to control view count visibility
  showFavoriteButton?: boolean // New prop to control favorite button visibility
  showStatusBadge?: boolean // New prop to control status badge visibility (for dashboard)
  onFavoriteChange?: (adId: string, isFavorited: boolean) => void // Callback for favorite changes
}

export default function AdCard({
  ad,
  showFeaturedBadge = true,
  viewMode = 'grid',
  showViewCount = false, // Default to false for public listings
  showFavoriteButton = true, // Default to true
  showStatusBadge = false, // Default to false for public listings
  onFavoriteChange
}: AdCardProps) {
  const { user } = useAuth()
  const [isFavorited, setIsFavorited] = useState(false)
  const [favoriteLoading, setFavoriteLoading] = useState(false)

  useEffect(() => {
    if (user && showFavoriteButton) {
      checkFavoriteStatus()
    }
  }, [user, ad.id, showFavoriteButton])

  const checkFavoriteStatus = async () => {
    if (!user) return

    try {
      const favorited = await FavoritesService.isAdFavorited(user.id, ad.id)
      setIsFavorited(favorited)
    } catch (error) {
      console.error('Error checking favorite status:', error)
    }
  }

  const handleFavoriteClick = async (e: React.MouseEvent) => {
    e.preventDefault()
    e.stopPropagation()

    if (!user || favoriteLoading) return

    try {
      setFavoriteLoading(true)
      const newFavoriteStatus = await FavoritesService.toggleFavorite(user.id, ad.id)
      setIsFavorited(newFavoriteStatus)

      // Call the callback if provided
      if (onFavoriteChange) {
        onFavoriteChange(ad.id, newFavoriteStatus)
      }
    } catch (error) {
      console.error('Error toggling favorite:', error)
    } finally {
      setFavoriteLoading(false)
    }
  }

  const formatTimeAgo = (dateString: string) => {
    const date = new Date(dateString)
    const now = new Date()
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60))

    if (diffInHours < 1) {
      return 'Just now'
    } else if (diffInHours < 24) {
      return `${diffInHours}h ago`
    } else {
      const diffInDays = Math.floor(diffInHours / 24)
      return `${diffInDays}d ago`
    }
  }

  const getConditionColor = (condition: string | null | undefined) => {
    if (!condition) return 'bg-gray-100 text-gray-800'

    switch (condition) {
      case 'new':
        return 'bg-green-100 text-green-800'
      case 'used':
        return 'bg-yellow-100 text-yellow-800'
      case 'refurbished':
        return 'bg-blue-100 text-blue-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return { text: 'Published', color: 'bg-green-100 text-green-800' }
      case 'pending':
        return { text: 'Pending', color: 'bg-yellow-100 text-yellow-800' }
      case 'sold':
        return { text: 'Sold', color: 'bg-gray-100 text-gray-800' }
      case 'expired':
        return { text: 'Expired', color: 'bg-red-100 text-red-800' }
      case 'draft':
        return { text: 'Draft', color: 'bg-blue-100 text-blue-800' }
      case 'rejected':
        return { text: 'Rejected', color: 'bg-red-100 text-red-800' }
      default:
        return { text: status, color: 'bg-gray-100 text-gray-800' }
    }
  }

  // List view layout
  if (viewMode === 'list') {
    return (
      <Link href={`/ad/${ad.id}`}>
        <Card className="hover:shadow-lg transition-shadow duration-200 cursor-pointer group overflow-hidden bg-white rounded-xl border border-gray-200 mb-6">
          <div className="flex h-48">
            {/* Image */}
            <div className="w-64 h-full bg-gray-200 relative overflow-hidden flex-shrink-0 rounded-l-xl">
              {ad.ad_images && ad.ad_images.length > 0 ? (
                <Image
                  src={ad.ad_images[0].image_url}
                  alt={ad.title}
                  fill
                  className="object-cover group-hover:scale-105 transition-transform duration-200"
                  sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 256px"
                />
              ) : (
                <div className="w-full h-full bg-gradient-to-br from-primary-blue/20 to-secondary-blue/20 flex items-center justify-center">
                  <span className="text-gray-500 text-sm">No Image</span>
                </div>
              )}

              {/* Badges */}
              <div className="absolute top-3 left-3 flex flex-col gap-2">
                {showStatusBadge && (
                  <span className={`px-2 py-1 rounded text-xs font-semibold ${getStatusBadge(ad.status).color}`}>
                    {getStatusBadge(ad.status).text}
                  </span>
                )}
                {ad.is_boosted && (
                  <span className="bg-orange-500 text-white px-2 py-1 rounded text-xs font-semibold flex items-center">
                    <Zap className="h-3 w-3 mr-1 fill-current" />
                    Boosted
                  </span>
                )}
                {showFeaturedBadge && ad.featured && (
                  <span className="bg-accent-orange text-white px-2 py-1 rounded text-xs font-semibold">
                    Featured
                  </span>
                )}
              </div>
            </div>

            {/* Content */}
            <div className="flex-1 p-6 flex flex-col justify-between">
              <div className="flex-1">
                {/* Category at top */}
                <div className="text-xs text-gray-500 mb-3 font-medium uppercase tracking-wide">
                  {ad.category.name}{ad.subcategory ? ` • ${ad.subcategory.name}` : ''}
                </div>

                <div className="flex justify-between items-start mb-4">
                  <h3 className="font-bold text-gray-900 text-xl group-hover:text-primary-blue transition-colors line-clamp-2 flex-1 pr-4">
                    {ad.title}
                  </h3>
                  {showFavoriteButton && user && (
                    <button
                      className="p-2 hover:bg-gray-100 rounded-full transition-colors flex-shrink-0"
                      onClick={handleFavoriteClick}
                      disabled={favoriteLoading}
                    >
                      <Heart
                        className={`h-5 w-5 transition-colors ${
                          isFavorited
                            ? 'text-red-500 fill-current'
                            : 'text-gray-600 hover:text-red-500'
                        }`}
                      />
                    </button>
                  )}
                </div>

                <div className="flex items-center text-gray-500 text-sm mb-4">
                  <MapPin className="h-4 w-4 mr-2 flex-shrink-0" />
                  <span className="truncate">{ad.location}</span>
                </div>

                {/* Category-specific key fields for list view */}
                {(() => {
                  const keyFields = getKeyFieldsForDisplay(ad.category_fields || {}, ad.category?.name)
                  if (keyFields.length > 0) {
                    return (
                      <div className="flex flex-wrap gap-2 mb-4">
                        {keyFields.map((field, index) => (
                          <span
                            key={index}
                            className="inline-block px-2 py-1 bg-blue-100 text-blue-700 text-xs rounded-full capitalize"
                          >
                            {field}
                          </span>
                        ))}
                      </div>
                    )
                  }
                  return null
                })()}
              </div>

              {/* Bottom section with price and details */}
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-4">
                  <span className="text-2xl font-bold text-primary-blue">
                    {formatCurrency(ad.price)}
                  </span>
                  {ad.condition && (
                    <span className={`px-3 py-1 rounded-full text-xs font-medium ${getConditionColor(ad.condition)}`}>
                      {ad.condition.charAt(0).toUpperCase() + ad.condition.slice(1)}
                    </span>
                  )}
                </div>

                <div className="flex items-center text-gray-400 text-sm">
                  {showViewCount && (
                    <span className="mr-4">{ad.views} views</span>
                  )}
                  <div className="flex items-center">
                    <Clock className="h-4 w-4 mr-1" />
                    {formatTimeAgo(ad.created_at)}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </Card>
      </Link>
    )
  }

  // Grid view layout (default)
  return (
    <Link href={`/ad/${ad.id}`}>
      <Card className="hover:shadow-lg transition-shadow duration-200 cursor-pointer group overflow-hidden h-full flex flex-col bg-white rounded-xl border border-gray-200">
        <div className="relative">
          <div className="w-full h-40 bg-gray-200 relative overflow-hidden rounded-t-xl">
            {ad.ad_images && ad.ad_images.length > 0 ? (
              <Image
                src={ad.ad_images[0].image_url}
                alt={ad.title}
                fill
                className="object-cover group-hover:scale-105 transition-transform duration-200"
                sizes="(max-width: 640px) 100vw, (max-width: 1024px) 50vw, 25vw"
              />
            ) : (
              <div className="w-full h-full bg-gradient-to-br from-primary-blue/20 to-secondary-blue/20 flex items-center justify-center">
                <span className="text-gray-500 text-sm">No Image</span>
              </div>
            )}
          </div>

          {/* Badges */}
          <div className="absolute top-2 left-2 flex flex-col gap-1">
            {showStatusBadge && (
              <span className={`px-1.5 py-0.5 rounded text-xs font-semibold ${getStatusBadge(ad.status).color}`}>
                {getStatusBadge(ad.status).text}
              </span>
            )}
            {ad.is_boosted && (
              <span className="bg-orange-500 text-white px-1.5 py-0.5 rounded text-xs font-semibold flex items-center">
                <Zap className="h-3 w-3 mr-1 fill-current" />
                Boosted
              </span>
            )}
            {showFeaturedBadge && ad.featured && (
              <span className="bg-accent-orange text-white px-1.5 py-0.5 rounded text-xs font-semibold">
                Featured
              </span>
            )}
          </div>

          {/* Condition Badge */}
          {ad.condition && (
            <div className="absolute top-2 right-10">
              <span className={`px-1.5 py-0.5 rounded text-xs font-medium ${getConditionColor(ad.condition)}`}>
                {ad.condition.charAt(0).toUpperCase() + ad.condition.slice(1)}
              </span>
            </div>
          )}

          {/* Favorite Button */}
          {showFavoriteButton && user && (
            <button
              className="absolute top-2 right-2 p-1.5 bg-white/80 hover:bg-white rounded-full shadow-sm transition-colors"
              onClick={handleFavoriteClick}
              disabled={favoriteLoading}
            >
              <Heart
                className={`h-3.5 w-3.5 transition-colors ${
                  isFavorited
                    ? 'text-red-500 fill-current'
                    : 'text-gray-600 hover:text-red-500'
                }`}
              />
            </button>
          )}
        </div>

        <CardContent className="p-3 flex-1 flex flex-col">
          {/* Category at top */}
          <div className="text-xs text-gray-500 mb-1 font-medium uppercase tracking-wide truncate">
            {ad.category.name}
          </div>

          <div className="flex-1">
            <h3 className="font-semibold text-gray-900 mb-2 line-clamp-2 group-hover:text-primary-blue transition-colors text-sm leading-tight">
              {ad.title}
            </h3>

            <div className="flex items-center text-gray-500 text-xs mb-2">
              <MapPin className="h-3 w-3 mr-1 flex-shrink-0" />
              <span className="truncate">{ad.location}</span>
            </div>

            {/* Category-specific key fields */}
            {(() => {
              const keyFields = getKeyFieldsForDisplay(ad.category_fields || {}, ad.category?.name)
              if (keyFields.length > 0) {
                return (
                  <div className="flex flex-wrap gap-1 mb-2">
                    {keyFields.map((field, index) => (
                      <span
                        key={index}
                        className="inline-block px-1.5 py-0.5 bg-gray-100 text-gray-600 text-xs rounded capitalize"
                      >
                        {field}
                      </span>
                    ))}
                  </div>
                )
              }
              return null
            })()}
          </div>

          <div className="mt-auto">
            <div className="flex items-center justify-between mb-1">
              <span className="text-lg font-bold text-primary-blue">
                {formatCurrency(ad.price)}
              </span>
            </div>

            <div className="flex items-center justify-between text-gray-400 text-xs">
              <div className="flex items-center">
                <Clock className="h-3 w-3 mr-1" />
                {formatTimeAgo(ad.created_at)}
              </div>
              {showViewCount && (
                <span>{ad.views} views</span>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    </Link>
  )
}
