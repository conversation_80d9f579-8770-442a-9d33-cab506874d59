const fs = require('fs');
const path = require('path');

function verifyBackup(backupFilePath) {
  console.log('🔍 Verifying backup integrity...');
  console.log(`📁 Backup file: ${backupFilePath}`);
  
  if (!fs.existsSync(backupFilePath)) {
    console.error('❌ Backup file not found!');
    return false;
  }
  
  try {
    // Read and parse backup file
    const backupData = JSON.parse(fs.readFileSync(backupFilePath, 'utf8'));
    
    // Basic structure validation
    if (!backupData.timestamp || !backupData.tables) {
      console.error('❌ Invalid backup structure!');
      return false;
    }
    
    console.log(`✅ Backup timestamp: ${backupData.timestamp}`);
    console.log(`✅ Supabase URL: ${backupData.supabase_url}`);
    
    // Verify tables
    const tables = Object.keys(backupData.tables);
    console.log(`✅ Total tables: ${tables.length}`);
    
    let totalRecords = 0;
    let tablesWithData = 0;
    let emptyTables = 0;
    
    console.log('\n📊 Table verification:');
    
    tables.forEach(tableName => {
      const tableData = backupData.tables[tableName];
      
      if (!tableData || !Array.isArray(tableData.data)) {
        console.log(`  ❌ ${tableName}: Invalid data structure`);
        return;
      }
      
      const recordCount = tableData.count || tableData.data.length;
      totalRecords += recordCount;
      
      if (recordCount > 0) {
        tablesWithData++;
        console.log(`  ✅ ${tableName}: ${recordCount} records`);
      } else {
        emptyTables++;
        console.log(`  ⚪ ${tableName}: 0 records (empty)`);
      }
    });
    
    console.log('\n📈 Summary:');
    console.log(`  Total records: ${totalRecords}`);
    console.log(`  Tables with data: ${tablesWithData}`);
    console.log(`  Empty tables: ${emptyTables}`);
    
    // File size check
    const stats = fs.statSync(backupFilePath);
    const fileSizeMB = (stats.size / (1024 * 1024)).toFixed(2);
    console.log(`  Backup file size: ${fileSizeMB} MB`);
    
    // Critical tables check
    const criticalTables = ['users', 'ads', 'categories', 'user_wallets', 'admin_settings'];
    const missingCritical = criticalTables.filter(table => !backupData.tables[table]);
    
    if (missingCritical.length > 0) {
      console.log(`\n⚠️  Missing critical tables: ${missingCritical.join(', ')}`);
    } else {
      console.log('\n✅ All critical tables present');
    }
    
    // Data integrity checks
    console.log('\n🔍 Data integrity checks:');
    
    // Check users table
    if (backupData.tables.users && backupData.tables.users.data.length > 0) {
      const users = backupData.tables.users.data;
      const usersWithEmail = users.filter(u => u.email).length;
      console.log(`  ✅ Users with email: ${usersWithEmail}/${users.length}`);
    }
    
    // Check ads table
    if (backupData.tables.ads && backupData.tables.ads.data.length > 0) {
      const ads = backupData.tables.ads.data;
      const activeAds = ads.filter(a => a.status === 'active').length;
      console.log(`  ✅ Active ads: ${activeAds}/${ads.length}`);
    }
    
    // Check wallet transactions
    if (backupData.tables.wallet_transactions && backupData.tables.wallet_transactions.data.length > 0) {
      const transactions = backupData.tables.wallet_transactions.data;
      const completedTransactions = transactions.filter(t => t.status === 'completed').length;
      console.log(`  ✅ Completed transactions: ${completedTransactions}/${transactions.length}`);
    }
    
    console.log('\n🎉 Backup verification completed successfully!');
    console.log('✅ Backup is valid and ready for use');
    
    return true;
    
  } catch (error) {
    console.error('❌ Error verifying backup:', error.message);
    return false;
  }
}

function verifyAllBackups() {
  console.log('🔍 Verifying all backup files in directory...\n');
  
  const backupDir = __dirname;
  const files = fs.readdirSync(backupDir);
  const backupFiles = files.filter(file => file.startsWith('complete_backup_') && file.endsWith('.json'));
  
  if (backupFiles.length === 0) {
    console.log('❌ No backup files found!');
    return;
  }
  
  console.log(`Found ${backupFiles.length} backup file(s):\n`);
  
  backupFiles.forEach((file, index) => {
    console.log(`\n${'='.repeat(60)}`);
    console.log(`Verifying backup ${index + 1}/${backupFiles.length}: ${file}`);
    console.log('='.repeat(60));
    
    const filePath = path.join(backupDir, file);
    const isValid = verifyBackup(filePath);
    
    if (isValid) {
      console.log(`✅ ${file} - VALID`);
    } else {
      console.log(`❌ ${file} - INVALID`);
    }
  });
  
  console.log('\n' + '='.repeat(60));
  console.log('🎉 All backup verifications completed!');
  console.log('='.repeat(60));
}

// Command line usage
const args = process.argv.slice(2);

if (args.length === 0) {
  verifyAllBackups();
} else {
  const backupFile = args[0];
  if (fs.existsSync(backupFile)) {
    verifyBackup(backupFile);
  } else {
    console.error(`❌ Backup file not found: ${backupFile}`);
    console.log('\nUsage:');
    console.log('  node verify_backup.js                    # Verify all backups');
    console.log('  node verify_backup.js <backup_file>      # Verify specific backup');
  }
}
