// Gift Tasks & Present System Tests
// Comprehensive test suite for the gift tasks and present allocation system

import { PresentAllocationService } from '@/lib/services/presentAllocationService'
import { GiftTasksService } from '@/lib/services/giftTasksService'
import { GiftSystemSecurity } from '@/lib/middleware/giftSystemSecurity'

// Mock Supabase
jest.mock('@/lib/supabase', () => ({
  supabase: {
    auth: {
      getSession: jest.fn()
    },
    from: jest.fn(() => ({
      select: jest.fn(() => ({
        eq: jest.fn(() => ({
          single: jest.fn(),
          order: jest.fn()
        }))
      }))
    }))
  },
  supabaseAdmin: {
    rpc: jest.fn(),
    from: jest.fn(() => ({
      select: jest.fn(() => ({
        eq: jest.fn(() => ({
          single: jest.fn(),
          range: jest.fn(() => ({
            order: jest.fn()
          }))
        })),
        insert: jest.fn(() => ({
          select: jest.fn(() => ({
            single: jest.fn()
          }))
        })),
        update: jest.fn(() => ({
          eq: jest.fn(() => ({
            select: jest.fn(() => ({
              single: jest.fn()
            }))
          }))
        }))
      }))
    }))
  }
}))

describe('Gift Tasks & Present System', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  describe('Present Allocation Service', () => {
    test('should calculate present allocations correctly', async () => {
      const mockAllocations = [
        {
          allocation_id: 'test-id-1',
          user_id: 'user-1',
          allocation_type: 'annual_present_user',
          allocation_amount: 100 // 1% of 10,000
        },
        {
          allocation_id: 'test-id-2',
          user_id: 'leader-1',
          allocation_type: 'present_leader',
          allocation_amount: 200 // 2% of 10,000
        }
      ]

      const { supabaseAdmin } = require('@/lib/supabase')
      supabaseAdmin.rpc.mockResolvedValue({ data: mockAllocations, error: null })

      const result = await PresentAllocationService.allocatePresentsFromPurchase(
        'user-1',
        10000,
        'sub-123',
        'txn-123'
      )

      expect(result).toEqual(mockAllocations)
      expect(supabaseAdmin.rpc).toHaveBeenCalledWith('calculate_present_allocations', {
        purchaser_id: 'user-1',
        package_amount: 10000,
        subscription_id: 'sub-123',
        transaction_id: 'txn-123'
      })
    })

    test('should get present pool summary', async () => {
      const mockPools = [
        {
          pool_type: 'normal_present',
          total_allocated: 5000,
          total_distributed: 2000,
          available_balance: 3000,
          allocation_count: 10,
          last_updated: '2024-01-01T00:00:00Z'
        }
      ]

      const { supabaseAdmin } = require('@/lib/supabase')
      supabaseAdmin.rpc.mockResolvedValue({ data: mockPools, error: null })

      const result = await PresentAllocationService.getPresentPoolSummary()

      expect(result).toEqual(mockPools)
      expect(supabaseAdmin.rpc).toHaveBeenCalledWith('get_present_pool_summary')
    })

    test('should handle errors gracefully', async () => {
      const { supabaseAdmin } = require('@/lib/supabase')
      supabaseAdmin.rpc.mockResolvedValue({
        data: null,
        error: { message: 'Database error' }
      })

      await expect(
        PresentAllocationService.allocatePresentsFromPurchase('user-1', 10000)
      ).rejects.toThrow('Failed to allocate presents: Database error')
    })
  })

  describe('Gift Tasks Service', () => {
    test('should create a task successfully', async () => {
      const mockTask = {
        id: 'task-123',
        title: 'Test Sales Target',
        task_type: 'sales_target',
        target_user_types: ['user'],
        requirements: { sales_count: 5, time_limit_days: 30 },
        reward_amount: 1000,
        reward_type: 'normal_present',
        is_active: true,
        created_at: '2024-01-01T00:00:00Z'
      }

      const { supabaseAdmin } = require('@/lib/supabase')
      supabaseAdmin.from.mockReturnValue({
        insert: jest.fn(() => ({
          select: jest.fn(() => ({
            single: jest.fn().mockResolvedValue({ data: mockTask, error: null })
          }))
        }))
      })

      const taskData = {
        title: 'Test Sales Target',
        task_type: 'sales_target' as const,
        target_user_types: ['user'],
        requirements: { sales_count: 5, time_limit_days: 30 },
        reward_amount: 1000,
        reward_type: 'normal_present' as const,
        created_by: 'admin-1'
      }

      const result = await GiftTasksService.createTask(taskData)

      expect(result).toEqual(mockTask)
    })

    test('should get user tasks with filters', async () => {
      const mockAssignments = [
        {
          id: 'assignment-1',
          user_id: 'user-1',
          task_id: 'task-1',
          status: 'assigned',
          progress: {},
          assigned_at: '2024-01-01T00:00:00Z',
          task: {
            id: 'task-1',
            title: 'Test Task',
            task_type: 'sales_target',
            reward_amount: 500
          }
        }
      ]

      const { supabase } = require('@/lib/supabase')
      supabase.from.mockReturnValue({
        select: jest.fn(() => ({
          eq: jest.fn(() => ({
            order: jest.fn().mockResolvedValue({ data: mockAssignments, error: null })
          }))
        }))
      })

      const result = await GiftTasksService.getUserTasks('user-1')

      expect(result).toEqual(mockAssignments)
    })
  })

  describe('Security & Privacy Controls', () => {
    test('should verify admin access correctly', async () => {
      const mockRequest = {
        headers: {
          get: jest.fn((header) => {
            if (header === 'user-agent') return 'Test Agent'
            if (header === 'x-forwarded-for') return '***********'
            return null
          })
        }
      } as any

      const { supabase, supabaseAdmin } = require('@/lib/supabase')

      supabase.auth.getSession.mockResolvedValue({
        data: { session: { user: { id: 'admin-1' } } },
        error: null
      })

      supabaseAdmin.from.mockReturnValue({
        select: jest.fn(() => ({
          eq: jest.fn(() => ({
            single: jest.fn().mockResolvedValue({
              data: { user_type: 'admin' },
              error: null
            })
          }))
        }))
      })

      const context = await GiftSystemSecurity.verifyAdminAccess(mockRequest)

      expect(context).toEqual({
        userId: 'admin-1',
        userType: 'admin',
        isAdmin: true,
        ipAddress: '***********',
        userAgent: 'Test Agent'
      })
    })

    test('should sanitize sensitive data from logs', () => {
      const sensitiveData = {
        user_id: 'user-1',
        password: 'secret123',
        present_allocation: 1000,
        allocation_amount: 500,
        normal_field: 'safe_value'
      }

      const sanitized = GiftSystemSecurity.sanitizeLogData(sensitiveData)

      expect(sanitized).toEqual({
        user_id: 'user-1',
        password: '[REDACTED]',
        present_allocation: '[REDACTED]',
        allocation_amount: '[REDACTED]',
        normal_field: 'safe_value'
      })
    })
  })
})

// Integration Tests
describe('Gift System Integration Tests', () => {
  test('should handle complete task lifecycle', async () => {
    // This would test the complete flow from task creation to reward claiming
    // In a real environment, this would use test database
    expect(true).toBe(true) // Placeholder
  })

  test('should validate present allocation percentages', () => {
    // Test that present allocations match the commission structure
    const packageAmount = 10000
    const expectedAllocations = {
      annual_present_user: packageAmount * 0.01, // 1%
      present_leader: packageAmount * 0.02, // 2%
      annual_present_leader: packageAmount * 0.02 // 2%
    }

    expect(expectedAllocations.annual_present_user).toBe(100)
    expect(expectedAllocations.present_leader).toBe(200)
    expect(expectedAllocations.annual_present_leader).toBe(200)
  })

  test('should ensure admin-only visibility for present data', () => {
    // Test that present allocations are never exposed to regular users
    const userContext = {
      userId: 'user-1',
      userType: 'user',
      isAdmin: false,
      ipAddress: '127.0.0.1',
      userAgent: 'Test'
    }

    expect(() => {
      GiftSystemSecurity.validatePresentAccess(userContext)
    }).rejects.toThrow('Access denied: Present allocations are admin-only')
  })
})