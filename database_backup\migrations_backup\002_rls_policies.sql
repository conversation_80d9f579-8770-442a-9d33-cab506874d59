-- Enable Row Level Security
ALTER TABLE categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE subcategories ENABLE ROW LEVEL SECURITY;
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE ads ENABLE ROW LEVEL SECURITY;
ALTER TABLE ad_images ENABLE ROW LEVEL SECURITY;

-- Categories policies (public read)
CREATE POLICY "Categories are viewable by everyone" ON categories
    FOR SELECT USING (true);

CREATE POLICY "Only admins can insert categories" ON categories
    FOR INSERT WITH CHECK (false); -- Will be updated when admin system is implemented

CREATE POLICY "Only admins can update categories" ON categories
    FOR UPDATE USING (false); -- Will be updated when admin system is implemented

-- Subcategories policies (public read)
CREATE POLICY "Subcategories are viewable by everyone" ON subcategories
    FOR SELECT USING (true);

CREATE POLICY "Only admins can insert subcategories" ON subcategories
    FOR INSERT WITH CHECK (false); -- Will be updated when admin system is implemented

CREATE POLICY "Only admins can update subcategories" ON subcategories
    FOR UPDATE USING (false); -- Will be updated when admin system is implemented

-- Users policies
CREATE POLICY "Users can view their own profile" ON users
    FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can insert their own profile" ON users
    FOR INSERT WITH CHECK (auth.uid() = id);

CREATE POLICY "Users can update their own profile" ON users
    FOR UPDATE USING (auth.uid() = id);

-- Ads policies
CREATE POLICY "Ads are viewable by everyone" ON ads
    FOR SELECT USING (status = 'active' OR auth.uid() = user_id);

CREATE POLICY "Users can insert their own ads" ON ads
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own ads" ON ads
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own ads" ON ads
    FOR DELETE USING (auth.uid() = user_id);

-- Ad images policies
CREATE POLICY "Ad images are viewable by everyone" ON ad_images
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM ads 
            WHERE ads.id = ad_images.ad_id 
            AND (ads.status = 'active' OR auth.uid() = ads.user_id)
        )
    );

CREATE POLICY "Users can insert images for their own ads" ON ad_images
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM ads 
            WHERE ads.id = ad_images.ad_id 
            AND auth.uid() = ads.user_id
        )
    );

CREATE POLICY "Users can update images for their own ads" ON ad_images
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM ads 
            WHERE ads.id = ad_images.ad_id 
            AND auth.uid() = ads.user_id
        )
    );

CREATE POLICY "Users can delete images for their own ads" ON ad_images
    FOR DELETE USING (
        EXISTS (
            SELECT 1 FROM ads 
            WHERE ads.id = ad_images.ad_id 
            AND auth.uid() = ads.user_id
        )
    );
