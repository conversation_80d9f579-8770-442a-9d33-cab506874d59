'use client'

import React from 'react'
import {
  Search,
  X,
  ChevronUp,
  ChevronDown,
  ExpandIcon,
  ShrinkIcon,
  Download,
  FileImage,
  FileText,
  ZoomIn,
  ZoomOut,
  RotateCcw,
  Loader2,
  Maximize2,
  RefreshCw
} from 'lucide-react'

interface ReferralTreeToolbarProps {
  // Search props
  searchTerm: string
  onSearchChange: (term: string) => void
  onClearSearch: () => void
  searchResults: number
  currentResultIndex: number
  onNextResult: () => void
  onPreviousResult: () => void
  hasResults: boolean

  // Navigation props
  onExpandAll: () => void
  onCollapseAll: () => void

  // Export props
  onExportPNG: () => void
  onExportPDF: () => void
  isExporting: boolean

  // Stats
  totalNodes: number
  visibleNodes: number

  // Zoom controls (optional)
  scale?: number
  onZoomIn?: () => void
  onZoomOut?: () => void
  onResetView?: () => void

  // New props for maximize and refresh
  onMaximize?: () => void
  onRefresh?: () => void
  isRefreshing?: boolean
}

export default function ReferralTreeToolbar({
  searchTerm,
  onSearchChange,
  onClearSearch,
  searchResults,
  currentResultIndex,
  onNextResult,
  onPreviousResult,
  hasResults,
  onExpandAll,
  onCollapseAll,
  onExportPNG,
  onExportPDF,
  isExporting,
  totalNodes,
  visibleNodes,
  scale,
  onZoomIn,
  onZoomOut,
  onResetView,
  onMaximize,
  onRefresh,
  isRefreshing
}: ReferralTreeToolbarProps) {
  return (
    <div className="bg-white border-b border-gray-200 p-4 space-y-4">
      {/* Top Row - Search and Export */}
      <div className="flex items-center justify-between gap-4">
        {/* Search Section */}
        <div className="flex items-center gap-3 flex-1 max-w-md">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <input
              type="text"
              placeholder="Search by name or email..."
              value={searchTerm}
              onChange={(e) => onSearchChange(e.target.value)}
              className="w-full pl-10 pr-10 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
            />
            {searchTerm && (
              <button
                onClick={onClearSearch}
                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
              >
                <X className="h-4 w-4" />
              </button>
            )}
          </div>

          {/* Search Results Navigation */}
          {hasResults && (
            <div className="flex items-center gap-2 text-sm text-gray-600">
              <span className="whitespace-nowrap">
                {currentResultIndex + 1} of {searchResults}
              </span>
              <div className="flex gap-1">
                <button
                  onClick={onPreviousResult}
                  disabled={searchResults <= 1}
                  className="p-1 rounded hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed"
                  title="Previous result"
                >
                  <ChevronUp className="h-4 w-4" />
                </button>
                <button
                  onClick={onNextResult}
                  disabled={searchResults <= 1}
                  className="p-1 rounded hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed"
                  title="Next result"
                >
                  <ChevronDown className="h-4 w-4" />
                </button>
              </div>
            </div>
          )}
        </div>

        {/* Export and Control Section */}
        <div className="flex items-center gap-2">
          {/* Refresh Button */}
          {onRefresh && (
            <button
              onClick={onRefresh}
              disabled={isRefreshing}
              className="flex items-center gap-2 px-3 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed text-sm transition-colors"
              title="Refresh tree data"
            >
              {isRefreshing ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <RefreshCw className="h-4 w-4" />
              )}
              Refresh
            </button>
          )}

          {/* Maximize Button */}
          {onMaximize && (
            <button
              onClick={onMaximize}
              className="flex items-center gap-2 px-3 py-2 bg-indigo-500 text-white rounded-lg hover:bg-indigo-600 text-sm transition-colors"
              title="Maximize tree view"
            >
              <Maximize2 className="h-4 w-4" />
              Maximize
            </button>
          )}

          <button
            onClick={onExportPNG}
            disabled={isExporting}
            className="flex items-center gap-2 px-3 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 disabled:opacity-50 disabled:cursor-not-allowed text-sm transition-colors"
            title="Export as PNG"
          >
            {isExporting ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <FileImage className="h-4 w-4" />
            )}
            PNG
          </button>
          <button
            onClick={onExportPDF}
            disabled={isExporting}
            className="flex items-center gap-2 px-3 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 disabled:opacity-50 disabled:cursor-not-allowed text-sm transition-colors"
            title="Export as PDF"
          >
            {isExporting ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <FileText className="h-4 w-4" />
            )}
            PDF
          </button>
        </div>
      </div>

      {/* Bottom Row - Navigation and Stats */}
      <div className="flex items-center justify-between gap-4">
        {/* Navigation Controls */}
        <div className="flex items-center gap-2">
          <button
            onClick={onExpandAll}
            className="flex items-center gap-2 px-3 py-2 bg-purple-500 text-white rounded-lg hover:bg-purple-600 text-sm transition-colors"
            title="Expand all nodes"
          >
            <ExpandIcon className="h-4 w-4" />
            Expand All
          </button>
          <button
            onClick={onCollapseAll}
            className="flex items-center gap-2 px-3 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 text-sm transition-colors"
            title="Collapse all nodes"
          >
            <ShrinkIcon className="h-4 w-4" />
            Collapse All
          </button>
        </div>

        {/* Stats and Zoom Controls */}
        <div className="flex items-center gap-4">
          {/* Tree Stats */}
          <div className="text-sm text-gray-600">
            <span className="font-medium">{visibleNodes}</span> visible of{' '}
            <span className="font-medium">{totalNodes}</span> total nodes
          </div>

          {/* Zoom Controls (if provided) */}
          {(onZoomIn || onZoomOut || onResetView) && (
            <div className="flex items-center gap-1 border-l border-gray-200 pl-4">
              {scale && (
                <span className="text-sm text-gray-600 mr-2">
                  {Math.round(scale * 100)}%
                </span>
              )}
              {onZoomOut && (
                <button
                  onClick={onZoomOut}
                  className="p-2 rounded hover:bg-gray-100 text-gray-600"
                  title="Zoom out"
                >
                  <ZoomOut className="h-4 w-4" />
                </button>
              )}
              {onZoomIn && (
                <button
                  onClick={onZoomIn}
                  className="p-2 rounded hover:bg-gray-100 text-gray-600"
                  title="Zoom in"
                >
                  <ZoomIn className="h-4 w-4" />
                </button>
              )}
              {onResetView && (
                <button
                  onClick={onResetView}
                  className="p-2 rounded hover:bg-gray-100 text-gray-600"
                  title="Reset view"
                >
                  <RotateCcw className="h-4 w-4" />
                </button>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Search Results Info */}
      {searchTerm && !hasResults && (
        <div className="text-sm text-gray-500 bg-gray-50 p-3 rounded-lg">
          No results found for "{searchTerm}". Try searching by name or email.
        </div>
      )}
    </div>
  )
}
