-- Fix RLS Security Errors Migration
-- Enables Row Level Security and creates appropriate policies for all tables missing RLS

-- Enable RLS for all tables that are missing it
ALTER TABLE admin_settings ENABLE ROW LEVEL SECURITY;
ALTER TABLE withdrawal_requests ENABLE ROW LEVEL SECURITY;
ALTER TABLE shop_categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE boost_packages ENABLE ROW LEVEL SECURITY;
ALTER TABLE shop_subcategories ENABLE ROW LEVEL SECURITY;
ALTER TABLE order_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE cart_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE order_status_history ENABLE ROW LEVEL SECURITY;
ALTER TABLE districts ENABLE ROW LEVEL SECURITY;
ALTER TABLE cities ENABLE ROW LEVEL SECURITY;
ALTER TABLE order_tracking_history ENABLE ROW LEVEL SECURITY;
ALTER TABLE shop_orders ENABLE ROW LEVEL SECURITY;

-- ===== ADMIN_SETTINGS POLICIES =====
-- Only admins can manage admin settings
CREATE POLICY "Ad<PERSON> can view admin settings" ON admin_settings
    FOR SELECT USING (is_admin(auth.uid()));

CREATE POLICY "Admins can insert admin settings" ON admin_settings
    FOR INSERT WITH CHECK (is_admin(auth.uid()));

CREATE POLICY "Admins can update admin settings" ON admin_settings
    FOR UPDATE USING (is_admin(auth.uid()));

CREATE POLICY "Admins can delete admin settings" ON admin_settings
    FOR DELETE USING (is_admin(auth.uid()));

-- ===== WITHDRAWAL_REQUESTS POLICIES =====
-- Users can view and manage their own withdrawal requests, admins can view all
CREATE POLICY "Users can view own withdrawal requests" ON withdrawal_requests
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can create withdrawal requests" ON withdrawal_requests
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own pending requests" ON withdrawal_requests
    FOR UPDATE USING (auth.uid() = user_id AND status = 'pending');

CREATE POLICY "Admins can view all withdrawal requests" ON withdrawal_requests
    FOR SELECT USING (is_admin(auth.uid()));

CREATE POLICY "Admins can update withdrawal requests" ON withdrawal_requests
    FOR UPDATE USING (is_admin(auth.uid()));

-- ===== SHOP_CATEGORIES POLICIES =====
-- Public read access, admin-only write access
CREATE POLICY "Shop categories are viewable by everyone" ON shop_categories
    FOR SELECT USING (true);

CREATE POLICY "Admins can insert shop categories" ON shop_categories
    FOR INSERT WITH CHECK (is_admin(auth.uid()));

CREATE POLICY "Admins can update shop categories" ON shop_categories
    FOR UPDATE USING (is_admin(auth.uid()));

CREATE POLICY "Admins can delete shop categories" ON shop_categories
    FOR DELETE USING (is_admin(auth.uid()));

-- ===== BOOST_PACKAGES POLICIES =====
-- Public read access to active packages, admin-only write access
CREATE POLICY "Active boost packages are viewable by everyone" ON boost_packages
    FOR SELECT USING (is_active = true);

CREATE POLICY "Admins can view all boost packages" ON boost_packages
    FOR SELECT USING (is_admin(auth.uid()));

CREATE POLICY "Admins can insert boost packages" ON boost_packages
    FOR INSERT WITH CHECK (is_admin(auth.uid()));

CREATE POLICY "Admins can update boost packages" ON boost_packages
    FOR UPDATE USING (is_admin(auth.uid()));

CREATE POLICY "Admins can delete boost packages" ON boost_packages
    FOR DELETE USING (is_admin(auth.uid()));

-- ===== SHOP_SUBCATEGORIES POLICIES =====
-- Public read access, admin-only write access
CREATE POLICY "Shop subcategories are viewable by everyone" ON shop_subcategories
    FOR SELECT USING (true);

CREATE POLICY "Admins can insert shop subcategories" ON shop_subcategories
    FOR INSERT WITH CHECK (is_admin(auth.uid()));

CREATE POLICY "Admins can update shop subcategories" ON shop_subcategories
    FOR UPDATE USING (is_admin(auth.uid()));

CREATE POLICY "Admins can delete shop subcategories" ON shop_subcategories
    FOR DELETE USING (is_admin(auth.uid()));

-- ===== DISTRICTS POLICIES =====
-- Public read access, admin-only write access
CREATE POLICY "Districts are viewable by everyone" ON districts
    FOR SELECT USING (true);

CREATE POLICY "Admins can insert districts" ON districts
    FOR INSERT WITH CHECK (is_admin(auth.uid()));

CREATE POLICY "Admins can update districts" ON districts
    FOR UPDATE USING (is_admin(auth.uid()));

CREATE POLICY "Admins can delete districts" ON districts
    FOR DELETE USING (is_admin(auth.uid()));

-- ===== CITIES POLICIES =====
-- Public read access, admin-only write access
CREATE POLICY "Cities are viewable by everyone" ON cities
    FOR SELECT USING (true);

CREATE POLICY "Admins can insert cities" ON cities
    FOR INSERT WITH CHECK (is_admin(auth.uid()));

CREATE POLICY "Admins can update cities" ON cities
    FOR UPDATE USING (is_admin(auth.uid()));

CREATE POLICY "Admins can delete cities" ON cities
    FOR DELETE USING (is_admin(auth.uid()));

-- ===== CART_ITEMS POLICIES =====
-- Users can only access their own cart items
CREATE POLICY "Users can view own cart items" ON cart_items
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own cart items" ON cart_items
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own cart items" ON cart_items
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own cart items" ON cart_items
    FOR DELETE USING (auth.uid() = user_id);

CREATE POLICY "Admins can view all cart items" ON cart_items
    FOR SELECT USING (is_admin(auth.uid()));

-- ===== SHOP_ORDERS POLICIES =====
-- Buyers and sellers can view their orders, admins can view all
CREATE POLICY "Buyers can view their orders" ON shop_orders
    FOR SELECT USING (auth.uid() = buyer_id);

CREATE POLICY "Sellers can view their orders" ON shop_orders
    FOR SELECT USING (auth.uid() = seller_id);

CREATE POLICY "Buyers can create orders" ON shop_orders
    FOR INSERT WITH CHECK (auth.uid() = buyer_id);

CREATE POLICY "Sellers can update order status" ON shop_orders
    FOR UPDATE USING (auth.uid() = seller_id);

CREATE POLICY "Buyers can update delivery confirmation" ON shop_orders
    FOR UPDATE USING (auth.uid() = buyer_id AND status = 'shipped');

CREATE POLICY "Admins can view all orders" ON shop_orders
    FOR SELECT USING (is_admin(auth.uid()));

CREATE POLICY "Admins can update all orders" ON shop_orders
    FOR UPDATE USING (is_admin(auth.uid()));

-- ===== ORDER_ITEMS POLICIES =====
-- Users can view order items for their orders (buyer or seller)
CREATE POLICY "Users can view order items for their orders" ON order_items
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM shop_orders
            WHERE shop_orders.id = order_items.order_id
            AND (shop_orders.buyer_id = auth.uid() OR shop_orders.seller_id = auth.uid())
        )
    );

CREATE POLICY "Buyers can insert order items" ON order_items
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM shop_orders
            WHERE shop_orders.id = order_items.order_id
            AND shop_orders.buyer_id = auth.uid()
        )
    );

CREATE POLICY "Sellers can update order items" ON order_items
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM shop_orders
            WHERE shop_orders.id = order_items.order_id
            AND shop_orders.seller_id = auth.uid()
        )
    );

CREATE POLICY "Admins can view all order items" ON order_items
    FOR SELECT USING (is_admin(auth.uid()));

CREATE POLICY "Admins can manage all order items" ON order_items
    FOR ALL USING (is_admin(auth.uid()));

-- ===== ORDER_STATUS_HISTORY POLICIES =====
-- Users can view status history for their orders
CREATE POLICY "Users can view order status history for their orders" ON order_status_history
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM shop_orders
            WHERE shop_orders.id = order_status_history.order_id
            AND (shop_orders.buyer_id = auth.uid() OR shop_orders.seller_id = auth.uid())
        )
    );

CREATE POLICY "System can insert order status history" ON order_status_history
    FOR INSERT WITH CHECK (true); -- Allows system triggers to insert

CREATE POLICY "Admins can view all order status history" ON order_status_history
    FOR SELECT USING (is_admin(auth.uid()));

-- ===== ORDER_TRACKING_HISTORY POLICIES =====
-- Users can view tracking history for their orders
CREATE POLICY "Users can view order tracking history for their orders" ON order_tracking_history
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM shop_orders
            WHERE shop_orders.id = order_tracking_history.order_id
            AND (shop_orders.buyer_id = auth.uid() OR shop_orders.seller_id = auth.uid())
        )
    );

CREATE POLICY "Sellers can insert tracking updates" ON order_tracking_history
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM shop_orders
            WHERE shop_orders.id = order_tracking_history.order_id
            AND shop_orders.seller_id = auth.uid()
        )
    );

CREATE POLICY "Admins can view all order tracking history" ON order_tracking_history
    FOR SELECT USING (is_admin(auth.uid()));

CREATE POLICY "Admins can manage all order tracking history" ON order_tracking_history
    FOR ALL USING (is_admin(auth.uid()));
