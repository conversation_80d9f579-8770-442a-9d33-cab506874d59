const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');

// Load environment variables
require('dotenv').config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase configuration in .env.local');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function getTableSchema() {
  console.log('Extracting table schema...');
  
  const { data, error } = await supabase.rpc('get_table_schema');
  
  if (error) {
    console.log('RPC function not available, using alternative method...');
    
    // Alternative: Get table information from information_schema
    const { data: tables, error: tablesError } = await supabase
      .from('information_schema.tables')
      .select('*')
      .eq('table_schema', 'public');
    
    if (tablesError) {
      console.error('Error getting tables:', tablesError);
      return null;
    }
    
    return tables;
  }
  
  return data;
}

async function getTableColumns() {
  console.log('Extracting table columns...');
  
  const { data, error } = await supabase
    .rpc('get_table_columns');
  
  if (error) {
    console.log('RPC function not available, using SQL query...');
    
    // Use raw SQL query
    const { data: columns, error: columnsError } = await supabase
      .from('information_schema.columns')
      .select('*')
      .eq('table_schema', 'public')
      .order('table_name')
      .order('ordinal_position');
    
    if (columnsError) {
      console.error('Error getting columns:', columnsError);
      return null;
    }
    
    return columns;
  }
  
  return data;
}

async function getForeignKeys() {
  console.log('Extracting foreign key constraints...');
  
  const query = `
    SELECT
      tc.table_name,
      kcu.column_name,
      ccu.table_name AS foreign_table_name,
      ccu.column_name AS foreign_column_name,
      tc.constraint_name
    FROM
      information_schema.table_constraints AS tc
      JOIN information_schema.key_column_usage AS kcu
        ON tc.constraint_name = kcu.constraint_name
        AND tc.table_schema = kcu.table_schema
      JOIN information_schema.constraint_column_usage AS ccu
        ON ccu.constraint_name = tc.constraint_name
        AND ccu.table_schema = tc.table_schema
    WHERE tc.constraint_type = 'FOREIGN KEY'
      AND tc.table_schema = 'public';
  `;
  
  try {
    const { data, error } = await supabase.rpc('execute_sql', { query });
    
    if (error) {
      console.log('Cannot execute custom SQL, skipping foreign keys');
      return [];
    }
    
    return data;
  } catch (err) {
    console.log('Foreign key extraction failed:', err.message);
    return [];
  }
}

async function getIndexes() {
  console.log('Extracting indexes...');
  
  const query = `
    SELECT
      schemaname,
      tablename,
      indexname,
      indexdef
    FROM pg_indexes
    WHERE schemaname = 'public'
    ORDER BY tablename, indexname;
  `;
  
  try {
    const { data, error } = await supabase.rpc('execute_sql', { query });
    
    if (error) {
      console.log('Cannot execute custom SQL, skipping indexes');
      return [];
    }
    
    return data;
  } catch (err) {
    console.log('Index extraction failed:', err.message);
    return [];
  }
}

async function createSchemaBackup() {
  console.log('Starting schema backup...');
  
  const schema = {
    timestamp: new Date().toISOString(),
    supabase_url: supabaseUrl,
    tables: await getTableSchema(),
    columns: await getTableColumns(),
    foreign_keys: await getForeignKeys(),
    indexes: await getIndexes()
  };
  
  // Save schema backup
  const schemaFileName = `schema_backup_${new Date().toISOString().replace(/[:.]/g, '-')}.json`;
  const schemaPath = path.join(__dirname, schemaFileName);
  
  fs.writeFileSync(schemaPath, JSON.stringify(schema, null, 2));
  
  console.log(`\n✅ Schema backup completed!`);
  console.log(`📁 Schema saved to: ${schemaPath}`);
  
  return schema;
}

// Also copy existing migrations
async function copyMigrations() {
  console.log('Copying existing migrations...');
  
  const migrationsDir = path.join(__dirname, '..', 'supabase', 'migrations');
  const backupMigrationsDir = path.join(__dirname, 'migrations_backup');
  
  if (!fs.existsSync(backupMigrationsDir)) {
    fs.mkdirSync(backupMigrationsDir);
  }
  
  if (fs.existsSync(migrationsDir)) {
    const files = fs.readdirSync(migrationsDir);
    
    files.forEach(file => {
      if (file.endsWith('.sql')) {
        const sourcePath = path.join(migrationsDir, file);
        const destPath = path.join(backupMigrationsDir, file);
        fs.copyFileSync(sourcePath, destPath);
        console.log(`✓ Copied migration: ${file}`);
      }
    });
    
    console.log(`📁 Migrations copied to: ${backupMigrationsDir}`);
  } else {
    console.log('No migrations directory found');
  }
}

// Run the schema backup
Promise.all([
  createSchemaBackup(),
  copyMigrations()
]).then(() => {
  console.log('\n🎉 Schema backup process completed!');
}).catch(console.error);
