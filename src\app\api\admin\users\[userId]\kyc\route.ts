import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  }
)

export async function GET(
  request: NextRequest,
  { params }: { params: { userId: string } }
) {
  try {
    const { userId } = params

    // Get user KYC information
    const { data: user, error: userError } = await supabaseAdmin
      .from('users')
      .select('kyc_status, kyc_submitted_at, kyc_approved_at')
      .eq('id', userId)
      .single()

    if (userError) {
      throw userError
    }

    // Get KYC submission details if exists
    const { data: kycSubmission, error: kycError } = await supabaseAdmin
      .from('kyc_submissions')
      .select('status, created_at, reviewed_at, rejection_reason')
      .eq('user_id', userId)
      .single()

    if (kycError && kycError.code !== 'PGRST116') {
      console.error('KYC submission error:', kycError)
    }

    return NextResponse.json({
      kyc_status: user.kyc_status || 'not_submitted',
      kyc_submitted_at: user.kyc_submitted_at || kycSubmission?.created_at,
      kyc_approved_at: user.kyc_approved_at || kycSubmission?.reviewed_at,
      submission_status: kycSubmission?.status,
      rejection_reason: kycSubmission?.rejection_reason
    })

  } catch (error) {
    console.error('Error fetching user KYC data:', error)
    return NextResponse.json(
      { error: 'Failed to fetch KYC data' },
      { status: 500 }
    )
  }
}
