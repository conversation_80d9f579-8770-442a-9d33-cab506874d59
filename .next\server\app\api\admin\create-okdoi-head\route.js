"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/admin/create-okdoi-head/route";
exports.ids = ["app/api/admin/create-okdoi-head/route"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

module.exports = require("https");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("stream");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fadmin%2Fcreate-okdoi-head%2Froute&page=%2Fapi%2Fadmin%2Fcreate-okdoi-head%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fcreate-okdoi-head%2Froute.ts&appDir=C%3A%5CUsers%5Cdream%5CDesktop%5Cokdoi%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cdream%5CDesktop%5Cokdoi&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fadmin%2Fcreate-okdoi-head%2Froute&page=%2Fapi%2Fadmin%2Fcreate-okdoi-head%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fcreate-okdoi-head%2Froute.ts&appDir=C%3A%5CUsers%5Cdream%5CDesktop%5Cokdoi%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cdream%5CDesktop%5Cokdoi&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_dream_Desktop_okdoi_src_app_api_admin_create_okdoi_head_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/admin/create-okdoi-head/route.ts */ \"(rsc)/./src/app/api/admin/create-okdoi-head/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/admin/create-okdoi-head/route\",\n        pathname: \"/api/admin/create-okdoi-head\",\n        filename: \"route\",\n        bundlePath: \"app/api/admin/create-okdoi-head/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\api\\\\admin\\\\create-okdoi-head\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_dream_Desktop_okdoi_src_app_api_admin_create_okdoi_head_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/admin/create-okdoi-head/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fadmin%2Fcreate-okdoi-head%2Froute&page=%2Fapi%2Fadmin%2Fcreate-okdoi-head%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fcreate-okdoi-head%2Froute.ts&appDir=C%3A%5CUsers%5Cdream%5CDesktop%5Cokdoi%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cdream%5CDesktop%5Cokdoi&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/admin/create-okdoi-head/route.ts":
/*!******************************************************!*\
  !*** ./src/app/api/admin/create-okdoi-head/route.ts ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _supabase_ssr__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @supabase/ssr */ \"(rsc)/./node_modules/@supabase/ssr/dist/module/index.js\");\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n/* harmony import */ var _lib_services_referralSystem__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/services/referralSystem */ \"(rsc)/./src/lib/services/referralSystem.ts\");\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/supabase */ \"(rsc)/./src/lib/supabase.ts\");\n\n\n\n\n\nasync function POST(request) {\n    try {\n        // Create Supabase client for auth using SSR\n        const cookieStore = (0,next_headers__WEBPACK_IMPORTED_MODULE_2__.cookies)();\n        const supabaseAuth = (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_1__.createServerClient)(\"https://vnmydqbwjjufnxngpnqo.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InZubXlkcWJ3amp1Zm54bmdwbnFvIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTYyODkyNjgsImV4cCI6MjA3MTg2NTI2OH0.23oAdwSQ11jasIhrtZf71oeC6ehBsgTda1iCRU8myCo\", {\n            cookies: {\n                get (name) {\n                    return cookieStore.get(name)?.value;\n                }\n            }\n        });\n        // Get the current user\n        const { data: { user }, error: authError } = await supabaseAuth.auth.getUser();\n        if (authError || !user) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Unauthorized\"\n            }, {\n                status: 401\n            });\n        }\n        // Check if user is admin using admin client\n        const { data: userData, error: userError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_4__.supabaseAdmin.from(\"users\").select(\"role, is_super_admin\").eq(\"id\", user.id).single();\n        if (userError || !userData || userData.role !== \"admin\" && !userData.is_super_admin) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Forbidden - Admin access required\"\n            }, {\n                status: 403\n            });\n        }\n        // Check if OKDOI Head already exists\n        const existingHead = await _lib_services_referralSystem__WEBPACK_IMPORTED_MODULE_3__.ReferralSystemService.getOKDOIHead();\n        if (existingHead) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"OKDOI Head already exists\",\n                data: existingHead\n            }, {\n                status: 400\n            });\n        }\n        // Parse request body for custom details (optional)\n        const body = await request.json().catch(()=>({}));\n        const { email, fullName, phone, userId } = body;\n        let okdoiHead;\n        if (userId) {\n            // Assign OKDOI Head to existing user\n            okdoiHead = await _lib_services_referralSystem__WEBPACK_IMPORTED_MODULE_3__.ReferralSystemService.assignOKDOIHeadToUser(userId);\n        } else {\n            // Create new OKDOI Head user\n            okdoiHead = await _lib_services_referralSystem__WEBPACK_IMPORTED_MODULE_3__.ReferralSystemService.createOKDOIHead(email || \"<EMAIL>\", fullName || \"OKDOI Head\", phone);\n        }\n        console.log(`Admin ${user.email} created OKDOI Head: ${okdoiHead.email}`);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            message: \"OKDOI Head created successfully\",\n            data: okdoiHead\n        });\n    } catch (error) {\n        console.error(\"Error creating OKDOI Head:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Failed to create OKDOI Head\",\n            details: error instanceof Error ? error.message : \"Unknown error\"\n        }, {\n            status: 500\n        });\n    }\n}\nasync function GET(request) {\n    try {\n        // Create Supabase client for auth using SSR\n        const cookieStore = (0,next_headers__WEBPACK_IMPORTED_MODULE_2__.cookies)();\n        const supabaseAuth = (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_1__.createServerClient)(\"https://vnmydqbwjjufnxngpnqo.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InZubXlkcWJ3amp1Zm54bmdwbnFvIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTYyODkyNjgsImV4cCI6MjA3MTg2NTI2OH0.23oAdwSQ11jasIhrtZf71oeC6ehBsgTda1iCRU8myCo\", {\n            cookies: {\n                get (name) {\n                    return cookieStore.get(name)?.value;\n                }\n            }\n        });\n        // Get the current user\n        const { data: { user }, error: authError } = await supabaseAuth.auth.getUser();\n        if (authError || !user) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Unauthorized\"\n            }, {\n                status: 401\n            });\n        }\n        // Check if user is admin\n        const { data: userData, error: userError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_4__.supabaseAdmin.from(\"users\").select(\"role, is_super_admin\").eq(\"id\", user.id).single();\n        if (userError || !userData || userData.role !== \"admin\" && !userData.is_super_admin) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Forbidden - Admin access required\"\n            }, {\n                status: 403\n            });\n        }\n        // Get OKDOI Head user\n        const okdoiHead = await _lib_services_referralSystem__WEBPACK_IMPORTED_MODULE_3__.ReferralSystemService.getOKDOIHead();\n        if (okdoiHead) {\n            // Get referral stats for OKDOI Head to include total network size\n            const referralStats = await _lib_services_referralSystem__WEBPACK_IMPORTED_MODULE_3__.ReferralSystemService.getUserReferralStats(okdoiHead.id);\n            // Add total referrals to the OKDOI Head data\n            const okdoiHeadWithStats = {\n                ...okdoiHead,\n                totalReferrals: referralStats.totalReferrals\n            };\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                data: okdoiHeadWithStats\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: okdoiHead\n        });\n    } catch (error) {\n        console.error(\"Error getting OKDOI Head:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Failed to get OKDOI Head\",\n            details: error instanceof Error ? error.message : \"Unknown error\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/admin/create-okdoi-head/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/services/referralSystem.ts":
/*!********************************************!*\
  !*** ./src/lib/services/referralSystem.ts ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ReferralSystemService: () => (/* binding */ ReferralSystemService)\n/* harmony export */ });\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/supabase */ \"(rsc)/./src/lib/supabase.ts\");\n\n/**\n * ReferralSystemService - Manages the multi-level referral and commission system\n */ class ReferralSystemService {\n    /**\n   * Generate a unique referral code\n   */ static async generateReferralCode() {\n        const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.rpc(\"generate_referral_code\");\n        if (error) {\n            throw new Error(`Failed to generate referral code: ${error.message}`);\n        }\n        return data;\n    }\n    /**\n   * Validate referral code and get referrer information\n   */ static async validateReferralCode(code) {\n        try {\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.USERS).select(\"*\").eq(\"referral_code\", code).eq(\"is_referral_active\", true).single();\n            if (error) {\n                if (error.code === \"PGRST116\") {\n                    return null // Code not found\n                    ;\n                }\n                throw new Error(`Failed to validate referral code: ${error.message}`);\n            }\n            return data;\n        } catch (error) {\n            console.error(\"Error validating referral code:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Place user in referral hierarchy\n   */ static async placeUserInHierarchy(newUserId, referrerId) {\n        try {\n            const { error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.rpc(\"place_user_in_hierarchy\", {\n                new_user_id: newUserId,\n                referrer_id: referrerId\n            });\n            if (error) {\n                throw new Error(`Failed to place user in hierarchy: ${error.message}`);\n            }\n        } catch (error) {\n            console.error(\"Error placing user in hierarchy:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Get user's referral statistics\n   */ static async getUserReferralStats(userId) {\n        try {\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.USERS).select(\"direct_referrals_count, total_downline_count, total_commission_earned, referral_level, referral_code, user_type\").eq(\"id\", userId).single();\n            if (error) {\n                throw new Error(`Failed to get referral stats: ${error.message}`);\n            }\n            let referralCode = data.referral_code;\n            // If user doesn't have a referral code, generate one\n            if (!referralCode) {\n                referralCode = await this.generateReferralCode();\n                // Update the user with the new referral code\n                const { error: updateError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.USERS).update({\n                    referral_code: referralCode\n                }).eq(\"id\", userId);\n                if (updateError) {\n                    console.error(\"Error updating referral code:\", updateError);\n                // Don't throw error, just use the generated code\n                }\n            }\n            // Get actual direct referrals count from database (real-time)\n            const { count: actualDirectReferrals, error: directError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.USERS).select(\"*\", {\n                count: \"exact\",\n                head: true\n            }).eq(\"referred_by_id\", userId);\n            if (directError) {\n                console.error(\"Error counting direct referrals:\", directError);\n            }\n            // Calculate total referrals based on user type using real-time data\n            let totalReferrals = 0;\n            let actualTotalDownline = 0;\n            if (data.user_type === \"user\") {\n                // For regular users, limit to level 10\n                totalReferrals = await this.getTotalReferralsWithLevelLimit(userId, 10);\n                actualTotalDownline = totalReferrals;\n            } else {\n                // For ZM, RSM, and OKDOI Head, calculate from referral_hierarchy table (real-time)\n                const { count: totalDownlineCount, error: hierarchyError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"referral_hierarchy\").select(\"*\", {\n                    count: \"exact\",\n                    head: true\n                }).eq(\"ancestor_id\", userId);\n                if (hierarchyError) {\n                    console.error(\"Error counting total referrals from hierarchy:\", hierarchyError);\n                    totalReferrals = actualDirectReferrals || 0;\n                    actualTotalDownline = actualDirectReferrals || 0;\n                } else {\n                    totalReferrals = totalDownlineCount || 0;\n                    actualTotalDownline = totalDownlineCount || 0;\n                }\n            }\n            return {\n                directReferrals: actualDirectReferrals || 0,\n                totalDownline: actualTotalDownline,\n                totalCommissionEarned: parseFloat(data.total_commission_earned) || 0,\n                currentLevel: data.referral_level || 0,\n                totalReferrals: totalReferrals,\n                referralCode: referralCode\n            };\n        } catch (error) {\n            console.error(\"Error getting referral stats:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Get total referrals with level limit for regular users\n   */ static async getTotalReferralsWithLevelLimit(userId, maxLevel) {\n        try {\n            // Get all referrals within the level limit using referral_hierarchy\n            const { count, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.REFERRAL_HIERARCHY).select(\"*\", {\n                count: \"exact\",\n                head: true\n            }).eq(\"ancestor_id\", userId).lte(\"level_difference\", maxLevel);\n            if (error) {\n                console.error(\"Error counting referrals with level limit:\", error);\n                return 0;\n            }\n            return count || 0;\n        } catch (error) {\n            console.error(\"Error getting total referrals with level limit:\", error);\n            return 0;\n        }\n    }\n    /**\n   * Get user's direct referrals\n   */ static async getDirectReferrals(userId) {\n        try {\n            // First get the placement records\n            const { data: placements, error: placementError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.REFERRAL_PLACEMENTS).select(\"child_id, position, created_at\").eq(\"parent_id\", userId).order(\"position\");\n            if (placementError) {\n                throw new Error(`Failed to get referral placements: ${placementError.message}`);\n            }\n            if (!placements || placements.length === 0) {\n                return [];\n            }\n            // Get the user details for each child\n            const childIds = placements.map((p)=>p.child_id);\n            const { data: users, error: userError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.USERS).select(\"*\").in(\"id\", childIds);\n            if (userError) {\n                throw new Error(`Failed to get referral users: ${userError.message}`);\n            }\n            // Sort users by placement position\n            const sortedUsers = users?.sort((a, b)=>{\n                const positionA = placements.find((p)=>p.child_id === a.id)?.position || 0;\n                const positionB = placements.find((p)=>p.child_id === b.id)?.position || 0;\n                return positionA - positionB;\n            }) || [];\n            return sortedUsers;\n        } catch (error) {\n            console.error(\"Error getting direct referrals:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Get user's referral tree (up to specified depth)\n   */ static async getReferralTree(userId, maxDepth = 3) {\n        try {\n            // Get the root user\n            const { data: rootUser, error: rootError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.USERS).select(\"*\").eq(\"id\", userId).single();\n            if (rootError) {\n                throw new Error(`Failed to get root user: ${rootError.message}`);\n            }\n            // Build the tree recursively using referral_placements (tree structure)\n            const buildTree = async (user, currentDepth)=>{\n                const children = [];\n                if (currentDepth < maxDepth) {\n                    const { data: placements, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.REFERRAL_PLACEMENTS).select(`\n              position,\n              placement_type,\n              child:child_id(\n                *,\n                active_subscription:user_subscriptions!left(\n                  id,\n                  status,\n                  expires_at,\n                  package:subscription_packages(\n                    name,\n                    price,\n                    currency\n                  )\n                )\n              )\n            `).eq(\"parent_id\", user.id).order(\"position\");\n                    if (!error && placements) {\n                        for (const placement of placements){\n                            const childNode = await buildTree(placement.child, currentDepth + 1);\n                            childNode.position = placement.position;\n                            children.push(childNode);\n                        }\n                    }\n                }\n                return {\n                    user,\n                    children,\n                    level: currentDepth,\n                    position: 0 // Will be set by parent\n                };\n            };\n            return await buildTree(rootUser, 0);\n        } catch (error) {\n            console.error(\"Error getting referral tree:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Get user's commission transactions\n   */ static async getCommissionTransactions(userId, page = 1, limit = 20) {\n        try {\n            const offset = (page - 1) * limit;\n            const [dataResult, countResult] = await Promise.all([\n                _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.COMMISSION_TRANSACTIONS).select(\"*\").eq(\"beneficiary_id\", userId).order(\"created_at\", {\n                    ascending: false\n                }).range(offset, offset + limit - 1),\n                _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.COMMISSION_TRANSACTIONS).select(\"*\", {\n                    count: \"exact\",\n                    head: true\n                }).eq(\"beneficiary_id\", userId)\n            ]);\n            if (dataResult.error) {\n                throw new Error(`Failed to get commission transactions: ${dataResult.error.message}`);\n            }\n            if (countResult.error) {\n                throw new Error(`Failed to count commission transactions: ${countResult.error.message}`);\n            }\n            return {\n                transactions: dataResult.data || [],\n                total: countResult.count || 0\n            };\n        } catch (error) {\n            console.error(\"Error getting commission transactions:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Create Zonal Manager\n   */ static async createZonalManager(userId, zoneName, zoneDescription, assignedDistricts = [], createdBy) {\n        try {\n            // First update user type\n            const { error: userError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.USERS).update({\n                user_type: \"zonal_manager\"\n            }).eq(\"id\", userId);\n            if (userError) {\n                throw new Error(`Failed to update user type: ${userError.message}`);\n            }\n            // Create zonal manager record\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.ZONAL_MANAGERS).insert({\n                user_id: userId,\n                zone_name: zoneName,\n                zone_description: zoneDescription,\n                assigned_districts: assignedDistricts,\n                created_by: createdBy\n            }).select().single();\n            if (error) {\n                throw new Error(`Failed to create zonal manager: ${error.message}`);\n            }\n            return data;\n        } catch (error) {\n            console.error(\"Error creating zonal manager:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Upgrade user to RSM\n   */ static async upgradeToRSM(userId, zonalManagerId, regionName, upgradedBy) {\n        try {\n            // Validate that user exists and is eligible\n            const { data: userData, error: userFetchError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.USERS).select(\"user_type, referred_by_id\").eq(\"id\", userId).single();\n            if (userFetchError) {\n                throw new Error(`Failed to get user data: ${userFetchError.message}`);\n            }\n            if (userData.user_type !== \"user\") {\n                throw new Error(\"Only regular users can be upgraded to RSM\");\n            }\n            // If ZM is specified, validate the relationship\n            if (zonalManagerId) {\n                const { data: zmData, error: zmError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.ZONAL_MANAGERS).select(\"user_id\").eq(\"id\", zonalManagerId).single();\n                if (zmError) {\n                    throw new Error(`Failed to get ZM data: ${zmError.message}`);\n                }\n                // Check if user is in the ZM's network (not just direct downline)\n                const { data: hierarchyCheck, error: hierarchyError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.REFERRAL_HIERARCHY).select(\"id\").eq(\"user_id\", userId).eq(\"ancestor_id\", zmData.user_id).single();\n                if (hierarchyError && hierarchyError.code !== \"PGRST116\") {\n                    throw new Error(`Failed to check user hierarchy: ${hierarchyError.message}`);\n                }\n                if (!hierarchyCheck) {\n                    throw new Error(\"User must be in the network of the selected Zonal Manager\");\n                }\n                // Check if user is already an RSM under this ZM\n                const { data: existingRSM, error: rsmCheckError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.REGIONAL_SALES_MANAGERS).select(\"id\").eq(\"zonal_manager_id\", zonalManagerId).eq(\"user_id\", userId).eq(\"is_active\", true).single();\n                if (rsmCheckError && rsmCheckError.code !== \"PGRST116\") {\n                    throw new Error(`Failed to check existing RSM: ${rsmCheckError.message}`);\n                }\n                if (existingRSM) {\n                    throw new Error(\"This user is already an RSM under the selected Zonal Manager\");\n                }\n                // Check if the user's direct line already has an RSM\n                const { data: directReferrals, error: directError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.USERS).select(\"id\").eq(\"referred_by_id\", zmData.user_id);\n                if (directError) {\n                    throw new Error(`Failed to get direct referrals: ${directError.message}`);\n                }\n                const directReferralIds = directReferrals?.map((user)=>user.id) || [];\n                const userDirectLine = await ReferralSystemService.findDirectLineForUser(userId, zmData.user_id, directReferralIds);\n                if (!userDirectLine) {\n                    throw new Error(\"Could not determine user's direct line under the ZM\");\n                }\n                // Check if this direct line already has an RSM\n                const { data: existingRSMs, error: existingRSMError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.REGIONAL_SALES_MANAGERS).select(`\n            user_id,\n            user:user_id(referred_by_id)\n          `).eq(\"zonal_manager_id\", zonalManagerId).eq(\"is_active\", true);\n                if (existingRSMError) {\n                    throw new Error(`Failed to check existing RSMs: ${existingRSMError.message}`);\n                }\n                if (existingRSMs) {\n                    for (const rsm of existingRSMs){\n                        const rsmDirectLine = await ReferralSystemService.findDirectLineForUser(rsm.user_id, zmData.user_id, directReferralIds);\n                        if (rsmDirectLine === userDirectLine) {\n                            throw new Error(\"This direct line already has an RSM assigned\");\n                        }\n                    }\n                }\n            }\n            // Update user type\n            const { error: userError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.USERS).update({\n                user_type: \"rsm\"\n            }).eq(\"id\", userId);\n            if (userError) {\n                throw new Error(`Failed to update user type: ${userError.message}`);\n            }\n            // Create RSM record\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.REGIONAL_SALES_MANAGERS).insert({\n                user_id: userId,\n                zonal_manager_id: zonalManagerId,\n                region_name: regionName,\n                upgraded_by: upgradedBy\n            }).select().single();\n            if (error) {\n                throw new Error(`Failed to create RSM: ${error.message}`);\n            }\n            return data;\n        } catch (error) {\n            console.error(\"Error upgrading to RSM:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Get all Zonal Managers\n   */ static async getZonalManagers() {\n        try {\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.ZONAL_MANAGERS).select(`\n          *,\n          user:user_id(*)\n        `).order(\"created_at\", {\n                ascending: false\n            });\n            if (error) {\n                throw new Error(`Failed to get zonal managers: ${error.message}`);\n            }\n            return data || [];\n        } catch (error) {\n            console.error(\"Error getting zonal managers:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Get all Regional Sales Managers\n   */ static async getRegionalSalesManagers() {\n        try {\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.REGIONAL_SALES_MANAGERS).select(`\n          *,\n          user:user_id(*),\n          zonal_manager:zonal_manager_id(*)\n        `).order(\"created_at\", {\n                ascending: false\n            });\n            if (error) {\n                throw new Error(`Failed to get regional sales managers: ${error.message}`);\n            }\n            return data || [];\n        } catch (error) {\n            console.error(\"Error getting regional sales managers:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Search users for ZM/RSM creation\n   */ static async searchUsers(searchTerm, limit = 20) {\n        try {\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.USERS).select(\"*\").or(`full_name.ilike.%${searchTerm}%,email.ilike.%${searchTerm}%`).eq(\"user_type\", \"user\") // Only regular users can be upgraded\n            .limit(limit);\n            if (error) {\n                throw new Error(`Failed to search users: ${error.message}`);\n            }\n            return data || [];\n        } catch (error) {\n            console.error(\"Error searching users:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Get eligible users for RSM upgrade under a specific ZM\n   * Rules:\n   * - Must be in the ZM's entire network (not just direct referrals)\n   * - Must not already be RSM or ZM\n   * - ZM can have only 1 RSM per direct downline (direct referral line)\n   */ static async getEligibleUsersForRSMUpgrade(zonalManagerId) {\n        try {\n            // Validate input\n            if (!zonalManagerId || typeof zonalManagerId !== \"string\") {\n                throw new Error(\"Invalid zonal manager ID provided\");\n            }\n            // First get the ZM's user_id (use admin client for RLS bypass)\n            const { data: zmData, error: zmError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.ZONAL_MANAGERS).select(\"user_id\").eq(\"id\", zonalManagerId).single();\n            if (zmError) {\n                console.error(\"ZM data query error:\", zmError);\n                throw new Error(`Failed to get ZM data: ${zmError.message}`);\n            }\n            if (!zmData || !zmData.user_id) {\n                throw new Error(\"Zonal Manager not found or invalid\");\n            }\n            // Get all users in the ZM's network using referral_hierarchy (use admin client for RLS bypass)\n            // Use inner join to ensure we only get users that actually exist\n            const { data: networkUsers, error: networkError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.REFERRAL_HIERARCHY).select(`\n          user_id,\n          level_difference,\n          user:user_id!inner(\n            id,\n            email,\n            full_name,\n            user_type,\n            referred_by_id\n          )\n        `).eq(\"ancestor_id\", zmData.user_id).order(\"level_difference\", {\n                ascending: true\n            });\n            if (networkError) {\n                console.error(\"Network users query error:\", networkError);\n                throw new Error(`Failed to get network users: ${networkError.message}`);\n            }\n            console.log(`Found ${networkUsers?.length || 0} network users for ZM ${zmData.user_id}`);\n            // Validate network users data\n            if (!networkUsers || !Array.isArray(networkUsers)) {\n                console.warn(\"No network users found or invalid data structure\");\n                return [];\n            }\n            // Debug: Log any null user entries and filter them out\n            const validNetworkUsers = networkUsers.filter((nu)=>{\n                if (!nu || !nu.user || !nu.user.id) {\n                    console.warn(\"Filtering out network user with null/invalid user data:\", nu);\n                    return false;\n                }\n                return true;\n            });\n            console.log(`Valid network users after filtering: ${validNetworkUsers.length}`);\n            if (validNetworkUsers.length === 0) {\n                console.log(\"No valid network users found after filtering\");\n                return [];\n            }\n            // Get direct referrals of ZM to understand direct lines (use admin client for RLS bypass)\n            const { data: directReferrals, error: directError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.USERS).select(\"id\").eq(\"referred_by_id\", zmData.user_id);\n            if (directError) {\n                console.error(\"Direct referrals query error:\", directError);\n                throw new Error(`Failed to get direct referrals: ${directError.message}`);\n            }\n            const directReferralIds = directReferrals?.map((user)=>user?.id).filter(Boolean) || [];\n            console.log(`Found ${directReferralIds.length} direct referrals for ZM`);\n            // Get existing RSMs under this ZM to check which direct lines already have RSMs (use admin client for RLS bypass)\n            const { data: existingRSMs, error: rsmError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.REGIONAL_SALES_MANAGERS).select(`\n          user_id,\n          user:user_id(referred_by_id)\n        `).eq(\"zonal_manager_id\", zonalManagerId).eq(\"is_active\", true);\n            if (rsmError) {\n                console.error(\"Error getting existing RSMs:\", rsmError);\n            // Don't throw here, just log and continue with empty array\n            }\n            // Find which direct lines already have RSMs\n            const directLinesWithRSM = new Set();\n            if (existingRSMs && Array.isArray(existingRSMs)) {\n                for (const rsm of existingRSMs){\n                    try {\n                        if (rsm && rsm.user_id) {\n                            // Find which direct line this RSM belongs to\n                            const rsmDirectLine = await ReferralSystemService.findDirectLineForUser(rsm.user_id, zmData.user_id, directReferralIds);\n                            if (rsmDirectLine) {\n                                directLinesWithRSM.add(rsmDirectLine);\n                            }\n                        }\n                    } catch (error) {\n                        console.error(\"Error processing existing RSM:\", rsm, error);\n                    // Continue processing other RSMs\n                    }\n                }\n            }\n            // Filter eligible users\n            const eligibleUsers = [];\n            const existingRSMUserIds = existingRSMs?.map((rsm)=>rsm?.user_id).filter(Boolean) || [];\n            console.log(`Processing ${validNetworkUsers.length} network users for eligibility`);\n            for (const networkUser of validNetworkUsers){\n                try {\n                    const user = networkUser.user;\n                    // Double-check user data validity (already filtered above, but being extra safe)\n                    if (!user || !user.id || typeof user.id !== \"string\") {\n                        console.warn(\"Skipping network user with invalid user data:\", networkUser);\n                        continue;\n                    }\n                    // Skip if already RSM or ZM - only regular users can be upgraded\n                    if (existingRSMUserIds.includes(user.id) || user.user_type !== \"user\") {\n                        console.log(`Skipping user ${user.id} - already RSM/ZM or not regular user (type: ${user.user_type})`);\n                        continue;\n                    }\n                    // Find which direct line this user belongs to\n                    const userDirectLine = await ReferralSystemService.findDirectLineForUser(user.id, zmData.user_id, directReferralIds);\n                    // Only include if the direct line doesn't already have an RSM\n                    if (userDirectLine && !directLinesWithRSM.has(userDirectLine)) {\n                        // Ensure user has required fields for display\n                        const eligibleUser = {\n                            ...user,\n                            full_name: user.full_name || \"N/A\",\n                            email: user.email || \"N/A\"\n                        };\n                        eligibleUsers.push(eligibleUser);\n                        console.log(`Added eligible user: ${eligibleUser.id} (${eligibleUser.full_name})`);\n                    } else {\n                        console.log(`Skipping user ${user.id} - direct line ${userDirectLine} already has RSM or user not in direct line`);\n                    }\n                } catch (error) {\n                    console.error(\"Error processing network user:\", networkUser, error);\n                    continue;\n                }\n            }\n            console.log(`Found ${eligibleUsers.length} eligible users for RSM upgrade`);\n            return eligibleUsers;\n        } catch (error) {\n            console.error(\"Error getting eligible users for RSM upgrade:\", error);\n            // Return empty array instead of throwing to prevent UI crashes\n            return [];\n        }\n    }\n    /**\n   * Helper method to find which direct line a user belongs to under a ZM\n   */ static async findDirectLineForUser(userId, zmUserId, directReferralIds) {\n        try {\n            // Validate inputs\n            if (!userId || !zmUserId || !Array.isArray(directReferralIds)) {\n                console.warn(\"Invalid parameters for findDirectLineForUser:\", {\n                    userId,\n                    zmUserId,\n                    directReferralIds\n                });\n                return null;\n            }\n            // Get the user's referral path or trace upwards (use admin client for RLS bypass)\n            const { data: userData, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.USERS).select(\"referral_path, referred_by_id\").eq(\"id\", userId).single();\n            if (error) {\n                console.warn(`Error getting user data for ${userId}:`, error);\n                return null;\n            }\n            if (!userData) {\n                console.warn(`No user data found for ${userId}`);\n                return null;\n            }\n            // If user is a direct referral of ZM\n            if (userData.referred_by_id === zmUserId) {\n                return userId;\n            }\n            // Parse referral path to find the direct line\n            if (userData.referral_path && typeof userData.referral_path === \"string\") {\n                try {\n                    const pathIds = userData.referral_path.split(\",\").filter(Boolean);\n                    const zmIndex = pathIds.indexOf(zmUserId);\n                    if (zmIndex !== -1 && zmIndex < pathIds.length - 1) {\n                        const directLineId = pathIds[zmIndex + 1];\n                        if (directReferralIds.includes(directLineId)) {\n                            return directLineId;\n                        }\n                    }\n                } catch (pathError) {\n                    console.warn(\"Error parsing referral path:\", userData.referral_path, pathError);\n                }\n            }\n            // Fallback: trace upwards through referral hierarchy\n            let currentUserId = userId;\n            let currentReferredBy = userData.referred_by_id;\n            let iterations = 0;\n            const maxIterations = 20 // Prevent infinite loops\n            ;\n            while(currentReferredBy && currentReferredBy !== zmUserId && iterations < maxIterations){\n                iterations++;\n                try {\n                    const { data: parentData, error: parentError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.USERS).select(\"referred_by_id\").eq(\"id\", currentReferredBy).single();\n                    if (parentError || !parentData) {\n                        console.warn(`Error or no data for parent user ${currentReferredBy}:`, parentError);\n                        break;\n                    }\n                    currentUserId = currentReferredBy;\n                    currentReferredBy = parentData.referred_by_id;\n                } catch (parentError) {\n                    console.warn(\"Error querying parent user:\", parentError);\n                    break;\n                }\n            }\n            if (iterations >= maxIterations) {\n                console.warn(`Max iterations reached while tracing referral hierarchy for user ${userId}`);\n            }\n            // If we reached the ZM, the current user is the direct line\n            if (currentReferredBy === zmUserId && directReferralIds.includes(currentUserId)) {\n                return currentUserId;\n            }\n            return null;\n        } catch (error) {\n            console.error(\"Error finding direct line for user:\", {\n                userId,\n                zmUserId,\n                error\n            });\n            return null;\n        }\n    }\n    /**\n   * Get referral system statistics\n   */ static async getReferralSystemStats() {\n        try {\n            const [totalUsersResult, zonalManagersResult, regionalManagersResult, totalReferralsResult, activeCodesResult] = await Promise.all([\n                _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.USERS).select(\"*\", {\n                    count: \"exact\",\n                    head: true\n                }),\n                _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.ZONAL_MANAGERS).select(\"*\", {\n                    count: \"exact\",\n                    head: true\n                }).eq(\"is_active\", true),\n                _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.REGIONAL_SALES_MANAGERS).select(\"*\", {\n                    count: \"exact\",\n                    head: true\n                }).eq(\"is_active\", true),\n                _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.REFERRAL_PLACEMENTS).select(\"*\", {\n                    count: \"exact\",\n                    head: true\n                }),\n                _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.USERS).select(\"*\", {\n                    count: \"exact\",\n                    head: true\n                }).not(\"referral_code\", \"is\", null)\n            ]);\n            return {\n                totalUsers: totalUsersResult.count || 0,\n                zonalManagers: zonalManagersResult.count || 0,\n                regionalManagers: regionalManagersResult.count || 0,\n                totalReferrals: totalReferralsResult.count || 0,\n                activeReferralCodes: activeCodesResult.count || 0\n            };\n        } catch (error) {\n            console.error(\"Error getting referral system stats:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Deactivate Zonal Manager\n   */ static async deactivateZonalManager(zmId) {\n        try {\n            const { error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.ZONAL_MANAGERS).update({\n                is_active: false\n            }).eq(\"id\", zmId);\n            if (error) {\n                throw new Error(`Failed to deactivate zonal manager: ${error.message}`);\n            }\n        } catch (error) {\n            console.error(\"Error deactivating zonal manager:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Deactivate Regional Sales Manager\n   */ static async deactivateRSM(rsmId) {\n        try {\n            const { error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.REGIONAL_SALES_MANAGERS).update({\n                is_active: false\n            }).eq(\"id\", rsmId);\n            if (error) {\n                throw new Error(`Failed to deactivate RSM: ${error.message}`);\n            }\n        } catch (error) {\n            console.error(\"Error deactivating RSM:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Create or get OKDOI Head user\n   */ static async createOKDOIHead(email = \"<EMAIL>\", fullName = \"OKDOI Head\", phone) {\n        try {\n            // Check if OKDOI Head already exists using admin client\n            const { data: existingHead, error: checkError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.USERS).select(\"*\").eq(\"user_type\", \"okdoi_head\").single();\n            if (existingHead && !checkError) {\n                return existingHead;\n            }\n            // Generate referral code first\n            const referralCode = await this.generateReferralCode();\n            // Create auth user first using admin auth API\n            const { data: authUser, error: authError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.auth.admin.createUser({\n                email,\n                password: crypto.randomUUID(),\n                email_confirm: true,\n                user_metadata: {\n                    full_name: fullName,\n                    phone: phone || null\n                }\n            });\n            if (authError || !authUser.user) {\n                throw new Error(`Failed to create auth user: ${authError?.message}`);\n            }\n            // Create OKDOI Head user in public.users table using the auth user ID\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.USERS).insert({\n                id: authUser.user.id,\n                email,\n                full_name: fullName,\n                phone,\n                user_type: \"okdoi_head\",\n                role: \"admin\",\n                is_verified: true,\n                is_referral_active: true,\n                referral_level: 0,\n                referral_path: \"\",\n                direct_referrals_count: 0,\n                total_downline_count: 0,\n                total_commission_earned: 0,\n                referral_code: referralCode,\n                created_at: new Date().toISOString(),\n                updated_at: new Date().toISOString()\n            }).select().single();\n            if (error) {\n                // If user creation fails, clean up the auth user\n                await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.auth.admin.deleteUser(authUser.user.id);\n                throw new Error(`Failed to create OKDOI Head: ${error.message}`);\n            }\n            return data;\n        } catch (error) {\n            console.error(\"Error creating OKDOI Head:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Assign OKDOI Head role to existing user\n   */ static async assignOKDOIHeadToUser(userId) {\n        try {\n            // Check if OKDOI Head already exists\n            const existingHead = await this.getOKDOIHead();\n            if (existingHead) {\n                throw new Error(\"OKDOI Head already exists. Only one OKDOI Head is allowed.\");\n            }\n            // Get the user to be assigned\n            const { data: user, error: userError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.USERS).select(\"*\").eq(\"id\", userId).single();\n            if (userError || !user) {\n                throw new Error(\"User not found\");\n            }\n            // Generate referral code if user doesn't have one\n            let referralCode = user.referral_code;\n            if (!referralCode) {\n                referralCode = await this.generateReferralCode();\n            }\n            // Update user to OKDOI Head\n            const { data: updatedUser, error: updateError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.USERS).update({\n                user_type: \"okdoi_head\",\n                role: \"admin\",\n                is_verified: true,\n                is_referral_active: true,\n                referral_level: 0,\n                referral_path: \"\",\n                direct_referrals_count: 0,\n                total_downline_count: 0,\n                total_commission_earned: 0,\n                referral_code: referralCode,\n                updated_at: new Date().toISOString()\n            }).eq(\"id\", userId).select().single();\n            if (updateError) {\n                throw new Error(`Failed to assign OKDOI Head: ${updateError.message}`);\n            }\n            return updatedUser;\n        } catch (error) {\n            console.error(\"Error assigning OKDOI Head:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Get OKDOI Head user\n   */ static async getOKDOIHead() {\n        try {\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.USERS).select(\"*\").eq(\"user_type\", \"okdoi_head\").single();\n            if (error) {\n                if (error.code === \"PGRST116\") {\n                    return null // No OKDOI Head found\n                    ;\n                }\n                throw new Error(`Failed to get OKDOI Head: ${error.message}`);\n            }\n            return data;\n        } catch (error) {\n            console.error(\"Error getting OKDOI Head:\", error);\n            throw error;\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/services/referralSystem.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/supabase.ts":
/*!*****************************!*\
  !*** ./src/lib/supabase.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TABLES: () => (/* binding */ TABLES),\n/* harmony export */   supabase: () => (/* binding */ supabase),\n/* harmony export */   supabaseAdmin: () => (/* binding */ supabaseAdmin)\n/* harmony export */ });\n/* harmony import */ var _supabase_ssr__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/ssr */ \"(rsc)/./node_modules/@supabase/ssr/dist/module/index.js\");\n/* harmony import */ var _barrel_optimize_names_createClient_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! __barrel_optimize__?names=createClient!=!@supabase/supabase-js */ \"(rsc)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n\n\n// Environment variables with validation\nconst supabaseUrl = \"https://vnmydqbwjjufnxngpnqo.supabase.co\";\nconst supabaseAnonKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InZubXlkcWJ3amp1Zm54bmdwbnFvIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTYyODkyNjgsImV4cCI6MjA3MTg2NTI2OH0.23oAdwSQ11jasIhrtZf71oeC6ehBsgTda1iCRU8myCo\";\nconst supabaseServiceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;\n// Validate required environment variables\nif (!supabaseUrl) {\n    console.error(\"Missing NEXT_PUBLIC_SUPABASE_URL environment variable\");\n    throw new Error(\"Missing NEXT_PUBLIC_SUPABASE_URL environment variable\");\n}\nif (!supabaseAnonKey) {\n    console.error(\"Missing NEXT_PUBLIC_SUPABASE_ANON_KEY environment variable\");\n    throw new Error(\"Missing NEXT_PUBLIC_SUPABASE_ANON_KEY environment variable\");\n}\nif (!supabaseServiceRoleKey) {\n    console.warn(\"Missing SUPABASE_SERVICE_ROLE_KEY environment variable - admin functions will not work\");\n}\n// Create browser client with error handling\nlet supabase;\ntry {\n    supabase = (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createBrowserClient)(supabaseUrl, supabaseAnonKey, {\n        cookies: {\n            get (name) {\n                if (typeof document !== \"undefined\") {\n                    const value = document.cookie.split(\"; \").find((row)=>row.startsWith(`${name}=`))?.split(\"=\")[1];\n                    return value ? decodeURIComponent(value) : undefined;\n                }\n                return undefined;\n            },\n            set (name, value, options) {\n                if (typeof document !== \"undefined\") {\n                    let cookieString = `${name}=${encodeURIComponent(value)}`;\n                    if (options?.maxAge) cookieString += `; max-age=${options.maxAge}`;\n                    if (options?.path) cookieString += `; path=${options.path}`;\n                    if (options?.domain) cookieString += `; domain=${options.domain}`;\n                    if (options?.secure) cookieString += \"; secure\";\n                    if (options?.httpOnly) cookieString += \"; httponly\";\n                    if (options?.sameSite) cookieString += `; samesite=${options.sameSite}`;\n                    document.cookie = cookieString;\n                }\n            },\n            remove (name, options) {\n                if (typeof document !== \"undefined\") {\n                    let cookieString = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 GMT`;\n                    if (options?.path) cookieString += `; path=${options.path}`;\n                    if (options?.domain) cookieString += `; domain=${options.domain}`;\n                    document.cookie = cookieString;\n                }\n            }\n        }\n    });\n} catch (error) {\n    console.error(\"Failed to create Supabase browser client:\", error);\n    // Fallback to basic client without SSR\n    supabase = (0,_barrel_optimize_names_createClient_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_1__.createClient)(supabaseUrl, supabaseAnonKey);\n}\n// Admin client with service role key for bypassing RLS\n// Note: This will be null on client-side for security reasons\nconst supabaseAdmin = supabaseServiceRoleKey ? (0,_barrel_optimize_names_createClient_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_1__.createClient)(supabaseUrl, supabaseServiceRoleKey, {\n    auth: {\n        autoRefreshToken: false,\n        persistSession: false\n    }\n}) : null;\n\n// Database table names\nconst TABLES = {\n    CATEGORIES: \"categories\",\n    SUBCATEGORIES: \"subcategories\",\n    ADS: \"ads\",\n    USERS: \"users\",\n    AD_IMAGES: \"ad_images\",\n    DISTRICTS: \"districts\",\n    CITIES: \"cities\",\n    USER_FAVORITES: \"user_favorites\",\n    VENDOR_SHOPS: \"vendor_shops\",\n    SHOP_PRODUCTS: \"shop_products\",\n    SHOP_PRODUCT_IMAGES: \"shop_product_images\",\n    SHOP_REVIEWS: \"shop_reviews\",\n    SHOP_FOLLOWERS: \"shop_followers\",\n    SHOP_CATEGORIES: \"shop_categories\",\n    SHOP_SUBCATEGORIES: \"shop_subcategories\",\n    PRODUCT_REVIEWS: \"product_reviews\",\n    CHAT_CONVERSATIONS: \"chat_conversations\",\n    CHAT_MESSAGES: \"chat_messages\",\n    USER_WALLETS: \"user_wallets\",\n    WALLET_TRANSACTIONS: \"wallet_transactions\",\n    P2P_TRANSFERS: \"p2p_transfers\",\n    DEPOSIT_REQUESTS: \"deposit_requests\",\n    WITHDRAWAL_REQUESTS: \"withdrawal_requests\",\n    SUBSCRIPTION_PACKAGES: \"subscription_packages\",\n    USER_SUBSCRIPTIONS: \"user_subscriptions\",\n    AD_BOOSTS: \"ad_boosts\",\n    BOOST_PACKAGES: \"boost_packages\",\n    // Order Management System\n    CART_ITEMS: \"cart_items\",\n    SHOP_ORDERS: \"shop_orders\",\n    ORDER_ITEMS: \"order_items\",\n    ORDER_STATUS_HISTORY: \"order_status_history\",\n    // Merchant Wallet System\n    MERCHANT_WALLETS: \"merchant_wallets\",\n    MERCHANT_WALLET_TRANSACTIONS: \"merchant_wallet_transactions\",\n    MERCHANT_TO_MAIN_TRANSFERS: \"merchant_to_main_transfers\",\n    // Referral & Commission System\n    REFERRAL_HIERARCHY: \"referral_hierarchy\",\n    COMMISSION_STRUCTURE: \"commission_structure\",\n    COMMISSION_TRANSACTIONS: \"commission_transactions\",\n    REFERRAL_PLACEMENTS: \"referral_placements\",\n    // KYC System\n    KYC_SUBMISSIONS: \"kyc_submissions\",\n    KYC_STATUS_HISTORY: \"kyc_status_history\",\n    KYC_DOCUMENT_TYPES: \"kyc_document_types\",\n    ZONAL_MANAGERS: \"zonal_managers\",\n    REGIONAL_SALES_MANAGERS: \"regional_sales_managers\"\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/supabase.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/whatwg-url","vendor-chunks/cookie","vendor-chunks/webidl-conversions"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fadmin%2Fcreate-okdoi-head%2Froute&page=%2Fapi%2Fadmin%2Fcreate-okdoi-head%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fcreate-okdoi-head%2Froute.ts&appDir=C%3A%5CUsers%5Cdream%5CDesktop%5Cokdoi%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cdream%5CDesktop%5Cokdoi&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();