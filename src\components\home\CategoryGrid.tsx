'use client'

import { useMemo, memo } from 'react'
import Link from 'next/link'
import {
  Home,
  Car,
  Laptop,
  Smartphone,
  Sofa,
  Wrench,
  Building,
  Briefcase,
  Gamepad2,
  Heart,
  Shirt,
  GraduationCap,
  ShoppingCart,
  MoreHorizontal
} from 'lucide-react'
import Card from '@/components/ui/card'
import { CategoryGridSkeleton } from '@/components/ui/SkeletonLoader'
import { Category } from '@/types'
import { useCategories } from '@/hooks/useCategories'

// Icon mapping for categories
const categoryIcons: Record<string, any> = {
  'property': Home,
  'vehicles': Car,
  'electronics': Laptop,
  'mobiles': Smartphone,
  'home-garden': Sofa,
  'services': Wrench,
  'business-industry': Building,
  'jobs': Briefcase,
  'hobby-sport-kids': Gamepad2,
  'animals': Heart,
  'fashion-beauty': Shirt,
  'education': GraduationCap,
  'essentials': ShoppingCart,
  'other': MoreHorizontal,
}

const CategoryGrid = memo(function CategoryGrid() {
  const { data: categories, isLoading: loading, error, refetch } = useCategories()

  // Memoize the category items to prevent unnecessary re-renders
  const categoryItems = useMemo(() => {
    if (!categories || categories.length === 0) return []

    return categories.slice(0, 8).map((category) => {
      const IconComponent = categoryIcons[category.slug] || MoreHorizontal
      return {
        id: category.id,
        name: category.name,
        slug: category.slug,
        IconComponent
      }
    })
  }, [categories])

  if (loading) {
    return <CategoryGridSkeleton />
  }

  if (error) {
    return (
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-8">
              Popular Categories
            </h2>
            <div className="bg-red-50 border border-red-200 rounded-lg p-8 max-w-md mx-auto">
              <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Failed to Load Popular Categories</h3>
              <p className="text-red-600 mb-4">{error.message}</p>
              <button
                onClick={() => refetch()}
                disabled={loading}
                className="inline-flex items-center px-4 py-2 bg-primary-blue text-white rounded-lg hover:bg-primary-blue/90 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                {loading ? (
                  <>
                    <svg className="animate-spin -ml-1 mr-3 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Retrying...
                  </>
                ) : (
                  'Try Again'
                )}
              </button>
            </div>
          </div>
        </div>
      </section>
    )
  }

  // Show empty state if no categories
  if (!categories || categories.length === 0) {
    return (
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
              Popular Categories
            </h2>
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6 max-w-md mx-auto">
              <p className="text-yellow-800 mb-4">No categories available at the moment.</p>
              <button
                onClick={() => refetch()}
                className="px-4 py-2 bg-primary-blue text-white rounded-lg hover:bg-primary-blue/90 transition-colors"
              >
                Refresh
              </button>
            </div>
          </div>
        </div>
      </section>
    )
  }

  return (
    <section className="py-16 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
            Popular Categories
          </h2>
        </div>

        <div className="grid grid-cols-2 md:grid-cols-4 gap-6 max-w-4xl mx-auto">
          {categoryItems.map((category, index) => (
            <div
              key={category.id}
              className="animate-in fade-in slide-in-from-bottom-4 duration-700"
              style={{ animationDelay: `${index * 100}ms` }}
            >
              <Link href={`/category/${category.slug}`}>
                <div className="group cursor-pointer">
                  <div className="bg-gray-50 rounded-2xl p-8 hover:bg-gray-100 transition-all duration-300 text-center">
                    <div className="flex flex-col items-center">
                      <div className="w-16 h-16 flex items-center justify-center mb-4">
                        <category.IconComponent className="h-12 w-12 text-pink-500 group-hover:scale-110 transition-transform duration-300" />
                      </div>
                      <h3 className="text-lg font-medium text-gray-900 group-hover:text-pink-500 transition-colors duration-300">
                        {category.name}
                      </h3>
                    </div>
                  </div>
                </div>
              </Link>
            </div>
          ))}
        </div>


      </div>
    </section>
  )
})

export default CategoryGrid
