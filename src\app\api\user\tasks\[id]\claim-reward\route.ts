import { NextRequest, NextResponse } from 'next/server'
import { GiftTasksService } from '@/lib/services/giftTasksService'
import { cookies } from 'next/headers'
import { createServerClient } from '@supabase/ssr'

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Create Supabase client for auth using SSR
    const cookieStore = cookies()
    const supabaseAuth = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value
          },
        },
      }
    )

    // Get the current user
    const { data: { user }, error: authError } = await supabaseAuth.auth.getUser()

    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const userId = user.id
    const taskId = params.id

    const giftTransaction = await GiftTasksService.claimTaskReward(userId, taskId)

    return NextResponse.json({
      success: true,
      message: '<PERSON><PERSON> claimed successfully',
      data: giftTransaction
    })
  } catch (error) {
    console.error('POST /api/user/tasks/[id]/claim-reward error:', error)
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to claim reward'
      },
      { status: 500 }
    )
  }
}