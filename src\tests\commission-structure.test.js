/**
 * Comprehensive Commission Structure Tests
 * Tests all aspects of the commission structure system
 */

// Test Configuration
const BASE_URL = 'http://localhost:3000'
const API_BASE = `${BASE_URL}/api/admin`

// Test Data
const TEST_PACKAGES = [
  { value: 2000, name: 'Starter Package' },
  { value: 5000, name: 'Business Package' },
  { value: 10000, name: 'Professional Package' },
  { value: 15000, name: 'Enterprise Package' }
]

const EXPECTED_COMMISSION_RATES = {
  2000: {
    direct_commission_rate: 0.10,
    level_commission_rate: 0.02,
    present_user_rate: 0.025,
    present_leader_rate: 0.01,
    annual_present_user_rate: 0.01,
    annual_present_leader_rate: 0.025
  },
  5000: {
    direct_commission_rate: 0.25,
    level_commission_rate: 0.02,
    present_user_rate: 0.02,
    present_leader_rate: 0.0075,
    annual_present_user_rate: 0.0075,
    annual_present_leader_rate: 0.02
  },
  10000: {
    direct_commission_rate: 0.25,
    level_commission_rate: 0.02,
    present_user_rate: 0.02,
    present_leader_rate: 0.0125,
    annual_present_user_rate: 0.0125,
    annual_present_leader_rate: 0.02
  },
  15000: {
    direct_commission_rate: 0.30,
    level_commission_rate: 0.02,
    present_user_rate: 0.025,
    present_leader_rate: 0.015,
    annual_present_user_rate: 0.015,
    annual_present_leader_rate: 0.025
  }
}

// Test Results Storage
let testResults = {
  passed: 0,
  failed: 0,
  total: 0,
  details: []
}

// Utility Functions
function logTest(testName, passed, details = '') {
  testResults.total++
  if (passed) {
    testResults.passed++
    console.log(`✅ ${testName}`)
  } else {
    testResults.failed++
    console.log(`❌ ${testName} - ${details}`)
  }
  testResults.details.push({ testName, passed, details })
}

async function makeRequest(url, options = {}) {
  try {
    const response = await fetch(url, options)
    const data = await response.json()
    return { success: response.ok, data, status: response.status }
  } catch (error) {
    return { success: false, error: error.message, status: 0 }
  }
}

// Test Suite 1: API Endpoints Testing
async function testAPIEndpoints() {
  console.log('\n🧪 Testing API Endpoints...')
  
  // Test 1: Unified Commission Structure API
  const unifiedResponse = await makeRequest(`${API_BASE}/unified-commission-structure`)
  logTest(
    'Unified Commission Structure API responds',
    unifiedResponse.success && unifiedResponse.data.success,
    unifiedResponse.error || 'API not responding correctly'
  )
  
  if (unifiedResponse.success && unifiedResponse.data.success) {
    const structures = unifiedResponse.data.data
    
    // Test 2: Correct number of structures
    logTest(
      'Returns correct number of structures (4)',
      structures.length === 4,
      `Expected 4, got ${structures.length}`
    )
    
    // Test 3: All expected packages present
    const packageValues = structures.map(s => s.package_value).sort((a, b) => a - b)
    const expectedValues = [2000, 5000, 10000, 15000]
    logTest(
      'All expected package values present',
      JSON.stringify(packageValues) === JSON.stringify(expectedValues),
      `Expected ${expectedValues}, got ${packageValues}`
    )
    
    // Test 4: Commission rates accuracy
    for (const structure of structures) {
      const expected = EXPECTED_COMMISSION_RATES[structure.package_value]
      if (expected) {
        const ratesMatch = Object.keys(expected).every(key => 
          Math.abs(structure[key] - expected[key]) < 0.0001
        )
        logTest(
          `Package ${structure.package_value} commission rates are correct`,
          ratesMatch,
          `Rates don't match expected values`
        )
      }
    }
  }
  
  // Test 5: Available Packages API
  const packagesResponse = await makeRequest(`${API_BASE}/available-packages`)
  logTest(
    'Available Packages API responds',
    packagesResponse.success && packagesResponse.data.success,
    packagesResponse.error || 'API not responding correctly'
  )
  
  if (packagesResponse.success && packagesResponse.data.success) {
    const data = packagesResponse.data.data
    
    // Test 6: Package statistics
    logTest(
      'Package statistics are correct',
      data.total_packages === 4 && data.structured_count === 4 && data.available_count === 0,
      `Expected total:4, structured:4, available:0, got total:${data.total_packages}, structured:${data.structured_count}, available:${data.available_count}`
    )
  }
}

// Test Suite 2: Database Integrity Testing
async function testDatabaseIntegrity() {
  console.log('\n🗄️ Testing Database Integrity...')
  
  // This would require direct database access
  // For now, we'll test through API responses
  
  const response = await makeRequest(`${API_BASE}/unified-commission-structure`)
  if (response.success && response.data.success) {
    const structures = response.data.data
    
    // Test 7: All structures have required fields
    for (const structure of structures) {
      const requiredFields = [
        'id', 'commission_type', 'package_value', 'direct_commission_rate',
        'level_commission_rate', 'present_user_rate', 'present_leader_rate',
        'annual_present_user_rate', 'annual_present_leader_rate', 'is_active'
      ]
      
      const hasAllFields = requiredFields.every(field => structure.hasOwnProperty(field))
      logTest(
        `Package ${structure.package_value} has all required fields`,
        hasAllFields,
        `Missing fields: ${requiredFields.filter(f => !structure.hasOwnProperty(f)).join(', ')}`
      )
    }
    
    // Test 8: All structures are active
    const allActive = structures.every(s => s.is_active === true)
    logTest(
      'All commission structures are active',
      allActive,
      'Some structures are inactive'
    )
    
    // Test 9: Commission type consistency
    const allUnified = structures.every(s => s.commission_type === 'unified_structure')
    logTest(
      'All structures have unified_structure type',
      allUnified,
      'Some structures have different commission types'
    )
  }
}

// Test Suite 3: Commission Calculation Testing
async function testCommissionCalculations() {
  console.log('\n🧮 Testing Commission Calculations...')
  
  const response = await makeRequest(`${API_BASE}/unified-commission-structure`)
  if (response.success && response.data.success) {
    const structures = response.data.data
    
    // Test 10: Commission rate ranges are valid
    for (const structure of structures) {
      const rates = [
        structure.direct_commission_rate,
        structure.level_commission_rate,
        structure.present_user_rate,
        structure.present_leader_rate,
        structure.annual_present_user_rate,
        structure.annual_present_leader_rate
      ]
      
      const validRates = rates.every(rate => rate >= 0 && rate <= 1)
      logTest(
        `Package ${structure.package_value} has valid commission rates (0-100%)`,
        validRates,
        'Some rates are outside valid range'
      )
    }
    
    // Test 11: Higher packages have higher direct commission rates
    const sortedByPackage = structures.sort((a, b) => a.package_value - b.package_value)
    let directRatesIncreasing = true
    for (let i = 1; i < sortedByPackage.length; i++) {
      if (sortedByPackage[i].direct_commission_rate < sortedByPackage[i-1].direct_commission_rate) {
        directRatesIncreasing = false
        break
      }
    }
    logTest(
      'Direct commission rates increase with package value',
      directRatesIncreasing,
      'Direct commission rates do not follow expected pattern'
    )
  }
}

// Test Suite 4: Gift System Testing
async function testGiftSystem() {
  console.log('\n🎁 Testing Gift System...')
  
  const response = await makeRequest(`${API_BASE}/unified-commission-structure`)
  if (response.success && response.data.success) {
    const structures = response.data.data
    
    // Test 12: Gift system rates match commission table
    for (const structure of structures) {
      const expected = EXPECTED_COMMISSION_RATES[structure.package_value]
      if (expected) {
        const giftRatesMatch = [
          'present_user_rate',
          'present_leader_rate', 
          'annual_present_user_rate',
          'annual_present_leader_rate'
        ].every(key => Math.abs(structure[key] - expected[key]) < 0.0001)
        
        logTest(
          `Package ${structure.package_value} gift system rates match commission table`,
          giftRatesMatch,
          'Gift system rates do not match expected values'
        )
      }
    }
  }
}

// Main Test Runner
async function runAllTests() {
  console.log('🚀 Starting Commission Structure Comprehensive Tests...')
  console.log('=' .repeat(60))
  
  await testAPIEndpoints()
  await testDatabaseIntegrity()
  await testCommissionCalculations()
  await testGiftSystem()
  
  // Print Summary
  console.log('\n' + '='.repeat(60))
  console.log('📊 TEST SUMMARY')
  console.log('='.repeat(60))
  console.log(`Total Tests: ${testResults.total}`)
  console.log(`Passed: ${testResults.passed} ✅`)
  console.log(`Failed: ${testResults.failed} ❌`)
  console.log(`Success Rate: ${((testResults.passed / testResults.total) * 100).toFixed(1)}%`)
  
  if (testResults.failed > 0) {
    console.log('\n❌ Failed Tests:')
    testResults.details
      .filter(t => !t.passed)
      .forEach(t => console.log(`  - ${t.testName}: ${t.details}`))
  }
  
  console.log('\n' + '='.repeat(60))
  return testResults
}

// Export for use in Node.js or browser
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { runAllTests, testResults }
} else if (typeof window !== 'undefined') {
  window.CommissionStructureTests = { runAllTests, testResults }
}

// Auto-run if called directly
if (typeof require !== 'undefined' && require.main === module) {
  runAllTests()
}
