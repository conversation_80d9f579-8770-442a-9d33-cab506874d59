-- OK<PERSON><PERSON><PERSON> Head Universal Commission System
-- This migration ensures <PERSON>DOI Head receives commissions from ALL users regardless of network connection

-- =====================================================
-- 1. UPDATE COMMISSION DISTRIBUTION FUNCTION
-- =====================================================

-- Update the calculate_commission_distribution function to always include OKDOI Head commission
CREATE OR REPLACE FUNCTION calculate_commission_distribution(
    purchaser_id UUID,
    package_id UUID,
    package_amount DECIMAL(12,2)
) RETURNS VOID AS $$
DECLARE
    commission_record RECORD;
    beneficiary_record RECORD;
    commission_amount DECIMAL(12,2);
    transaction_id_val VARCHAR(50);
    okdoi_head_id UUID;
    total_distributed DECIMAL(12,2) := 0;
    remaining_commission DECIMAL(12,2);
    beneficiary_wallet_id UUID;
    wallet_transaction_id UUID;
    okdoi_head_commission_amount DECIMAL(12,2);
    okdoi_head_wallet_id UUID;
    okdoi_head_wallet_transaction_id UUID;
BEGIN
    -- Generate unique transaction ID
    transaction_id_val := 'COM-' || TO_CHAR(NOW(), 'YYYYMMDD') || '-' || UPPER(SUBSTRING(MD5(RANDOM()::TEXT) FROM 1 FOR 8));

    -- Get OKDOI Head user ID
    SELECT id INTO okdoi_head_id FROM users WHERE user_type = 'okdoi_head' LIMIT 1;
    
    IF okdoi_head_id IS NULL THEN
        RAISE EXCEPTION 'OKDOI Head not found in the system';
    END IF;

    -- Get commission structures for this package amount
    FOR commission_record IN
        SELECT * FROM commission_structure 
        WHERE package_value <= package_amount 
        AND is_active = true 
        ORDER BY package_value DESC
        LIMIT 1
    LOOP
        -- =====================================================
        -- FIRST: ALWAYS PROCESS OKDOI HEAD COMMISSION
        -- =====================================================
        
        -- Calculate OKDOI Head commission (this happens for ALL users)
        okdoi_head_commission_amount := package_amount * (commission_record.okdoi_head_rate / 100.0);
        
        -- Get OKDOI Head's wallet
        SELECT id INTO okdoi_head_wallet_id 
        FROM user_wallets 
        WHERE user_id = okdoi_head_id;
        
        IF okdoi_head_wallet_id IS NOT NULL AND okdoi_head_commission_amount > 0 THEN
            -- Credit OKDOI Head's wallet
            INSERT INTO wallet_transactions (
                wallet_id, transaction_type, amount, description, 
                reference_id, status, created_at
            ) VALUES (
                okdoi_head_wallet_id, 'commission_credit', okdoi_head_commission_amount,
                'OKDOI Head Commission from Package Purchase',
                package_id, 'completed', NOW()
            ) RETURNING id INTO okdoi_head_wallet_transaction_id;
            
            -- Update wallet balance
            UPDATE user_wallets 
            SET balance = balance + okdoi_head_commission_amount,
                updated_at = NOW()
            WHERE id = okdoi_head_wallet_id;
            
            -- Create commission transaction record
            INSERT INTO commission_transactions (
                user_id, beneficiary_id, subscription_purchase_id, commission_type,
                commission_level, commission_rate, commission_amount, status, 
                wallet_transaction_id, transaction_id, created_at, metadata
            ) VALUES (
                purchaser_id, okdoi_head_id, package_id, 'okdoi_head_commission',
                0, commission_record.okdoi_head_rate, okdoi_head_commission_amount, 
                'processed', okdoi_head_wallet_transaction_id, transaction_id_val, NOW(),
                '{"source": "universal_commission", "applies_to": "all_users"}'
            );
            
            -- Update OKDOI Head's total commission earned
            UPDATE users 
            SET total_commission_earned = COALESCE(total_commission_earned, 0) + okdoi_head_commission_amount,
                updated_at = NOW()
            WHERE id = okdoi_head_id;
            
            total_distributed := total_distributed + okdoi_head_commission_amount;
        END IF;
        
        -- =====================================================
        -- SECOND: PROCESS NETWORK-BASED COMMISSIONS (1-10 LEVELS)
        -- =====================================================
        
        -- Process each level (1-10) for network-connected users
        FOR i IN 1..10 LOOP
            -- Get beneficiary at this level
            SELECT * INTO beneficiary_record FROM get_referral_upline(purchaser_id, i);
            
            IF beneficiary_record.id IS NOT NULL THEN
                -- Calculate commission amount for this level
                commission_amount := package_amount * (
                    CASE i
                        WHEN 1 THEN commission_record.level_1_rate
                        WHEN 2 THEN commission_record.level_2_rate
                        WHEN 3 THEN commission_record.level_3_rate
                        WHEN 4 THEN commission_record.level_4_rate
                        WHEN 5 THEN commission_record.level_5_rate
                        WHEN 6 THEN commission_record.level_6_rate
                        WHEN 7 THEN commission_record.level_7_rate
                        WHEN 8 THEN commission_record.level_8_rate
                        WHEN 9 THEN commission_record.level_9_rate
                        WHEN 10 THEN commission_record.level_10_rate
                    END / 100.0
                );
                
                IF commission_amount > 0 THEN
                    -- Get beneficiary's wallet
                    SELECT id INTO beneficiary_wallet_id 
                    FROM user_wallets 
                    WHERE user_id = beneficiary_record.id;
                    
                    IF beneficiary_wallet_id IS NOT NULL THEN
                        -- Credit beneficiary's wallet
                        INSERT INTO wallet_transactions (
                            wallet_id, transaction_type, amount, description, 
                            reference_id, status, created_at
                        ) VALUES (
                            beneficiary_wallet_id, 'commission_credit', commission_amount,
                            'Level ' || i || ' Commission from Referral',
                            package_id, 'completed', NOW()
                        ) RETURNING id INTO wallet_transaction_id;
                        
                        -- Update wallet balance
                        UPDATE user_wallets 
                        SET balance = balance + commission_amount,
                            updated_at = NOW()
                        WHERE id = beneficiary_wallet_id;
                        
                        -- Create commission transaction record
                        INSERT INTO commission_transactions (
                            user_id, beneficiary_id, subscription_purchase_id, commission_type,
                            commission_level, commission_rate, commission_amount, status, 
                            wallet_transaction_id, transaction_id, created_at
                        ) VALUES (
                            purchaser_id, beneficiary_record.id, package_id, 'level_commission',
                            i, CASE i
                                WHEN 1 THEN commission_record.level_1_rate
                                WHEN 2 THEN commission_record.level_2_rate
                                WHEN 3 THEN commission_record.level_3_rate
                                WHEN 4 THEN commission_record.level_4_rate
                                WHEN 5 THEN commission_record.level_5_rate
                                WHEN 6 THEN commission_record.level_6_rate
                                WHEN 7 THEN commission_record.level_7_rate
                                WHEN 8 THEN commission_record.level_8_rate
                                WHEN 9 THEN commission_record.level_9_rate
                                WHEN 10 THEN commission_record.level_10_rate
                            END,
                            commission_amount, 'processed', wallet_transaction_id, transaction_id_val, NOW()
                        );
                        
                        -- Update user's total commission earned
                        UPDATE users 
                        SET total_commission_earned = COALESCE(total_commission_earned, 0) + commission_amount,
                            updated_at = NOW()
                        WHERE id = beneficiary_record.id;
                        
                        total_distributed := total_distributed + commission_amount;
                    END IF;
                END IF;
            ELSE
                -- No beneficiary at this level, but OKDOI Head already got their universal commission
                -- So we don't need to do anything here (unlike the old system that gave unallocated to OKDOI Head)
                NULL;
            END IF;
        END LOOP;
    END LOOP;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- 2. CREATE FUNCTION TO GET OKDOI HEAD COMMISSION STATS
-- =====================================================

-- Function to get OKDOI Head commission statistics
CREATE OR REPLACE FUNCTION get_okdoi_head_commission_stats()
RETURNS TABLE(
    total_universal_commissions DECIMAL(12,2),
    total_unallocated_commissions DECIMAL(12,2),
    total_all_commissions DECIMAL(12,2),
    commission_transactions_count BIGINT,
    unique_purchasers_count BIGINT
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        COALESCE(SUM(CASE WHEN ct.metadata::jsonb->>'source' = 'universal_commission' THEN ct.commission_amount ELSE 0 END), 0) as total_universal_commissions,
        COALESCE(SUM(CASE WHEN ct.commission_type LIKE '%unallocated%' THEN ct.commission_amount ELSE 0 END), 0) as total_unallocated_commissions,
        COALESCE(SUM(ct.commission_amount), 0) as total_all_commissions,
        COUNT(*) as commission_transactions_count,
        COUNT(DISTINCT ct.user_id) as unique_purchasers_count
    FROM commission_transactions ct
    JOIN users u ON u.id = ct.beneficiary_id
    WHERE u.user_type = 'okdoi_head'
    AND ct.status = 'processed';
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- 3. CREATE FUNCTION TO VALIDATE COMMISSION INTEGRITY
-- =====================================================

-- Function to validate that all users have OKDOI Head commission records
CREATE OR REPLACE FUNCTION validate_okdoi_head_commission_integrity()
RETURNS TABLE(
    user_id UUID,
    user_email TEXT,
    subscription_count BIGINT,
    okdoi_head_commission_count BIGINT,
    missing_commissions BIGINT
) AS $$
BEGIN
    RETURN QUERY
    WITH user_subscriptions AS (
        SELECT
            ct.user_id,
            u.email,
            COUNT(DISTINCT ct.subscription_purchase_id) as subscription_count
        FROM commission_transactions ct
        JOIN users u ON u.id = ct.user_id
        GROUP BY ct.user_id, u.email
    ),
    okdoi_head_commissions AS (
        SELECT
            ct.user_id,
            COUNT(*) as okdoi_head_commission_count
        FROM commission_transactions ct
        JOIN users beneficiary ON beneficiary.id = ct.beneficiary_id
        WHERE beneficiary.user_type = 'okdoi_head'
        AND (ct.commission_type = 'okdoi_head_commission' OR ct.metadata::jsonb->>'source' = 'universal_commission')
        GROUP BY ct.user_id
    )
    SELECT
        us.user_id,
        us.email,
        us.subscription_count,
        COALESCE(ohc.okdoi_head_commission_count, 0) as okdoi_head_commission_count,
        (us.subscription_count - COALESCE(ohc.okdoi_head_commission_count, 0)) as missing_commissions
    FROM user_subscriptions us
    LEFT JOIN okdoi_head_commissions ohc ON ohc.user_id = us.user_id
    WHERE (us.subscription_count - COALESCE(ohc.okdoi_head_commission_count, 0)) > 0;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- 4. ADD COMMENTS FOR DOCUMENTATION
-- =====================================================

COMMENT ON FUNCTION calculate_commission_distribution(UUID, UUID, DECIMAL) IS
'Updated commission distribution function that ensures OKDOI Head receives commissions from ALL users regardless of network connection, plus normal network-based commissions for connected users.';

COMMENT ON FUNCTION get_okdoi_head_commission_stats() IS
'Returns comprehensive statistics about OKDOI Head commission earnings, including universal commissions from all users and unallocated commissions.';

COMMENT ON FUNCTION validate_okdoi_head_commission_integrity() IS
'Validates that all user subscriptions have corresponding OKDOI Head commission records, helping identify any missing universal commissions.';

-- =====================================================
-- 5. CREATE INDEXES FOR BETTER PERFORMANCE
-- =====================================================

-- Index for faster OKDOI Head commission queries
CREATE INDEX IF NOT EXISTS idx_commission_transactions_okdoi_head
ON commission_transactions(beneficiary_id, commission_type)
WHERE commission_type = 'okdoi_head_commission';

-- Index for metadata-based queries
CREATE INDEX IF NOT EXISTS idx_commission_transactions_metadata
ON commission_transactions USING GIN(metadata)
WHERE metadata IS NOT NULL;
