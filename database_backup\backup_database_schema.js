const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');

// Load environment variables
require('dotenv').config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function executeQuery(query, description) {
  try {
    const { data, error } = await supabase.rpc('execute_sql', { query });
    
    if (error) {
      console.log(`  ⚠️  ${description}: ${error.message}`);
      return { error: error.message };
    }
    
    console.log(`  ✅ ${description}: ${data?.length || 0} items`);
    return data || [];
  } catch (err) {
    console.log(`  ❌ ${description}: ${err.message}`);
    return { error: err.message };
  }
}

async function backupDatabaseSchema() {
  console.log('🗄️  Backing up complete database schema...');
  
  const schemaBackup = {
    timestamp: new Date().toISOString(),
    backup_type: 'DATABASE_SCHEMA',
    components: {}
  };
  
  // 1. Tables and Columns
  console.log('📊 Getting table structures...');
  const tablesQuery = `
    SELECT 
      t.table_name,
      t.table_type,
      c.column_name,
      c.data_type,
      c.is_nullable,
      c.column_default,
      c.character_maximum_length,
      c.numeric_precision,
      c.numeric_scale
    FROM information_schema.tables t
    LEFT JOIN information_schema.columns c ON t.table_name = c.table_name
    WHERE t.table_schema = 'public'
    AND t.table_type = 'BASE TABLE'
    ORDER BY t.table_name, c.ordinal_position;
  `;
  schemaBackup.components.tables = await executeQuery(tablesQuery, 'Table structures');
  
  // 2. Primary Keys
  console.log('🔑 Getting primary keys...');
  const primaryKeysQuery = `
    SELECT 
      tc.table_name,
      kcu.column_name,
      tc.constraint_name
    FROM information_schema.table_constraints tc
    JOIN information_schema.key_column_usage kcu 
      ON tc.constraint_name = kcu.constraint_name
    WHERE tc.constraint_type = 'PRIMARY KEY'
    AND tc.table_schema = 'public'
    ORDER BY tc.table_name;
  `;
  schemaBackup.components.primary_keys = await executeQuery(primaryKeysQuery, 'Primary keys');
  
  // 3. Foreign Keys
  console.log('🔗 Getting foreign keys...');
  const foreignKeysQuery = `
    SELECT 
      tc.table_name,
      kcu.column_name,
      ccu.table_name AS foreign_table_name,
      ccu.column_name AS foreign_column_name,
      tc.constraint_name,
      rc.update_rule,
      rc.delete_rule
    FROM information_schema.table_constraints AS tc
    JOIN information_schema.key_column_usage AS kcu
      ON tc.constraint_name = kcu.constraint_name
    JOIN information_schema.constraint_column_usage AS ccu
      ON ccu.constraint_name = tc.constraint_name
    JOIN information_schema.referential_constraints AS rc
      ON tc.constraint_name = rc.constraint_name
    WHERE tc.constraint_type = 'FOREIGN KEY'
    AND tc.table_schema = 'public'
    ORDER BY tc.table_name;
  `;
  schemaBackup.components.foreign_keys = await executeQuery(foreignKeysQuery, 'Foreign keys');
  
  // 4. Indexes
  console.log('📇 Getting indexes...');
  const indexesQuery = `
    SELECT 
      schemaname,
      tablename,
      indexname,
      indexdef
    FROM pg_indexes
    WHERE schemaname = 'public'
    ORDER BY tablename, indexname;
  `;
  schemaBackup.components.indexes = await executeQuery(indexesQuery, 'Indexes');
  
  // 5. Check Constraints
  console.log('✅ Getting check constraints...');
  const checkConstraintsQuery = `
    SELECT 
      tc.table_name,
      tc.constraint_name,
      cc.check_clause
    FROM information_schema.table_constraints tc
    JOIN information_schema.check_constraints cc
      ON tc.constraint_name = cc.constraint_name
    WHERE tc.constraint_type = 'CHECK'
    AND tc.table_schema = 'public'
    ORDER BY tc.table_name;
  `;
  schemaBackup.components.check_constraints = await executeQuery(checkConstraintsQuery, 'Check constraints');
  
  // 6. Sequences
  console.log('🔢 Getting sequences...');
  const sequencesQuery = `
    SELECT 
      sequence_name,
      data_type,
      start_value,
      minimum_value,
      maximum_value,
      increment,
      cycle_option
    FROM information_schema.sequences
    WHERE sequence_schema = 'public'
    ORDER BY sequence_name;
  `;
  schemaBackup.components.sequences = await executeQuery(sequencesQuery, 'Sequences');
  
  // 7. Extensions
  console.log('🧩 Getting extensions...');
  const extensionsQuery = `
    SELECT 
      extname as extension_name,
      extversion as version,
      extrelocatable as relocatable
    FROM pg_extension
    ORDER BY extname;
  `;
  schemaBackup.components.extensions = await executeQuery(extensionsQuery, 'Extensions');
  
  // 8. Custom Types
  console.log('🏷️  Getting custom types...');
  const customTypesQuery = `
    SELECT 
      t.typname as type_name,
      t.typtype as type_type,
      n.nspname as schema_name,
      pg_catalog.format_type(t.oid, NULL) AS type_definition
    FROM pg_type t
    LEFT JOIN pg_namespace n ON n.oid = t.typnamespace
    WHERE n.nspname = 'public'
    AND t.typtype IN ('e', 'c', 'd')
    ORDER BY t.typname;
  `;
  schemaBackup.components.custom_types = await executeQuery(customTypesQuery, 'Custom types');
  
  // 9. Table Comments
  console.log('💬 Getting table comments...');
  const tableCommentsQuery = `
    SELECT 
      c.relname as table_name,
      obj_description(c.oid) as comment
    FROM pg_class c
    JOIN pg_namespace n ON n.oid = c.relnamespace
    WHERE n.nspname = 'public'
    AND c.relkind = 'r'
    AND obj_description(c.oid) IS NOT NULL
    ORDER BY c.relname;
  `;
  schemaBackup.components.table_comments = await executeQuery(tableCommentsQuery, 'Table comments');
  
  // 10. Column Comments
  console.log('📝 Getting column comments...');
  const columnCommentsQuery = `
    SELECT 
      c.table_name,
      c.column_name,
      pgd.description as comment
    FROM information_schema.columns c
    JOIN pg_class pgc ON pgc.relname = c.table_name
    JOIN pg_namespace pgn ON pgn.oid = pgc.relnamespace
    JOIN pg_attribute pga ON pga.attrelid = pgc.oid AND pga.attname = c.column_name
    JOIN pg_description pgd ON pgd.objoid = pgc.oid AND pgd.objsubid = pga.attnum
    WHERE c.table_schema = 'public'
    AND pgn.nspname = 'public'
    ORDER BY c.table_name, c.ordinal_position;
  `;
  schemaBackup.components.column_comments = await executeQuery(columnCommentsQuery, 'Column comments');
  
  // Save schema backup
  const schemaFileName = `database_schema_backup_${new Date().toISOString().replace(/[:.]/g, '-')}.json`;
  const schemaPath = path.join(__dirname, schemaFileName);
  
  fs.writeFileSync(schemaPath, JSON.stringify(schemaBackup, null, 2));
  
  console.log('\n✅ Database schema backup completed!');
  console.log(`📁 Schema backup saved to: ${schemaPath}`);
  
  return schemaBackup;
}

// Run if called directly
if (require.main === module) {
  backupDatabaseSchema().catch(console.error);
}

module.exports = { backupDatabaseSchema };
