-- Merchant Wallet System Migration
-- Creates separate wallet system for shop owners to receive payments

-- Create merchant_wallets table
CREATE TABLE merchant_wallets (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    shop_id UUID NOT NULL REFERENCES vendor_shops(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    balance DECIMAL(12,2) DEFAULT 0.00 NOT NULL CHECK (balance >= 0),
    total_earned DECIMAL(12,2) DEFAULT 0.00 NOT NULL,
    total_withdrawn DECIMAL(12,2) DEFAULT 0.00 NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(shop_id),
    UNIQUE(user_id, shop_id)
);

-- Create merchant_wallet_transactions table
CREATE TABLE merchant_wallet_transactions (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    merchant_wallet_id UUID NOT NULL REFERENCES merchant_wallets(id) ON DELETE CASCADE,
    transaction_id VARCHAR(50) NOT NULL UNIQUE,
    type VARCHAR(20) NOT NULL CHECK (type IN ('credit', 'debit')),
    category VARCHAR(30) NOT NULL CHECK (category IN ('order_payment', 'transfer_to_main', 'commission_deduction', 'refund', 'adjustment')),
    amount DECIMAL(12,2) NOT NULL CHECK (amount > 0),
    balance_before DECIMAL(12,2) NOT NULL,
    balance_after DECIMAL(12,2) NOT NULL,
    description TEXT,
    reference_id UUID, -- Can reference order_id, transfer_id, etc.
    reference_type VARCHAR(20), -- 'order', 'transfer', 'refund', etc.
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID REFERENCES users(id)
);

-- Create merchant_to_main_transfers table
CREATE TABLE merchant_to_main_transfers (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    merchant_wallet_id UUID NOT NULL REFERENCES merchant_wallets(id) ON DELETE CASCADE,
    user_wallet_id UUID NOT NULL REFERENCES user_wallets(id) ON DELETE CASCADE,
    transfer_id VARCHAR(50) NOT NULL UNIQUE,
    amount DECIMAL(12,2) NOT NULL CHECK (amount > 0),
    status VARCHAR(20) DEFAULT 'completed' CHECK (status IN ('pending', 'completed', 'failed', 'cancelled')),
    description TEXT DEFAULT 'Transfer from merchant wallet to main wallet',
    merchant_transaction_id UUID REFERENCES merchant_wallet_transactions(id),
    main_transaction_id UUID REFERENCES wallet_transactions(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    completed_at TIMESTAMP WITH TIME ZONE,
    failed_reason TEXT
);

-- Create indexes for better performance
CREATE INDEX idx_merchant_wallets_shop_id ON merchant_wallets(shop_id);
CREATE INDEX idx_merchant_wallets_user_id ON merchant_wallets(user_id);
CREATE INDEX idx_merchant_wallet_transactions_wallet_id ON merchant_wallet_transactions(merchant_wallet_id);
CREATE INDEX idx_merchant_wallet_transactions_type ON merchant_wallet_transactions(type);
CREATE INDEX idx_merchant_wallet_transactions_category ON merchant_wallet_transactions(category);
CREATE INDEX idx_merchant_wallet_transactions_created_at ON merchant_wallet_transactions(created_at DESC);
CREATE INDEX idx_merchant_to_main_transfers_merchant_wallet_id ON merchant_to_main_transfers(merchant_wallet_id);
CREATE INDEX idx_merchant_to_main_transfers_status ON merchant_to_main_transfers(status);

-- Enable Row Level Security
ALTER TABLE merchant_wallets ENABLE ROW LEVEL SECURITY;
ALTER TABLE merchant_wallet_transactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE merchant_to_main_transfers ENABLE ROW LEVEL SECURITY;

-- RLS Policies for merchant_wallets
-- Shop owners can view their own merchant wallet
CREATE POLICY "Shop owners can view their merchant wallet" ON merchant_wallets
    FOR SELECT USING (auth.uid() = user_id);

-- Shop owners can update their own merchant wallet (for settings)
CREATE POLICY "Shop owners can update their merchant wallet" ON merchant_wallets
    FOR UPDATE USING (auth.uid() = user_id);

-- RLS Policies for merchant_wallet_transactions
-- Shop owners can view their own merchant wallet transactions
CREATE POLICY "Shop owners can view their merchant wallet transactions" ON merchant_wallet_transactions
    FOR SELECT USING (
        merchant_wallet_id IN (
            SELECT id FROM merchant_wallets WHERE user_id = auth.uid()
        )
    );

-- RLS Policies for merchant_to_main_transfers
-- Shop owners can view their own transfers
CREATE POLICY "Shop owners can view their transfers" ON merchant_to_main_transfers
    FOR SELECT USING (
        merchant_wallet_id IN (
            SELECT id FROM merchant_wallets WHERE user_id = auth.uid()
        )
    );

-- Function to create merchant wallet when shop is approved
CREATE OR REPLACE FUNCTION create_merchant_wallet_on_shop_approval()
RETURNS TRIGGER AS $$
BEGIN
    -- Only create wallet when shop is approved for the first time
    IF NEW.status = 'approved' AND (OLD.status IS NULL OR OLD.status != 'approved') THEN
        INSERT INTO merchant_wallets (shop_id, user_id)
        VALUES (NEW.id, NEW.user_id)
        ON CONFLICT (shop_id) DO NOTHING; -- Prevent duplicate wallets
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger to create merchant wallet when shop is approved
CREATE TRIGGER create_merchant_wallet_on_shop_approval_trigger
    AFTER UPDATE ON vendor_shops
    FOR EACH ROW
    EXECUTE FUNCTION create_merchant_wallet_on_shop_approval();

-- Function to generate unique transaction ID
CREATE OR REPLACE FUNCTION generate_merchant_transaction_id()
RETURNS TEXT AS $$
BEGIN
    RETURN 'MWT' || TO_CHAR(NOW(), 'YYYYMMDD') || LPAD(EXTRACT(EPOCH FROM NOW())::BIGINT % 100000, 5, '0') || LPAD(FLOOR(RANDOM() * 1000)::INT, 3, '0');
END;
$$ LANGUAGE plpgsql;

-- Function to generate unique transfer ID
CREATE OR REPLACE FUNCTION generate_transfer_id()
RETURNS TEXT AS $$
BEGIN
    RETURN 'TRF' || TO_CHAR(NOW(), 'YYYYMMDD') || LPAD(EXTRACT(EPOCH FROM NOW())::BIGINT % 100000, 5, '0') || LPAD(FLOOR(RANDOM() * 1000)::INT, 3, '0');
END;
$$ LANGUAGE plpgsql;

-- Function to credit merchant wallet (when order is completed)
CREATE OR REPLACE FUNCTION credit_merchant_wallet(
    p_shop_id UUID,
    p_amount DECIMAL(12,2),
    p_description TEXT,
    p_reference_id UUID DEFAULT NULL,
    p_reference_type VARCHAR(20) DEFAULT 'order'
)
RETURNS UUID AS $$
DECLARE
    v_merchant_wallet_id UUID;
    v_transaction_id VARCHAR(50);
    v_balance_before DECIMAL(12,2);
    v_balance_after DECIMAL(12,2);
    v_transaction_record_id UUID;
BEGIN
    -- Get merchant wallet
    SELECT id, balance INTO v_merchant_wallet_id, v_balance_before
    FROM merchant_wallets
    WHERE shop_id = p_shop_id AND is_active = TRUE;
    
    IF v_merchant_wallet_id IS NULL THEN
        RAISE EXCEPTION 'Merchant wallet not found for shop %', p_shop_id;
    END IF;
    
    -- Calculate new balance
    v_balance_after := v_balance_before + p_amount;
    
    -- Generate transaction ID
    v_transaction_id := generate_merchant_transaction_id();
    
    -- Update wallet balance
    UPDATE merchant_wallets
    SET 
        balance = v_balance_after,
        total_earned = total_earned + p_amount,
        updated_at = NOW()
    WHERE id = v_merchant_wallet_id;
    
    -- Create transaction record
    INSERT INTO merchant_wallet_transactions (
        merchant_wallet_id,
        transaction_id,
        type,
        category,
        amount,
        balance_before,
        balance_after,
        description,
        reference_id,
        reference_type
    ) VALUES (
        v_merchant_wallet_id,
        v_transaction_id,
        'credit',
        'order_payment',
        p_amount,
        v_balance_before,
        v_balance_after,
        p_description,
        p_reference_id,
        p_reference_type
    ) RETURNING id INTO v_transaction_record_id;
    
    RETURN v_transaction_record_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to transfer from merchant wallet to main wallet
CREATE OR REPLACE FUNCTION transfer_merchant_to_main_wallet(
    p_merchant_wallet_id UUID,
    p_amount DECIMAL(12,2),
    p_user_id UUID
)
RETURNS UUID AS $$
DECLARE
    v_merchant_balance DECIMAL(12,2);
    v_user_wallet_id UUID;
    v_user_balance DECIMAL(12,2);
    v_transfer_id VARCHAR(50);
    v_merchant_transaction_id UUID;
    v_main_transaction_id UUID;
    v_transfer_record_id UUID;
BEGIN
    -- Check merchant wallet balance
    SELECT balance INTO v_merchant_balance
    FROM merchant_wallets
    WHERE id = p_merchant_wallet_id AND user_id = p_user_id AND is_active = TRUE;
    
    IF v_merchant_balance IS NULL THEN
        RAISE EXCEPTION 'Merchant wallet not found or inactive';
    END IF;
    
    IF v_merchant_balance < p_amount THEN
        RAISE EXCEPTION 'Insufficient merchant wallet balance';
    END IF;
    
    -- Get user main wallet
    SELECT id, balance INTO v_user_wallet_id, v_user_balance
    FROM user_wallets
    WHERE user_id = p_user_id AND is_active = TRUE;
    
    IF v_user_wallet_id IS NULL THEN
        RAISE EXCEPTION 'Main wallet not found or inactive';
    END IF;
    
    -- Generate transfer ID
    v_transfer_id := generate_transfer_id();
    
    -- Debit merchant wallet
    UPDATE merchant_wallets
    SET 
        balance = balance - p_amount,
        total_withdrawn = total_withdrawn + p_amount,
        updated_at = NOW()
    WHERE id = p_merchant_wallet_id;
    
    -- Credit main wallet
    UPDATE user_wallets
    SET 
        balance = balance + p_amount,
        updated_at = NOW()
    WHERE id = v_user_wallet_id;
    
    -- Create merchant wallet transaction (debit)
    INSERT INTO merchant_wallet_transactions (
        merchant_wallet_id,
        transaction_id,
        type,
        category,
        amount,
        balance_before,
        balance_after,
        description,
        reference_type
    ) VALUES (
        p_merchant_wallet_id,
        v_transfer_id || '_OUT',
        'debit',
        'transfer_to_main',
        p_amount,
        v_merchant_balance,
        v_merchant_balance - p_amount,
        'Transfer to main wallet',
        'transfer'
    ) RETURNING id INTO v_merchant_transaction_id;
    
    -- Create main wallet transaction (credit)
    INSERT INTO wallet_transactions (
        wallet_id,
        transaction_id,
        type,
        category,
        amount,
        balance_before,
        balance_after,
        description,
        reference_type
    ) VALUES (
        v_user_wallet_id,
        v_transfer_id || '_IN',
        'credit',
        'merchant_transfer',
        p_amount,
        v_user_balance,
        v_user_balance + p_amount,
        'Transfer from merchant wallet',
        'transfer'
    ) RETURNING id INTO v_main_transaction_id;
    
    -- Create transfer record
    INSERT INTO merchant_to_main_transfers (
        merchant_wallet_id,
        user_wallet_id,
        transfer_id,
        amount,
        status,
        merchant_transaction_id,
        main_transaction_id,
        completed_at
    ) VALUES (
        p_merchant_wallet_id,
        v_user_wallet_id,
        v_transfer_id,
        p_amount,
        'completed',
        v_merchant_transaction_id,
        v_main_transaction_id,
        NOW()
    ) RETURNING id INTO v_transfer_record_id;
    
    RETURN v_transfer_record_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create merchant wallets for existing approved shops
INSERT INTO merchant_wallets (shop_id, user_id)
SELECT id, user_id
FROM vendor_shops
WHERE status = 'approved'
ON CONFLICT (shop_id) DO NOTHING;
