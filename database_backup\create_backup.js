const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');

// Load environment variables
require('dotenv').config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase configuration in .env.local');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

// List of all tables to backup (based on actual database schema)
const tables = [
  'users',
  'categories',
  'subcategories',
  'ads',
  'ad_images',
  'ad_boosts',
  'ad_drafts',
  'user_favorites',
  'chat_conversations',
  'chat_messages',
  'subscription_packages',
  'user_subscriptions',
  'boost_packages',
  'vendor_shops',
  'shop_categories',
  'shop_products',
  'shop_product_images',
  'product_reviews',
  'shop_orders',
  'order_items',
  'order_status_history',
  'cart_items',
  'user_wallets',
  'wallet_transactions',
  'deposit_requests',
  'withdrawal_requests',
  'p2p_transfers',
  'merchant_wallets',
  'merchant_wallet_transactions',
  'merchant_to_main_transfers',
  'kyc_document_types',
  'kyc_submissions',
  'kyc_status_history',
  'referral_codes',
  'referrals',
  'referral_hierarchy',
  'referral_placements',
  'regional_sales_managers',
  'zonal_managers',
  'commission_structure',
  'commission_transactions',
  'present_allocations',
  'gift_transactions',
  'present_pools',
  'user_tasks',
  'user_task_assignments',
  'gift_system_audit_log',
  'user_rewards',
  'shop_reviews',
  'shop_followers',
  'districts',
  'cities',
  'admin_settings'
];

async function backupTable(tableName) {
  console.log(`Backing up table: ${tableName}`);
  
  try {
    const { data, error } = await supabase
      .from(tableName)
      .select('*');
    
    if (error) {
      console.error(`Error backing up ${tableName}:`, error);
      return null;
    }
    
    return {
      table: tableName,
      count: data?.length || 0,
      data: data || []
    };
  } catch (err) {
    console.error(`Exception backing up ${tableName}:`, err.message);
    return null;
  }
}

async function createCompleteBackup() {
  console.log('Starting complete database backup...');
  
  const backup = {
    timestamp: new Date().toISOString(),
    supabase_url: supabaseUrl,
    tables: {}
  };
  
  // Backup all tables
  for (const table of tables) {
    const tableBackup = await backupTable(table);
    if (tableBackup) {
      backup.tables[table] = tableBackup;
      console.log(`✓ ${table}: ${tableBackup.count} records`);
    } else {
      console.log(`✗ ${table}: Failed to backup`);
    }
  }
  
  // Save backup to file
  const backupFileName = `complete_backup_${new Date().toISOString().replace(/[:.]/g, '-')}.json`;
  const backupPath = path.join(__dirname, backupFileName);
  
  fs.writeFileSync(backupPath, JSON.stringify(backup, null, 2));
  
  console.log(`\n✅ Backup completed successfully!`);
  console.log(`📁 Backup saved to: ${backupPath}`);
  console.log(`📊 Total tables backed up: ${Object.keys(backup.tables).length}`);
  
  // Create summary
  const summary = {
    timestamp: backup.timestamp,
    total_tables: Object.keys(backup.tables).length,
    table_summary: {}
  };
  
  Object.keys(backup.tables).forEach(table => {
    summary.table_summary[table] = backup.tables[table].count;
  });
  
  const summaryPath = path.join(__dirname, 'backup_summary.json');
  fs.writeFileSync(summaryPath, JSON.stringify(summary, null, 2));
  
  console.log(`📋 Summary saved to: ${summaryPath}`);
  
  return backup;
}

// Run the backup
createCompleteBackup().catch(console.error);
