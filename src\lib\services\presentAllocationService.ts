import { supabase, supabaseAdmin } from "@/lib/supabase"

export interface PresentAllocation {
  id: string
  user_id: string
  allocation_type: "present_leader" | "annual_present_user" | "annual_present_leader"
  source_transaction_id?: string
  source_subscription_id?: string
  package_value: number
  allocation_percentage: number
  allocation_amount: number
  allocation_date: string
  is_redeemed: boolean
  redeemed_at?: string
  redeemed_amount: number
}

export interface PresentPool {
  pool_type: "normal_present" | "annual_present_user" | "annual_present_leader"
  total_allocated: number
  total_distributed: number
  available_balance: number
  allocation_count: number
  last_updated: string
}

export interface UserPresentBalance {
  allocation_type: string
  total_allocated: number
  total_redeemed: number
  available_balance: number
  allocation_count: number
}

export class PresentAllocationService {
  /**
   * Calculate and allocate presents from a package purchase
   * This function is called automatically during commission distribution
   */
  static async allocatePresentsFromPurchase(
    purchaserId: string,
    packageAmount: number,
    subscriptionId?: string,
    transactionId?: string
  ): Promise<PresentAllocation[]> {
    try {
      const { data, error } = await supabaseAdmin.rpc("calculate_present_allocations", {
        purchaser_id: purchaserId,
        package_amount: packageAmount,
        subscription_id: subscriptionId,
        transaction_id: transactionId
      })

      if (error) {
        throw new Error(`Failed to allocate presents: ${error.message}`)
      }

      return data || []
    } catch (error) {
      console.error("PresentAllocationService.allocatePresentsFromPurchase:", error)
      throw error
    }
  }

  /**
   * Get present pool summary (admin-only)
   */
  static async getPresentPoolSummary(): Promise<PresentPool[]> {
    try {
      const { data, error } = await supabaseAdmin.rpc("get_present_pool_summary")

      if (error) {
        throw new Error(`Failed to get present pool summary: ${error.message}`)
      }

      return data || []
    } catch (error) {
      console.error("PresentAllocationService.getPresentPoolSummary:", error)
      throw error
    }
  }

  /**
   * Get user present balance (admin-only)
   */
  static async getUserPresentBalance(userId: string): Promise<UserPresentBalance[]> {
    try {
      const { data, error } = await supabaseAdmin.rpc("get_user_present_balance", {
        target_user_id: userId
      })

      if (error) {
        throw new Error(`Failed to get user present balance: ${error.message}`)
      }

      return data || []
    } catch (error) {
      console.error("PresentAllocationService.getUserPresentBalance:", error)
      throw error
    }
  }

  /**
   * Get present allocations for a user (admin-only)
   */
  static async getUserPresentAllocations(
    userId: string,
    allocationType?: string,
    limit: number = 50,
    offset: number = 0
  ): Promise<{
    allocations: PresentAllocation[]
    total: number
  }> {
    try {
      let query = supabaseAdmin
        .from("present_allocations")
        .select("*", { count: "exact" })
        .eq("user_id", userId)
        .order("allocation_date", { ascending: false })
        .range(offset, offset + limit - 1)

      if (allocationType) {
        query = query.eq("allocation_type", allocationType)
      }

      const { data, error, count } = await query

      if (error) {
        throw new Error(`Failed to get present allocations: ${error.message}`)
      }

      return {
        allocations: data || [],
        total: count || 0
      }
    } catch (error) {
      console.error("PresentAllocationService.getUserPresentAllocations:", error)
      throw error
    }
  }

  /**
   * Redeem annual present for a user (admin action)
   */
  static async redeemAnnualPresent(
    userId: string,
    allocationIds: string[],
    redeemAmount: number,
    adminId: string
  ): Promise<void> {
    try {
      // Start transaction
      const { error: updateError } = await supabaseAdmin
        .from("present_allocations")
        .update({
          is_redeemed: true,
          redeemed_at: new Date().toISOString(),
          redeemed_amount: redeemAmount
        })
        .in("id", allocationIds)
        .eq("user_id", userId)
        .eq("is_redeemed", false)

      if (updateError) {
        throw new Error(`Failed to update present allocations: ${updateError.message}`)
      }

      // Create gift transaction record
      const transactionId = `ANNUAL_${Date.now()}_${userId.substring(0, 8)}`

      const { error: giftError } = await supabaseAdmin
        .from("gift_transactions")
        .insert({
          transaction_id: transactionId,
          user_id: userId,
          gift_type: "annual_present",
          gift_amount: redeemAmount,
          gift_description: `Annual present redemption for ${allocationIds.length} allocations`,
          status: "processed",
          processed_at: new Date().toISOString(),
          processed_by: adminId,
          metadata: {
            allocation_ids: allocationIds,
            redemption_type: "annual_present"
          }
        })

      if (giftError) {
        throw new Error(`Failed to create gift transaction: ${giftError.message}`)
      }

    } catch (error) {
      console.error("PresentAllocationService.redeemAnnualPresent:", error)
      throw error
    }
  }

  /**
   * Get present allocation statistics (admin-only)
   */
  static async getPresentAllocationStats(): Promise<{
    totalAllocations: number
    totalAmount: number
    totalRedeemed: number
    totalPending: number
    byType: Record<string, {
      count: number
      amount: number
      redeemed: number
    }>
  }> {
    try {
      const { data, error } = await supabaseAdmin
        .from("present_allocations")
        .select("allocation_type, allocation_amount, is_redeemed, redeemed_amount")

      if (error) {
        throw new Error(`Failed to get present allocation stats: ${error.message}`)
      }

      const stats = {
        totalAllocations: data.length,
        totalAmount: 0,
        totalRedeemed: 0,
        totalPending: 0,
        byType: {} as Record<string, { count: number; amount: number; redeemed: number }>
      }

      data.forEach(allocation => {
        stats.totalAmount += allocation.allocation_amount

        if (allocation.is_redeemed) {
          stats.totalRedeemed += allocation.redeemed_amount
        } else {
          stats.totalPending += allocation.allocation_amount
        }

        if (!stats.byType[allocation.allocation_type]) {
          stats.byType[allocation.allocation_type] = {
            count: 0,
            amount: 0,
            redeemed: 0
          }
        }

        stats.byType[allocation.allocation_type].count++
        stats.byType[allocation.allocation_type].amount += allocation.allocation_amount

        if (allocation.is_redeemed) {
          stats.byType[allocation.allocation_type].redeemed += allocation.redeemed_amount
        }
      })

      return stats
    } catch (error) {
      console.error("PresentAllocationService.getPresentAllocationStats:", error)
      throw error
    }
  }

  /**
   * Get all present allocations with filters (admin-only)
   */
  static async getAllPresentAllocations(filters: {
    allocationType?: string
    isRedeemed?: string
    dateFrom?: string
    dateTo?: string
    limit?: number
    offset?: number
  }): Promise<{
    allocations: (PresentAllocation & { user: { email: string; full_name?: string } })[]
    total: number
  }> {
    try {
      let query = supabaseAdmin
        .from("present_allocations")
        .select(`
          *,
          user:users!present_allocations_user_id_fkey(email, full_name)
        `, { count: "exact" })
        .order("allocation_date", { ascending: false })

      // Apply filters
      if (filters.allocationType) {
        query = query.eq("allocation_type", filters.allocationType)
      }

      if (filters.isRedeemed) {
        query = query.eq("is_redeemed", filters.isRedeemed === "true")
      }

      if (filters.dateFrom) {
        query = query.gte("allocation_date", filters.dateFrom)
      }

      if (filters.dateTo) {
        query = query.lte("allocation_date", filters.dateTo)
      }

      // Apply pagination
      const limit = filters.limit || 50
      const offset = filters.offset || 0
      query = query.range(offset, offset + limit - 1)

      const { data, error, count } = await query

      if (error) {
        throw new Error(`Failed to get present allocations: ${error.message}`)
      }

      return {
        allocations: data || [],
        total: count || 0
      }
    } catch (error) {
      console.error("PresentAllocationService.getAllPresentAllocations:", error)
      throw error
    }
  }
}
