import { supabase, supabaseAdmin, TABLES } from '@/lib/supabase'
import { CommissionStructure, CommissionTransaction } from '@/types'
import { WalletService } from './wallet'

export interface CommissionDistributionResult {
  totalDistributed: number
  transactionsCreated: number
  unallocatedAmount: number
  distributionDetails: {
    level: number
    beneficiaryId: string
    amount: number
    rate: number
  }[]
}

export interface CommissionBreakdown {
  directCommission: number
  levelCommission: number
  rsmBonus: number
  zmBonus: number
  okdoiHeadCommission: number
  totalCommissions: number
}

/**
 * CommissionSystemService - Handles commission calculations and distributions
 */
export class CommissionSystemService {
  /**
   * Get commission structure for a package value
   */
  static async getCommissionStructure(packageValue: number): Promise<CommissionStructure[]> {
    try {
      const { data, error } = await supabase
        .from(TABLES.COMMISSION_STRUCTURE)
        .select('*')
        .lte('package_value', packageValue)
        .eq('is_active', true)
        .order('package_value', { ascending: false })

      if (error) {
        throw new Error(`Failed to get commission structure: ${error.message}`)
      }

      return data || []
    } catch (error) {
      console.error('Error getting commission structure:', error)
      throw error
    }
  }

  /**
   * Get all commission structures (for admin)
   */
  static async getAllCommissionStructures(): Promise<CommissionStructure[]> {
    try {
      const { data, error } = await supabase
        .from(TABLES.COMMISSION_STRUCTURE)
        .select('*')
        .order('package_value', { ascending: true })

      if (error) {
        throw new Error(`Failed to get all commission structures: ${error.message}`)
      }

      return data || []
    } catch (error) {
      console.error('Error getting all commission structures:', error)
      throw error
    }
  }

  /**
   * Create new commission structure
   */
  static async createCommissionStructure(structure: Omit<CommissionStructure, 'id' | 'created_at' | 'updated_at'>): Promise<CommissionStructure> {
    try {
      const { data, error } = await supabase
        .from(TABLES.COMMISSION_STRUCTURE)
        .insert(structure)
        .select()
        .single()

      if (error) {
        throw new Error(`Failed to create commission structure: ${error.message}`)
      }

      return data
    } catch (error) {
      console.error('Error creating commission structure:', error)
      throw error
    }
  }

  /**
   * Calculate and distribute commissions for a subscription purchase
   * NEW: Handles leftover commissions by sending them to company wallet
   */
  static async distributeCommissions(
    purchaserId: string,
    subscriptionId: string,
    packageAmount: number
  ): Promise<CommissionDistributionResult> {
    try {
      if (!supabaseAdmin) {
        throw new Error("Admin client not available")
      }

      console.log(`Starting commission distribution for subscription ${subscriptionId}, package amount: Rs ${packageAmount}`)

      // Get commission distributions using the new absolute function
      const { data: distributions, error: distributionError } = await supabaseAdmin
        .rpc('calculate_commission_distribution_absolute', {
          p_purchaser_id: purchaserId,
          p_subscription_id: subscriptionId,
          p_package_price: packageAmount
        })

      if (distributionError) {
        throw new Error(`Failed to calculate commission distributions: ${distributionError.message}`)
      }

      if (!distributions || distributions.length === 0) {
        console.warn('No commission distributions calculated')
        return {
          totalDistributed: 0,
          transactionsCreated: 0,
          unallocatedAmount: 0,
          distributionDetails: []
        }
      }

      let totalDistributed = 0
      let distributionCount = 0
      let companyLeftoverAmount = 0

      // Process each commission distribution
      for (const distribution of distributions) {
        // Handle company leftover allocation separately
        if (distribution.beneficiary_id === null && distribution.commission_type === 'company_leftover_allocation') {
          // ✅ Use new function to properly update company wallet balance
          const { error: companyWalletError } = await supabaseAdmin.rpc('update_company_wallet_balance', {
            p_amount: distribution.commission_amount,
            p_transaction_type: 'company_profit',
            p_description: `Leftover commission allocation from network distribution (${distribution.metadata?.missing_positions || 0} missing positions)`,
            p_subscription_id: subscriptionId,
            p_metadata: {
              ...distribution.metadata,
              subscription_id: subscriptionId,
              package_price: packageAmount
            }
          })

          if (companyWalletError) {
            console.error('Failed to credit company wallet with leftover:', companyWalletError)
            throw new Error(`Failed to credit company wallet: ${companyWalletError.message}`)
          }

          companyLeftoverAmount += parseFloat(distribution.commission_amount.toString())
          console.log(`Credited Rs ${distribution.commission_amount} leftover to company wallet`)
          continue // Skip normal commission processing for company allocations
        }

        const transactionId = `comm_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`

        // Insert commission transaction (let id auto-generate as UUID)
        const { error: commissionError } = await supabaseAdmin
          .from('commission_transactions')
          .insert({
            transaction_id: transactionId, // Add transaction_id (required field)
            user_id: distribution.beneficiary_id, // Add user_id (required field)
            beneficiary_id: distribution.beneficiary_id,
            subscription_purchase_id: subscriptionId,
            commission_type: distribution.commission_type,
            commission_level: distribution.level_position, // ✅ Fixed: use commission_level, now supports 999 for unlimited users
            package_value: packageAmount, // Add package_value (required field)
            commission_rate: 0, // Set to 0 for absolute amounts
            commission_amount: distribution.commission_amount,
            status: 'processed',
            metadata: distribution.metadata
          })

        if (commissionError) {
          console.error('Failed to insert commission transaction:', commissionError)
          throw new Error(`Failed to insert commission transaction: ${commissionError.message}`)
        }

        // Get user's wallet ID first
        const { data: userWallet, error: walletFetchError } = await supabaseAdmin
          .from('user_wallets')
          .select('id')
          .eq('user_id', distribution.beneficiary_id)
          .single()

        if (walletFetchError || !userWallet) {
          console.error(`Failed to get wallet for user ${distribution.beneficiary_id}:`, walletFetchError)
          throw new Error(`Failed to get wallet for user ${distribution.beneficiary_id}: ${walletFetchError?.message || 'Wallet not found'}`)
        }

        // ✅ CORRECTED: Check if this is a gift system commission
        const isGiftSystemCommission = distribution.metadata?.gift_system === true ||
          ['present_user', 'annual_present_user', 'present_leader', 'annual_present_leader'].includes(distribution.commission_type)

        // Only update wallet balance for NON-gift system commissions
        if (!isGiftSystemCommission) {
          // Update user's wallet balance using correct wallet_id
          const { error: walletError } = await supabaseAdmin.rpc('update_wallet_balance', {
            p_wallet_id: userWallet.id, // ✅ Now using actual wallet_id
            p_amount: distribution.commission_amount, // ✅ Fixed: Positive amount for adding money
            p_transaction_type: 'deposit', // ✅ Fixed: Use valid transaction type for adding money
            p_description: `${distribution.commission_type} commission from subscription`,
            p_reference_id: subscriptionId,
            p_reference_type: 'subscription',
            p_metadata: {
              commission_type: distribution.commission_type,
              commission_level: distribution.level_position, // ✅ Fixed: use commission_level
              subscription_id: subscriptionId
            }
          })

          if (walletError) {
            console.error('Failed to update wallet balance:', walletError)
            throw new Error(`Failed to update wallet balance: ${walletError.message}`)
          }

          console.log(`✅ Added Rs ${distribution.commission_amount} to wallet for ${distribution.commission_type}`)
        } else {
          console.log(`⚠️ Skipped wallet update for gift system commission: ${distribution.commission_type} (Rs ${distribution.commission_amount})`)
        }

        totalDistributed += parseFloat(distribution.commission_amount.toString())
        distributionCount++
      }

      // Process gift system commissions
      try {
        await supabaseAdmin.rpc('distribute_gift_system_commissions', {
          p_purchaser_id: purchaserId,
          p_subscription_id: subscriptionId,
          p_package_price: packageAmount
        })
        console.log('Gift system commissions distributed successfully')
      } catch (giftError) {
        console.error('Failed to distribute gift system commissions:', giftError)
        // Don't fail the main distribution, but log the error
      }

      // Credit company profit to company wallet
      const { data: packageStructure } = await supabaseAdmin
        .from('commission_structure')
        .select('company_wallet_amount')
        .eq('package_value', packageAmount)
        .eq('commission_type', 'unified_structure')
        .single()

      if (packageStructure?.company_wallet_amount) {
        // ✅ Use new function to properly update company wallet balance
        const { error: companyProfitError } = await supabaseAdmin.rpc('update_company_wallet_balance', {
          p_amount: packageStructure.company_wallet_amount,
          p_transaction_type: 'company_profit',
          p_description: `Company profit from subscription package Rs. ${packageAmount}`,
          p_subscription_id: subscriptionId,
          p_metadata: {
            package_price: packageAmount,
            subscription_id: subscriptionId
          }
        })

        if (companyProfitError) {
          console.error('Failed to credit company profit:', companyProfitError)
          throw new Error(`Failed to credit company profit: ${companyProfitError.message}`)
        }
      }

      console.log(`Commission distribution completed: Rs ${totalDistributed} to ${distributionCount} recipients, Rs ${companyLeftoverAmount} leftover to company`)

      return {
        totalDistributed,
        transactionsCreated: distributionCount,
        unallocatedAmount: companyLeftoverAmount,
        distributionDetails: distributions.filter(d => d.beneficiary_id !== null).map(d => ({
          level: d.level_position,
          beneficiaryId: d.beneficiary_id,
          amount: parseFloat(d.commission_amount.toString()),
          rate: 0 // Not applicable for absolute amounts
        }))
      }
    } catch (error) {
      console.error('Error distributing commissions:', error)
      throw error
    }
  }

  /**
   * Process pending commission transactions and credit user wallets
   */
  static async processPendingCommissions(): Promise<{
    processed: number
    totalAmount: number
    errors: string[]
  }> {
    try {
      if (!supabaseAdmin) {
        throw new Error("Admin client not available")
      }

      // Get all pending commission transactions
      const { data: pendingCommissions, error: fetchError } = await supabaseAdmin
        .from(TABLES.COMMISSION_TRANSACTIONS)
        .select('*')
        .eq('status', 'pending')
        .order('created_at', { ascending: true })

      if (fetchError) {
        throw new Error(`Failed to fetch pending commissions: ${fetchError.message}`)
      }

      if (!pendingCommissions || pendingCommissions.length === 0) {
        return { processed: 0, totalAmount: 0, errors: [] }
      }

      let processedCount = 0
      let totalAmount = 0
      const errors: string[] = []

      for (const commission of pendingCommissions) {
        try {
          // Get beneficiary's wallet
          const { data: wallet, error: walletError } = await supabaseAdmin
            .from('user_wallets')
            .select('id, balance')
            .eq('user_id', commission.beneficiary_id)
            .single()

          if (walletError || !wallet) {
            errors.push(`No wallet found for beneficiary ${commission.beneficiary_id}`)
            continue
          }

          // Create wallet transaction
          const { data: walletTransaction, error: walletTransError } = await supabaseAdmin
            .from('wallet_transactions')
            .insert({
              wallet_id: wallet.id,
              transaction_type: 'commission',
              amount: commission.commission_amount,
              balance_before: wallet.balance,
              balance_after: parseFloat(wallet.balance) + parseFloat(commission.commission_amount),
              description: `Level ${commission.commission_level} commission from subscription`,
              reference_id: commission.subscription_purchase_id,
              reference_type: 'subscription',
              status: 'completed',
              metadata: {
                commission_type: commission.commission_type,
                commission_level: commission.commission_level,
                original_transaction_id: commission.transaction_id
              }
            })
            .select()
            .single()

          if (walletTransError) {
            errors.push(`Failed to create wallet transaction for ${commission.beneficiary_id}: ${walletTransError.message}`)
            continue
          }

          // Update wallet balance
          const { error: balanceError } = await supabaseAdmin
            .from('user_wallets')
            .update({
              balance: parseFloat(wallet.balance) + parseFloat(commission.commission_amount),
              updated_at: new Date().toISOString()
            })
            .eq('id', wallet.id)

          if (balanceError) {
            errors.push(`Failed to update wallet balance for ${commission.beneficiary_id}: ${balanceError.message}`)
            continue
          }

          // Update commission transaction status
          const { error: commissionUpdateError } = await supabaseAdmin
            .from(TABLES.COMMISSION_TRANSACTIONS)
            .update({
              status: 'processed',
              processed_at: new Date().toISOString(),
              wallet_transaction_id: walletTransaction.id
            })
            .eq('id', commission.id)

          if (commissionUpdateError) {
            errors.push(`Failed to update commission status for ${commission.id}: ${commissionUpdateError.message}`)
            continue
          }

          processedCount++
          totalAmount += parseFloat(commission.commission_amount)

        } catch (error) {
          errors.push(`Error processing commission ${commission.id}: ${error instanceof Error ? error.message : 'Unknown error'}`)
        }
      }

      return {
        processed: processedCount,
        totalAmount,
        errors
      }

    } catch (error) {
      console.error('Error processing pending commissions:', error)
      throw error
    }
  }

  /**
   * Get commission summary for admin dashboard
   */
  static async getCommissionSummary(): Promise<{
    totalCommissionsPaid: number
    pendingCommissions: number
    failedCommissions: number
    totalTransactions: number
    topEarners: { userId: string, fullName: string, totalEarned: number }[]
  }> {
    try {
      const [
        totalPaidResult,
        pendingResult,
        failedResult,
        totalTransactionsResult,
        topEarnersResult
      ] = await Promise.all([
        supabase
          .from(TABLES.COMMISSION_TRANSACTIONS)
          .select('commission_amount')
          .eq('status', 'processed'),
        supabase
          .from(TABLES.COMMISSION_TRANSACTIONS)
          .select('commission_amount')
          .eq('status', 'pending'),
        supabase
          .from(TABLES.COMMISSION_TRANSACTIONS)
          .select('*', { count: 'exact', head: true })
          .eq('status', 'failed'),
        supabase
          .from(TABLES.COMMISSION_TRANSACTIONS)
          .select('*', { count: 'exact', head: true }),
        supabase
          .from(TABLES.USERS)
          .select('id, full_name, total_commission_earned')
          .gt('total_commission_earned', 0)
          .order('total_commission_earned', { ascending: false })
          .limit(10)
      ])

      const totalCommissionsPaid = totalPaidResult.data?.reduce((sum, t) => sum + t.commission_amount, 0) || 0
      const pendingCommissions = pendingResult.data?.reduce((sum, t) => sum + t.commission_amount, 0) || 0
      const failedCommissions = failedResult.count || 0
      const totalTransactions = totalTransactionsResult.count || 0
      const topEarners = topEarnersResult.data?.map(user => ({
        userId: user.id,
        fullName: user.full_name || 'Unknown',
        totalEarned: user.total_commission_earned || 0
      })) || []

      return {
        totalCommissionsPaid,
        pendingCommissions,
        failedCommissions,
        totalTransactions,
        topEarners
      }
    } catch (error) {
      console.error('Error getting commission summary:', error)
      throw error
    }
  }

  /**
   * Update commission structure rates
   */
  static async updateCommissionStructure(
    id: string,
    updates: Partial<CommissionStructure>
  ): Promise<void> {
    try {
      const { error } = await supabase
        .from(TABLES.COMMISSION_STRUCTURE)
        .update(updates)
        .eq('id', id)

      if (error) {
        throw new Error(`Failed to update commission structure: ${error.message}`)
      }
    } catch (error) {
      console.error('Error updating commission structure:', error)
      throw error
    }
  }

  /**
   * Get commission transactions for a specific user with filters
   * Excludes gift system commissions from user wallet view
   */
  static async getUserCommissionTransactions(
    userId: string,
    filters: {
      status?: string
      commissionType?: string
      dateFrom?: string
      dateTo?: string
    } = {},
    page: number = 1,
    limit: number = 20
  ): Promise<{ transactions: CommissionTransaction[], total: number }> {
    try {
      const offset = (page - 1) * limit

      let query = supabase
        .from(TABLES.COMMISSION_TRANSACTIONS)
        .select('*')
        .eq('beneficiary_id', userId)
        // Exclude gift system commissions from user wallet view
        .not('commission_type', 'in', '(present_user,annual_present_user,present_leader,annual_present_leader)')

      // Apply filters
      if (filters.status) {
        query = query.eq('status', filters.status)
      }
      if (filters.commissionType) {
        query = query.eq('commission_type', filters.commissionType)
      }
      if (filters.dateFrom) {
        query = query.gte('created_at', filters.dateFrom)
      }
      if (filters.dateTo) {
        query = query.lte('created_at', filters.dateTo)
      }

      const [dataResult, countResult] = await Promise.all([
        query
          .order('created_at', { ascending: false })
          .range(offset, offset + limit - 1),
        query.select('*', { count: 'exact', head: true })
      ])

      if (dataResult.error) {
        throw new Error(`Failed to get commission transactions: ${dataResult.error.message}`)
      }

      if (countResult.error) {
        throw new Error(`Failed to count commission transactions: ${countResult.error.message}`)
      }

      return {
        transactions: dataResult.data || [],
        total: countResult.count || 0
      }
    } catch (error) {
      console.error('Error getting user commission transactions:', error)
      throw error
    }
  }

  /**
   * Get ALL commission transactions for a specific user (including gift system commissions)
   * This method is for admin use only
   */
  static async getAllUserCommissionTransactions(
    userId: string,
    filters: {
      status?: string
      commissionType?: string
      dateFrom?: string
      dateTo?: string
    } = {},
    page: number = 1,
    limit: number = 20
  ): Promise<{ transactions: CommissionTransaction[], total: number }> {
    try {
      const offset = (page - 1) * limit

      let query = supabase
        .from(TABLES.COMMISSION_TRANSACTIONS)
        .select('*')
        .eq('beneficiary_id', userId)
        // Include ALL commission types (including gift system commissions)

      // Apply filters
      if (filters.status) {
        query = query.eq('status', filters.status)
      }
      if (filters.commissionType) {
        query = query.eq('commission_type', filters.commissionType)
      }
      if (filters.dateFrom) {
        query = query.gte('created_at', filters.dateFrom)
      }
      if (filters.dateTo) {
        query = query.lte('created_at', filters.dateTo)
      }

      const [dataResult, countResult] = await Promise.all([
        query
          .order('created_at', { ascending: false })
          .range(offset, offset + limit - 1),
        query.select('*', { count: 'exact', head: true })
      ])

      if (dataResult.error) {
        throw new Error(`Failed to get all commission transactions: ${dataResult.error.message}`)
      }

      if (countResult.error) {
        throw new Error(`Failed to count all commission transactions: ${countResult.error.message}`)
      }

      return {
        transactions: dataResult.data || [],
        total: countResult.count || 0
      }
    } catch (error) {
      console.error('Error getting all user commission transactions:', error)
      throw error
    }
  }

  /**
   * Get detailed commission analytics for admin reports
   */
  static async getCommissionAnalytics(filters: {
    dateFrom?: string
    dateTo?: string
    status?: string
    commissionType?: string
    level?: string
  } = {}): Promise<{
    transactions: CommissionTransaction[]
    total: number
    monthlyData: { month: string, amount: number, count: number }[]
    levelDistribution: { level: number, amount: number, count: number }[]
    typeDistribution: { type: string, amount: number, count: number }[]
  }> {
    try {
      let query = supabase
        .from(TABLES.COMMISSION_TRANSACTIONS)
        .select('*')

      // Apply filters
      if (filters.status) {
        query = query.eq('status', filters.status)
      }
      if (filters.commissionType) {
        query = query.eq('commission_type', filters.commissionType)
      }
      if (filters.level) {
        query = query.eq('commission_level', parseInt(filters.level))
      }
      if (filters.dateFrom) {
        query = query.gte('created_at', filters.dateFrom)
      }
      if (filters.dateTo) {
        query = query.lte('created_at', filters.dateTo)
      }

      const [dataResult, countResult] = await Promise.all([
        query.order('created_at', { ascending: false }).limit(1000),
        query.select('*', { count: 'exact', head: true })
      ])

      if (dataResult.error) {
        throw new Error(`Failed to get commission analytics: ${dataResult.error.message}`)
      }

      if (countResult.error) {
        throw new Error(`Failed to count commission analytics: ${countResult.error.message}`)
      }

      const transactions = dataResult.data || []

      // Calculate monthly data
      const monthlyMap = new Map<string, { amount: number, count: number }>()
      transactions.forEach(t => {
        const month = new Date(t.created_at).toISOString().slice(0, 7) // YYYY-MM
        const existing = monthlyMap.get(month) || { amount: 0, count: 0 }
        monthlyMap.set(month, {
          amount: existing.amount + t.commission_amount,
          count: existing.count + 1
        })
      })

      const monthlyData = Array.from(monthlyMap.entries()).map(([month, data]) => ({
        month,
        ...data
      })).sort((a, b) => a.month.localeCompare(b.month))

      // Calculate level distribution
      const levelMap = new Map<number, { amount: number, count: number }>()
      transactions.forEach(t => {
        const existing = levelMap.get(t.commission_level) || { amount: 0, count: 0 }
        levelMap.set(t.commission_level, {
          amount: existing.amount + t.commission_amount,
          count: existing.count + 1
        })
      })

      const levelDistribution = Array.from(levelMap.entries()).map(([level, data]) => ({
        level,
        ...data
      })).sort((a, b) => a.level - b.level)

      // Calculate type distribution
      const typeMap = new Map<string, { amount: number, count: number }>()
      transactions.forEach(t => {
        const existing = typeMap.get(t.commission_type) || { amount: 0, count: 0 }
        typeMap.set(t.commission_type, {
          amount: existing.amount + t.commission_amount,
          count: existing.count + 1
        })
      })

      const typeDistribution = Array.from(typeMap.entries()).map(([type, data]) => ({
        type,
        ...data
      }))

      return {
        transactions,
        total: countResult.count || 0,
        monthlyData,
        levelDistribution,
        typeDistribution
      }
    } catch (error) {
      console.error('Error getting commission analytics:', error)
      throw error
    }
  }

  /**
   * Get commission transactions with pagination and filters
   */
  static async getCommissionTransactionsWithFilters(
    filters: {
      dateFrom?: string
      dateTo?: string
      status?: string
      commissionType?: string
      level?: string
    } = {},
    page: number = 1,
    limit: number = 50
  ): Promise<{ transactions: CommissionTransaction[], total: number }> {
    try {
      const offset = (page - 1) * limit

      let query = supabase
        .from(TABLES.COMMISSION_TRANSACTIONS)
        .select('*')

      // Apply filters
      if (filters.status) {
        query = query.eq('status', filters.status)
      }
      if (filters.commissionType) {
        query = query.eq('commission_type', filters.commissionType)
      }
      if (filters.level) {
        query = query.eq('commission_level', parseInt(filters.level))
      }
      if (filters.dateFrom) {
        query = query.gte('created_at', filters.dateFrom)
      }
      if (filters.dateTo) {
        query = query.lte('created_at', filters.dateTo)
      }

      const [dataResult, countResult] = await Promise.all([
        query
          .order('created_at', { ascending: false })
          .range(offset, offset + limit - 1),
        query.select('*', { count: 'exact', head: true })
      ])

      if (dataResult.error) {
        throw new Error(`Failed to get commission transactions: ${dataResult.error.message}`)
      }

      if (countResult.error) {
        throw new Error(`Failed to count commission transactions: ${countResult.error.message}`)
      }

      return {
        transactions: dataResult.data || [],
        total: countResult.count || 0
      }
    } catch (error) {
      console.error('Error getting commission transactions with filters:', error)
      throw error
    }
  }

  /**
   * Get commission breakdown by type for a user
   */
  static async getCommissionBreakdown(userId: string): Promise<CommissionBreakdown> {
    try {
      const response = await fetch(`/api/commission-breakdown?userId=${userId}`)
      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || 'Failed to fetch commission breakdown')
      }

      if (!result.success) {
        throw new Error(result.error || 'API request failed')
      }

      // Map the extended data to the basic breakdown format
      const data = result.data
      const breakdown: CommissionBreakdown = {
        directCommission: data.directCommission || 0,
        levelCommission: data.levelCommission || 0,
        rsmBonus: data.rsmBonuses || 0,
        zmBonus: data.zmBonuses || 0,
        okdoiHeadCommission: data.okdoiHeadCommission || 0,
        totalCommissions: data.totalCommissions || 0
      }

      return breakdown
    } catch (error) {
      console.error('Error getting commission breakdown:', error)
      throw error
    }
  }

  /**
   * Get extended commission breakdown with all commission types
   */
  static async getExtendedCommissionBreakdown(userId: string): Promise<{
    directCommission: number
    levelCommission: number
    voucherCommission: number
    festivalBonus: number
    savingCommission: number
    giftCenterCommission: number
    entertainmentCommission: number
    medicalCommission: number
    educationCommission: number
    creditCommission: number
    zmBonuses: number
    petralAllowanceZM: number
    leasingFacilityZM: number
    phoneBillZM: number
    rsmBonuses: number
    petralAllowanceRSM: number
    leasingFacilityRSM: number
    phoneBillRSM: number
    okdoiHeadCommission: number
    leftoverCommission: number
    totalCommissions: number
  }> {
    try {
      const response = await fetch(`/api/commission-breakdown?userId=${userId}`)
      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || 'Failed to fetch commission breakdown')
      }

      if (!result.success) {
        throw new Error(result.error || 'API request failed')
      }

      return result.data

    } catch (error) {
      console.error('Error getting extended commission breakdown:', error)
      throw error
    }
  }
}
