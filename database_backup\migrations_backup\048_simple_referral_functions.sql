-- Simple referral system functions
-- This migration creates basic functions for the referral system

-- Function to increment referral count
CREATE OR REPLACE FUNCTION increment_referral_count(user_id UUID)
RETURNS VOID AS $$
BEGIN
  UPDATE users 
  SET direct_referrals_count = COALESCE(direct_referrals_count, 0) + 1
  WHERE id = user_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to update commission earned
CREATE OR REPLACE FUNCTION update_commission_earned(user_id UUID, amount DECIMAL)
RETURNS VOID AS $$
BEGIN
  UPDATE users 
  SET total_commission_earned = COALESCE(total_commission_earned, 0) + amount
  WHERE id = user_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to generate referral code if not exists
CREATE OR REPLACE FUNCTION ensure_referral_code()
RETURNS TRIGGER AS $$
DECLARE
  new_code VARCHAR(20);
  code_exists BOOLEAN;
BEGIN
  -- Only generate if referral_code is null
  IF NEW.referral_code IS NULL THEN
    LOOP
      -- Generate random 8-character code
      new_code := upper(substring(md5(random()::text) from 1 for 8));
      
      -- Check if code already exists
      SELECT EXISTS(SELECT 1 FROM users WHERE referral_code = new_code) INTO code_exists;
      
      -- Exit loop if code is unique
      EXIT WHEN NOT code_exists;
    END LOOP;
    
    NEW.referral_code := new_code;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to auto-generate referral codes
DROP TRIGGER IF EXISTS trigger_ensure_referral_code ON users;
CREATE TRIGGER trigger_ensure_referral_code
  BEFORE INSERT OR UPDATE ON users
  FOR EACH ROW
  EXECUTE FUNCTION ensure_referral_code();

-- Grant necessary permissions
GRANT EXECUTE ON FUNCTION increment_referral_count(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION update_commission_earned(UUID, DECIMAL) TO authenticated;
