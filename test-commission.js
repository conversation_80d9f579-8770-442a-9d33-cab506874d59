// Simple test script to run commission distribution
// Using built-in fetch (Node.js 18+)

async function testCommissionDistribution() {
  try {
    console.log('Testing commission distribution...');
    
    const response = await fetch('http://localhost:3000/api/test-commission-distribution', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        purchaserId: '7f711398-ed10-4e51-9c8f-f1a4f4490033', // <EMAIL>
        subscriptionId: 'd5db780b-e946-419e-85cf-5acd254a5075',
        packageAmount: 2000
      })
    });

    const result = await response.json();
    console.log('Response:', JSON.stringify(result, null, 2));
    
    if (result.success) {
      console.log('✅ Commission distribution successful!');
    } else {
      console.log('❌ Commission distribution failed:', result.error);
    }
    
  } catch (error) {
    console.error('Error:', error.message);
  }
}

testCommissionDistribution();
