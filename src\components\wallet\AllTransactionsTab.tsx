'use client'

import React, { useState, useEffect } from 'react'
import { 
  Filter, 
  Search, 
  ArrowUpRight, 
  ArrowDownLeft, 
  Banknote, 
  Package,
  ChevronDown,
  ChevronUp,
  TrendingUp
} from 'lucide-react'
import { formatCurrency } from '@/lib/utils'
import { CommissionSystemService } from '@/lib/services/commissionSystem'

interface WalletTransaction {
  id: string
  wallet_id: string
  transaction_type: 'deposit' | 'withdrawal' | 'transfer_in' | 'transfer_out' | 'purchase' | 'refund' | 'commission'
  amount: number
  currency: string
  balance_before: number
  balance_after: number
  reference_id?: string
  reference_type?: string
  reference_number?: string
  description?: string
  metadata?: Record<string, any>
  status: 'pending' | 'completed' | 'failed' | 'cancelled'
  created_at: string
  updated_at: string
  p2p_transfer?: {
    sender?: { full_name: string }
    receiver?: { full_name: string }
  }
}

interface GroupedCommissionTransaction {
  subscription_purchase_id: string
  package_value: number
  total_amount: number
  transaction_count: number
  created_at: string
  transactions: any[]
}

interface AllTransactionsTabProps {
  userId: string
  walletTransactions: WalletTransaction[]
}

const getTransactionIcon = (transactionType: string) => {
  switch (transactionType) {
    case 'deposit':
      return <ArrowDownLeft className="h-5 w-5 text-green-500" />
    case 'withdrawal':
      return <ArrowUpRight className="h-5 w-5 text-red-500" />
    case 'transfer_in':
      return <ArrowDownLeft className="h-5 w-5 text-blue-500" />
    case 'transfer_out':
      return <ArrowUpRight className="h-5 w-5 text-orange-500" />
    case 'purchase':
      return <Package className="h-5 w-5 text-purple-500" />
    case 'refund':
      return <ArrowDownLeft className="h-5 w-5 text-green-500" />
    case 'commission':
      return <TrendingUp className="h-5 w-5 text-blue-500" />
    default:
      return <Banknote className="h-5 w-5 text-gray-500" />
  }
}

const getTransactionColor = (transactionType: string) => {
  switch (transactionType) {
    case 'deposit':
    case 'transfer_in':
    case 'refund':
    case 'commission':
      return 'text-green-600'
    case 'withdrawal':
    case 'transfer_out':
    case 'purchase':
      return 'text-red-600'
    default:
      return 'text-gray-600'
  }
}

const getStatusIcon = (status: string) => {
  switch (status) {
    case 'completed':
      return <div className="w-2 h-2 bg-green-500 rounded-full"></div>
    case 'pending':
      return <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
    case 'failed':
    case 'cancelled':
      return <div className="w-2 h-2 bg-red-500 rounded-full"></div>
    default:
      return <div className="w-2 h-2 bg-gray-500 rounded-full"></div>
  }
}

export default function AllTransactionsTab({ userId, walletTransactions }: AllTransactionsTabProps) {
  const [groupedCommissions, setGroupedCommissions] = useState<GroupedCommissionTransaction[]>([])
  const [expandedGroups, setExpandedGroups] = useState<Set<string>>(new Set())
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchCommissionTransactions()
  }, [userId])

  const fetchCommissionTransactions = async () => {
    try {
      setLoading(true)
      // Fetch all commission transactions (increase limit to get all)
      const { transactions: commissionTransactions } = await CommissionSystemService.getUserCommissionTransactions(
        userId,
        {}, // no filters
        1, // page 1
        1000 // high limit to get all transactions
      )

      // Group transactions by subscription_purchase_id
      const grouped = commissionTransactions.reduce((acc, transaction) => {
        const key = transaction.subscription_purchase_id || 'no_subscription'

        if (!acc[key]) {
          acc[key] = {
            subscription_purchase_id: key,
            package_value: transaction.package_value || 0,
            total_amount: 0,
            transaction_count: 0,
            created_at: transaction.created_at,
            transactions: []
          }
        }

        acc[key].total_amount += parseFloat(transaction.commission_amount.toString())
        acc[key].transaction_count += 1
        acc[key].transactions.push(transaction)

        // Use the earliest transaction date
        if (new Date(transaction.created_at) < new Date(acc[key].created_at)) {
          acc[key].created_at = transaction.created_at
        }

        return acc
      }, {} as Record<string, GroupedCommissionTransaction>)

      // Convert to array and sort by date (newest first)
      const groupedArray = Object.values(grouped).sort((a, b) =>
        new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
      )

      setGroupedCommissions(groupedArray)
    } catch (error) {
      console.error('Error fetching commission transactions:', error)
    } finally {
      setLoading(false)
    }
  }

  const toggleGroupExpansion = (subscriptionId: string) => {
    const newExpanded = new Set(expandedGroups)
    if (newExpanded.has(subscriptionId)) {
      newExpanded.delete(subscriptionId)
    } else {
      newExpanded.add(subscriptionId)
    }
    setExpandedGroups(newExpanded)
  }

  // Combine and sort all transactions
  const allTransactions = [
    // Filter out individual commission transactions (they're now grouped)
    ...walletTransactions.filter(t =>
      t.transaction_type !== 'commission' &&
      !t.description?.includes('commission from subscription')
    ),
    ...groupedCommissions.map(group => ({
      id: `commission-group-${group.subscription_purchase_id}`,
      transaction_type: 'commission' as const,
      amount: group.total_amount,
      created_at: group.created_at,
      status: 'completed' as const,
      description: `Commission from Rs ${formatCurrency(group.package_value)} Package (${group.transaction_count} types)`,
      isCommissionGroup: true,
      commissionGroup: group
    }))
  ].sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())

  if (loading) {
    return (
      <div className="p-6">
        <div className="flex items-center justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      </div>
    )
  }

  return (
    <div className="p-6">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-gray-900">All Transactions</h3>
        <div className="flex items-center space-x-2">
          <button className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100">
            <Filter className="h-4 w-4" />
          </button>
          <button className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100">
            <Search className="h-4 w-4" />
          </button>
        </div>
      </div>
      <div className="space-y-4">
        {allTransactions.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            No transactions found
          </div>
        ) : (
          allTransactions.map((transaction: any) => {
            if (transaction.isCommissionGroup) {
              const group = transaction.commissionGroup
              return (
                <div key={transaction.id} className="border border-gray-200 rounded-lg overflow-hidden">
                  {/* Commission Group Header */}
                  <div 
                    className="flex items-center justify-between py-4 px-4 cursor-pointer hover:bg-gray-50 transition-colors"
                    onClick={() => toggleGroupExpansion(group.subscription_purchase_id)}
                  >
                    <div className="flex items-center">
                      <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                        <Package className="h-5 w-5 text-blue-600" />
                      </div>
                      <div>
                        <p className="text-sm font-medium text-gray-900">Commission Earned</p>
                        <p className="text-sm text-gray-500">{transaction.description}</p>
                        <p className="text-xs text-gray-400">
                          {new Date(transaction.created_at).toLocaleString()}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-3">
                      <div className="text-right">
                        <div className="flex items-center">
                          <p className="text-sm font-medium mr-2 text-green-600">
                            +{formatCurrency(transaction.amount)}
                          </p>
                          {getStatusIcon(transaction.status)}
                        </div>
                      </div>
                      {expandedGroups.has(group.subscription_purchase_id) ? (
                        <ChevronUp className="h-5 w-5 text-gray-400" />
                      ) : (
                        <ChevronDown className="h-5 w-5 text-gray-400" />
                      )}
                    </div>
                  </div>

                  {/* Expanded Commission Details */}
                  {expandedGroups.has(group.subscription_purchase_id) && (
                    <div className="bg-gray-50 px-4 py-2">
                      <p className="text-xs text-gray-600 mb-2">Commission breakdown:</p>
                      <div className="grid grid-cols-2 gap-2 text-xs">
                        {group.transactions.map((ct: any, index: number) => (
                          <div key={index} className="flex justify-between">
                            <span className="text-gray-600 capitalize">{ct.commission_type.replace(/_/g, ' ')}</span>
                            <span className="text-green-600">+{formatCurrency(ct.commission_amount)}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              )
            } else {
              // Regular wallet transaction
              return (
                <div key={transaction.id} className="flex items-center justify-between py-4 border-b border-gray-100 last:border-b-0">
                  <div className="flex items-center">
                    {getTransactionIcon(transaction.transaction_type)}
                    <div className="ml-3">
                      <p className="text-sm font-medium text-gray-900 capitalize">
                        {transaction.transaction_type.replace('_', ' ')}
                      </p>
                      <p className="text-sm text-gray-500">
                        {transaction.reference_type === 'p2p_transfer' && transaction.p2p_transfer ? (
                          transaction.transaction_type === 'transfer_out' ? (
                            `Transfer to ${transaction.p2p_transfer.receiver?.full_name || 'Unknown'}`
                          ) : (
                            `Transfer from ${transaction.p2p_transfer.sender?.full_name || 'Unknown'}`
                          )
                        ) : (
                          transaction.description
                        )}
                      </p>
                      {transaction.reference_number && (
                        <p className="text-xs text-gray-400 font-mono">
                          Ref: {transaction.reference_number}
                        </p>
                      )}
                      <p className="text-xs text-gray-400">
                        {new Date(transaction.created_at).toLocaleString()}
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="flex items-center">
                      <p className={`text-sm font-medium mr-2 ${getTransactionColor(transaction.transaction_type)}`}>
                        {transaction.transaction_type.includes('in') || transaction.transaction_type === 'deposit' || transaction.transaction_type === 'refund' ? '+' : '-'}
                        {formatCurrency(transaction.amount)}
                      </p>
                      {getStatusIcon(transaction.status)}
                    </div>
                    <p className="text-xs text-gray-500">
                      Balance: {formatCurrency(transaction.balance_after || 0)}
                    </p>
                  </div>
                </div>
              )
            }
          })
        )}
      </div>
    </div>
  )
}
