const fs = require('fs');
const path = require('path');

// Create SQL backup using pg_dump approach
async function createSQLBackup() {
  console.log('🗄️  Creating SQL-based backup...');
  
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const sqlBackupDir = path.join(__dirname, 'sql_backup');
  
  if (!fs.existsSync(sqlBackupDir)) {
    fs.mkdirSync(sqlBackupDir, { recursive: true });
  }
  
  // Create comprehensive SQL backup script
  const sqlBackupScript = `
-- OKDOI Complete Database Backup
-- Generated: ${new Date().toISOString()}
-- Project: vnmydqbwjjufnxngpnqo

-- ============================================================================
-- EXTENSIONS
-- ============================================================================

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- ============================================================================
-- CUSTOM TYPES (if any)
-- ============================================================================

-- Add any custom enum types here
-- Example: CREATE TYPE user_role AS ENUM ('user', 'admin', 'super_admin');

-- ============================================================================
-- TABLES STRUCTURE
-- ============================================================================

-- This section would contain CREATE TABLE statements
-- Note: Use supabase db dump for complete schema

-- ============================================================================
-- RLS POLICIES
-- ============================================================================

-- Enable RLS on all tables
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE ads ENABLE ROW LEVEL SECURITY;
ALTER TABLE categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE subcategories ENABLE ROW LEVEL SECURITY;
ALTER TABLE ad_images ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_favorites ENABLE ROW LEVEL SECURITY;
ALTER TABLE chat_conversations ENABLE ROW LEVEL SECURITY;
ALTER TABLE chat_messages ENABLE ROW LEVEL SECURITY;
ALTER TABLE vendor_shops ENABLE ROW LEVEL SECURITY;
ALTER TABLE shop_products ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_wallets ENABLE ROW LEVEL SECURITY;
ALTER TABLE wallet_transactions ENABLE ROW LEVEL SECURITY;

-- Add RLS policies (these would need to be extracted from your current setup)
-- Example policies:

-- Users can view their own data
CREATE POLICY "Users can view own data" ON users
  FOR SELECT USING (auth.uid() = id);

-- Users can update their own data
CREATE POLICY "Users can update own data" ON users
  FOR UPDATE USING (auth.uid() = id);

-- Public can view active ads
CREATE POLICY "Public can view active ads" ON ads
  FOR SELECT USING (status = 'active');

-- Users can manage their own ads
CREATE POLICY "Users can manage own ads" ON ads
  FOR ALL USING (auth.uid() = user_id);

-- ============================================================================
-- FUNCTIONS
-- ============================================================================

-- Add custom functions here
-- These would need to be extracted from your current database

-- Example function for updating timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- ============================================================================
-- TRIGGERS
-- ============================================================================

-- Add triggers for updated_at columns
-- Example:
-- CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users
--   FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- ============================================================================
-- INDEXES
-- ============================================================================

-- Add performance indexes
-- Examples:
-- CREATE INDEX idx_ads_user_id ON ads(user_id);
-- CREATE INDEX idx_ads_category_id ON ads(category_id);
-- CREATE INDEX idx_ads_status ON ads(status);
-- CREATE INDEX idx_ads_created_at ON ads(created_at);

-- ============================================================================
-- STORAGE BUCKETS
-- ============================================================================

-- Create storage buckets
-- Note: These need to be created via Supabase dashboard or API

-- INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
-- VALUES 
--   ('ad-images', 'ad-images', true, 52428800, ARRAY['image/jpeg', 'image/png', 'image/webp', 'image/gif']),
--   ('kyc-documents', 'kyc-documents', false, 10485760, ARRAY['image/jpeg', 'image/png', 'application/pdf']);

-- ============================================================================
-- STORAGE POLICIES
-- ============================================================================

-- Add storage bucket policies
-- Examples:
-- CREATE POLICY "Public can view ad images" ON storage.objects
--   FOR SELECT USING (bucket_id = 'ad-images');

-- CREATE POLICY "Users can upload ad images" ON storage.objects
--   FOR INSERT WITH CHECK (bucket_id = 'ad-images' AND auth.role() = 'authenticated');

-- ============================================================================
-- INITIAL DATA
-- ============================================================================

-- Insert initial/seed data
-- This would include categories, admin settings, etc.

-- ============================================================================
-- FINAL NOTES
-- ============================================================================

-- This is a template SQL backup script.
-- For a complete backup, you should:
-- 1. Use 'supabase db dump' for complete schema
-- 2. Export data using the JSON backup scripts
-- 3. Manually recreate storage buckets and policies
-- 4. Test the restore process thoroughly

-- End of backup script
`;

  const sqlBackupFile = path.join(sqlBackupDir, `backup_template_${timestamp}.sql`);
  fs.writeFileSync(sqlBackupFile, sqlBackupScript);
  
  console.log(`✅ SQL backup template created: ${sqlBackupFile}`);
  
  return sqlBackupFile;
}

// Create a comprehensive backup instructions file
async function createBackupInstructions() {
  console.log('📋 Creating backup instructions...');
  
  const instructions = `# Complete Supabase Backup Instructions

## What We Have Backed Up

### ✅ Successfully Backed Up:
1. **All Table Data** (53 tables, 1,201+ records)
   - Users, ads, categories, shops, products, orders
   - Wallet transactions, commission data
   - KYC submissions, referral hierarchy
   - Admin settings and configurations

2. **Storage Buckets** (2 buckets)
   - ad-images: 11 files (public bucket)
   - kyc-documents: 3 files (private bucket)

3. **Migration Files** (56 migration files)
   - Complete database evolution history
   - All schema changes preserved

### ⚠️ Partially Backed Up (Manual Steps Required):
1. **RLS Policies** - Need manual extraction
2. **Database Functions** - Need manual extraction  
3. **Triggers** - Need manual extraction
4. **Views** - Need manual extraction
5. **Indexes** - Need manual extraction
6. **Constraints** - Need manual extraction

## Complete Backup Process

### Step 1: Use Supabase CLI for Schema
\`\`\`bash
# Update Supabase CLI first
npm install -g @supabase/cli@latest

# Create complete schema backup
supabase db dump --schema-only --file schema_complete.sql

# Create complete data backup  
supabase db dump --data-only --file data_complete.sql

# Create complete backup (schema + data)
supabase db dump --file complete_database.sql
\`\`\`

### Step 2: Manual RLS Policies Extraction
1. Go to Supabase Dashboard → Authentication → Policies
2. Copy each policy definition
3. Save to \`rls_policies.sql\`

### Step 3: Manual Functions Extraction
1. Go to Supabase Dashboard → SQL Editor
2. Run: \`SELECT * FROM pg_proc WHERE pronamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'public');\`
3. Extract function definitions
4. Save to \`functions.sql\`

### Step 4: Storage Bucket Policies
1. Go to Supabase Dashboard → Storage
2. Copy bucket policies for each bucket
3. Save to \`storage_policies.sql\`

### Step 5: Edge Functions (if any)
\`\`\`bash
# List edge functions
supabase functions list

# Download each function
supabase functions download <function-name>
\`\`\`

## Restore Process

### 1. Create New Supabase Project
\`\`\`bash
supabase projects create "okdoi-restore"
supabase link --project-ref <new-project-ref>
\`\`\`

### 2. Restore Schema
\`\`\`bash
supabase db reset
psql -h <host> -U postgres -d postgres -f schema_complete.sql
\`\`\`

### 3. Restore Data
\`\`\`bash
# Use our JSON restore script
node restore_backup.js complete_backup_2025-09-08T19-32-54-897Z.json

# Or use SQL dump
psql -h <host> -U postgres -d postgres -f data_complete.sql
\`\`\`

### 4. Recreate Storage Buckets
- Manually create buckets in dashboard
- Upload files from backup
- Apply storage policies

### 5. Apply RLS Policies
\`\`\`bash
psql -h <host> -U postgres -d postgres -f rls_policies.sql
\`\`\`

### 6. Restore Functions and Triggers
\`\`\`bash
psql -h <host> -U postgres -d postgres -f functions.sql
\`\`\`

## Files in This Backup

### Data Backups:
- \`complete_backup_2025-09-08T19-32-54-897Z.json\` - **MAIN DATA BACKUP**
- \`complete_supabase_backup_2025-09-08T19-33-13-857Z.json\` - **COMPLETE BACKUP**

### Schema Backups:
- \`migrations_backup/\` - All 56 migration files
- \`database_schema_backup_*.json\` - Schema metadata

### Storage:
- Storage bucket information included in complete backup
- File lists and metadata preserved

### Tools:
- \`restore_backup.js\` - Data restoration script
- \`verify_backup.js\` - Backup verification script

## Security Notes

🔒 **IMPORTANT**: 
- Backup files contain sensitive user data
- Store in encrypted, secure locations
- Limit access to authorized personnel only
- Consider encrypting backup files

## Testing Restore

Always test restore process on a development environment first:
1. Create test Supabase project
2. Restore schema and data
3. Verify all functionality works
4. Test user authentication and permissions
5. Verify storage bucket access

## Support

For issues with backup/restore:
1. Check Supabase CLI version (\`supabase --version\`)
2. Verify database connection
3. Check file permissions
4. Review error logs carefully

---
Generated: ${new Date().toISOString()}
Project: vnmydqbwjjufnxngpnqo (OKDOI Marketplace)
`;

  const instructionsFile = path.join(__dirname, 'COMPLETE_BACKUP_INSTRUCTIONS.md');
  fs.writeFileSync(instructionsFile, instructions);
  
  console.log(`✅ Backup instructions created: ${instructionsFile}`);
  
  return instructionsFile;
}

// Run both functions
async function main() {
  try {
    await createSQLBackup();
    await createBackupInstructions();
    console.log('\n🎉 SQL backup and instructions completed!');
  } catch (error) {
    console.error('❌ Error:', error);
  }
}

if (require.main === module) {
  main();
}

module.exports = { createSQLBackup, createBackupInstructions };
