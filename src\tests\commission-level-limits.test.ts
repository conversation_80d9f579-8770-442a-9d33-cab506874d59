import { supabase, TABLES } from '@/lib/supabase'
import { CommissionSystemService } from '@/lib/services/commissionSystem'
import { ReferralSystemService } from '@/lib/services/referralSystem'

describe('Commission Level Limits by User Type', () => {
  // Helper function to create test users
  const createTestUser = async (email: string, userType: string = 'user') => {
    const { data, error } = await supabase
      .from(TABLES.USERS)
      .insert({
        email: `${email}@test.com`,
        full_name: `Test ${email}`,
        user_type: userType,
        role: 'user'
      })
      .select()
      .single()

    if (error) throw error
    return data
  }

  // Helper function to create a deep referral hierarchy
  const createDeepHierarchy = async () => {
    // Create OKDOI Head
    const okdoiHead = await createTestUser('okdoi-head', 'okdoi_head')
    
    // Create ZM
    const zm = await createTestUser('zonal-manager', 'zonal_manager')
    await ReferralSystemService.placeUserInHierarchy(zm.id, okdoiHead.id)
    
    // Create RSM
    const rsm = await createTestUser('rsm', 'rsm')
    await ReferralSystemService.placeUserInHierarchy(rsm.id, zm.id)
    
    // Create a chain of 15 normal users (levels 1-15 from RSM)
    const users = []
    let previousUserId = rsm.id
    
    for (let i = 1; i <= 15; i++) {
      const user = await createTestUser(`user-level-${i}`, 'user')
      await ReferralSystemService.placeUserInHierarchy(user.id, previousUserId)
      users.push(user)
      previousUserId = user.id
    }
    
    return {
      okdoiHead,
      zm,
      rsm,
      users,
      purchaser: users[14] // The 15th level user makes the purchase
    }
  }

  // Clean up test data
  const cleanupTestData = async () => {
    await supabase.from(TABLES.COMMISSION_TRANSACTIONS).delete().like('transaction_id', 'COM-%')
    await supabase.from(TABLES.REFERRAL_HIERARCHY).delete().neq('id', '00000000-0000-0000-0000-000000000000')
    await supabase.from(TABLES.USERS).delete().like('email', '%@test.com')
  }

  beforeEach(async () => {
    await cleanupTestData()
  })

  afterEach(async () => {
    await cleanupTestData()
  })

  describe('User Type Level Limits', () => {
    it('should test commission level limit function for different user types', async () => {
      // Test the database function directly
      const { data: zmLimit } = await supabase.rpc('get_commission_level_limit', {
        beneficiary_user_type: 'zonal_manager'
      })
      expect(zmLimit).toBe(999)

      const { data: rsmLimit } = await supabase.rpc('get_commission_level_limit', {
        beneficiary_user_type: 'rsm'
      })
      expect(rsmLimit).toBe(999)

      const { data: userLimit } = await supabase.rpc('get_commission_level_limit', {
        beneficiary_user_type: 'user'
      })
      expect(userLimit).toBe(10)

      const { data: okdoiLimit } = await supabase.rpc('get_commission_level_limit', {
        beneficiary_user_type: 'okdoi_head'
      })
      expect(okdoiLimit).toBe(999)
    })

    it('should distribute commissions correctly with user type level limits', async () => {
      const hierarchy = await createDeepHierarchy()
      const { okdoiHead, zm, rsm, users, purchaser } = hierarchy

      // Simulate a subscription purchase by the deepest user (level 15)
      const subscriptionId = 'test-subscription-level-limits'
      const packageAmount = 5000

      // Distribute commissions
      const result = await CommissionSystemService.distributeCommissions(
        purchaser.id,
        subscriptionId,
        packageAmount
      )

      expect(result.totalDistributed).toBeGreaterThan(0)
      expect(result.transactionsCreated).toBeGreaterThan(0)

      // Get all commission transactions for this purchase
      const { data: transactions } = await supabase
        .from(TABLES.COMMISSION_TRANSACTIONS)
        .select('*, beneficiary:users!commission_transactions_beneficiary_id_fkey(user_type)')
        .eq('subscription_purchase_id', subscriptionId)
        .order('commission_level')

      expect(transactions).toBeTruthy()
      expect(transactions!.length).toBeGreaterThan(0)

      // Analyze transactions by beneficiary type
      const zmTransactions = transactions!.filter(t => t.beneficiary?.user_type === 'zonal_manager')
      const rsmTransactions = transactions!.filter(t => t.beneficiary?.user_type === 'rsm')
      const userTransactions = transactions!.filter(t => t.beneficiary?.user_type === 'user')
      const okdoiHeadTransactions = transactions!.filter(t => t.beneficiary?.user_type === 'okdoi_head')

      // ZM should receive commission (unlimited levels)
      expect(zmTransactions.length).toBeGreaterThan(0)
      console.log(`ZM received ${zmTransactions.length} commission transactions`)

      // RSM should receive commission (unlimited levels)
      expect(rsmTransactions.length).toBeGreaterThan(0)
      console.log(`RSM received ${rsmTransactions.length} commission transactions`)

      // Normal users should only receive commissions up to level 10
      const userLevels = userTransactions.map(t => t.commission_level)
      const maxUserLevel = Math.max(...userLevels)
      expect(maxUserLevel).toBeLessThanOrEqual(10)
      console.log(`Normal users received commissions up to level ${maxUserLevel}`)

      // OKDOI Head should receive unallocated commissions from levels beyond user limits
      expect(okdoiHeadTransactions.length).toBeGreaterThan(0)
      console.log(`OKDOI Head received ${okdoiHeadTransactions.length} commission transactions`)

      // Check for level limit exceeded transactions
      const levelLimitExceededTransactions = transactions!.filter(t => 
        t.commission_type.includes('level_limit_exceeded')
      )
      expect(levelLimitExceededTransactions.length).toBeGreaterThan(0)
      console.log(`${levelLimitExceededTransactions.length} transactions were redirected due to level limits`)
    })

    it('should handle normal user 10-level limit correctly', async () => {
      // Create a hierarchy where a normal user is at level 5, and we have users up to level 15
      const okdoiHead = await createTestUser('okdoi-head-2', 'okdoi_head')
      const normalUser = await createTestUser('normal-user-beneficiary', 'user')
      await ReferralSystemService.placeUserInHierarchy(normalUser.id, okdoiHead.id)

      // Create 15 levels below the normal user
      let previousUserId = normalUser.id
      const deepUsers = []
      
      for (let i = 1; i <= 15; i++) {
        const user = await createTestUser(`deep-user-${i}`, 'user')
        await ReferralSystemService.placeUserInHierarchy(user.id, previousUserId)
        deepUsers.push(user)
        previousUserId = user.id
      }

      // The deepest user makes a purchase
      const purchaser = deepUsers[14] // 15th level user
      const subscriptionId = 'test-normal-user-limit'
      const packageAmount = 2000

      await CommissionSystemService.distributeCommissions(
        purchaser.id,
        subscriptionId,
        packageAmount
      )

      // Check transactions for the normal user beneficiary
      const { data: normalUserTransactions } = await supabase
        .from(TABLES.COMMISSION_TRANSACTIONS)
        .select('*')
        .eq('subscription_purchase_id', subscriptionId)
        .eq('beneficiary_id', normalUser.id)

      // Normal user should only receive commissions up to level 10
      const levels = normalUserTransactions!.map(t => t.commission_level)
      const maxLevel = Math.max(...levels)
      expect(maxLevel).toBeLessThanOrEqual(10)
      
      // Should not receive commission from level 11 and beyond
      const beyondLimitLevels = levels.filter(level => level > 10)
      expect(beyondLimitLevels.length).toBe(0)

      console.log(`Normal user received commissions for levels: ${levels.join(', ')}`)
    })

    it('should allow ZM to receive commissions from entire downline', async () => {
      // Create a hierarchy where ZM has a very deep downline
      const okdoiHead = await createTestUser('okdoi-head-3', 'okdoi_head')
      const zm = await createTestUser('zm-unlimited', 'zonal_manager')
      await ReferralSystemService.placeUserInHierarchy(zm.id, okdoiHead.id)

      // Create 20 levels below the ZM
      let previousUserId = zm.id
      const deepUsers = []
      
      for (let i = 1; i <= 20; i++) {
        const user = await createTestUser(`zm-deep-user-${i}`, 'user')
        await ReferralSystemService.placeUserInHierarchy(user.id, previousUserId)
        deepUsers.push(user)
        previousUserId = user.id
      }

      // The deepest user makes a purchase
      const purchaser = deepUsers[19] // 20th level user
      const subscriptionId = 'test-zm-unlimited'
      const packageAmount = 10000

      await CommissionSystemService.distributeCommissions(
        purchaser.id,
        subscriptionId,
        packageAmount
      )

      // Check transactions for the ZM
      const { data: zmTransactions } = await supabase
        .from(TABLES.COMMISSION_TRANSACTIONS)
        .select('*')
        .eq('subscription_purchase_id', subscriptionId)
        .eq('beneficiary_id', zm.id)

      // ZM should receive commission from the purchase (level 20 from ZM's perspective)
      expect(zmTransactions!.length).toBeGreaterThan(0)
      
      const levels = zmTransactions!.map(t => t.commission_level)
      const maxLevel = Math.max(...levels)
      
      // ZM should be able to receive from level 20 (beyond normal 10-level limit)
      expect(maxLevel).toBeGreaterThan(10)
      
      console.log(`ZM received commissions for levels: ${levels.join(', ')}, max level: ${maxLevel}`)
    })
  })
})
