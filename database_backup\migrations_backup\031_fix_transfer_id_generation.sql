-- Fix Transfer ID Generation Functions
-- Fixes LPAD function calls that don't properly cast BIGINT to TEXT

-- Fix generate_transfer_id function
CREATE OR REPLACE FUNCTION generate_transfer_id()
RETURNS TEXT AS $$
BEGIN
    RETURN 'TRF' || TO_CHAR(NOW(), 'YYYYMMDD') || 
           LPAD((EXTRACT(EPOCH FROM NOW())::BIGINT % 100000)::TEXT, 5, '0') || 
           LPAD(FLOOR(RANDOM() * 1000)::TEXT, 3, '0');
END;
$$ LANGUAGE plpgsql;

-- Fix generate_merchant_transaction_id function (if needed)
CREATE OR REPLACE FUNCTION generate_merchant_transaction_id()
RETURNS VARCHAR(50) AS $$
BEGIN
    RETURN 'MWT' || TO_CHAR(NOW(), 'YYYYMMDD') || 
           LPAD((EXTRACT(EPOCH FROM NOW())::BIGINT % 100000)::TEXT, 5, '0') || 
           LPAD((FLOOR(RANDOM() * 1000)::INT)::TEXT, 3, '0');
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Fix generate_order_number function (if needed)
CREATE OR REPLACE FUNCTION generate_order_number()
RETURNS VARCHAR(50) AS $$
DECLARE
    new_number varchar(50);
    counter integer := 0;
BEGIN
    LOOP
        new_number := 'ORD-' || TO_CHAR(NOW(), 'YYYYMMDD') || '-' ||
                     LPAD((EXTRACT(EPOCH FROM NOW())::BIGINT % 100000)::TEXT, 5, '0');

        -- Check if this number already exists
        IF NOT EXISTS (SELECT 1 FROM shop_orders WHERE order_number = new_number) THEN
            RETURN new_number;
        END IF;

        counter := counter + 1;
        IF counter > 100 THEN
            -- Fallback with random component
            new_number := 'ORD-' || TO_CHAR(NOW(), 'YYYYMMDD') || '-' ||
                         LPAD(FLOOR(RANDOM() * 99999)::TEXT, 5, '0');
            EXIT;
        END IF;
    END LOOP;

    RETURN new_number;
END;
$$ LANGUAGE plpgsql;

-- Fix transfer_merchant_to_main_wallet function to use correct column names and transaction types
CREATE OR REPLACE FUNCTION transfer_merchant_to_main_wallet(
    p_merchant_wallet_id UUID,
    p_amount DECIMAL(12,2),
    p_user_id UUID
)
RETURNS UUID AS $$
DECLARE
    v_merchant_balance DECIMAL(12,2);
    v_user_wallet_id UUID;
    v_user_balance DECIMAL(12,2);
    v_transfer_id VARCHAR(50);
    v_merchant_transaction_id UUID;
    v_main_transaction_id UUID;
    v_transfer_record_id UUID;
BEGIN
    -- Check merchant wallet balance
    SELECT balance INTO v_merchant_balance
    FROM merchant_wallets
    WHERE id = p_merchant_wallet_id AND user_id = p_user_id AND is_active = TRUE;

    IF v_merchant_balance IS NULL THEN
        RAISE EXCEPTION 'Merchant wallet not found or inactive';
    END IF;

    IF v_merchant_balance < p_amount THEN
        RAISE EXCEPTION 'Insufficient merchant wallet balance';
    END IF;

    -- Get user main wallet
    SELECT id, balance INTO v_user_wallet_id, v_user_balance
    FROM user_wallets
    WHERE user_id = p_user_id AND is_active = TRUE;

    IF v_user_wallet_id IS NULL THEN
        RAISE EXCEPTION 'Main wallet not found or inactive';
    END IF;

    -- Generate transfer ID
    v_transfer_id := generate_transfer_id();

    -- Debit merchant wallet
    UPDATE merchant_wallets
    SET
        balance = balance - p_amount,
        total_withdrawn = total_withdrawn + p_amount,
        updated_at = NOW()
    WHERE id = p_merchant_wallet_id;

    -- Credit main wallet
    UPDATE user_wallets
    SET
        balance = balance + p_amount,
        updated_at = NOW()
    WHERE id = v_user_wallet_id;

    -- Create merchant wallet transaction (debit)
    INSERT INTO merchant_wallet_transactions (
        merchant_wallet_id,
        transaction_id,
        type,
        category,
        amount,
        balance_before,
        balance_after,
        description,
        reference_type
    ) VALUES (
        p_merchant_wallet_id,
        v_transfer_id || '_OUT',
        'debit',
        'transfer_to_main',
        p_amount,
        v_merchant_balance,
        v_merchant_balance - p_amount,
        'Transfer to main wallet',
        'transfer'
    ) RETURNING id INTO v_merchant_transaction_id;

    -- Create main wallet transaction (credit) - using correct column names and transaction type
    INSERT INTO wallet_transactions (
        wallet_id,
        reference_number,
        transaction_type,
        amount,
        currency,
        balance_before,
        balance_after,
        description,
        reference_type,
        status
    ) VALUES (
        v_user_wallet_id,
        v_transfer_id || '_IN',
        'transfer_in',  -- Use correct transaction type
        p_amount,
        'LKR',
        v_user_balance,
        v_user_balance + p_amount,
        'Transfer from merchant wallet',
        'transfer',
        'completed'
    ) RETURNING id INTO v_main_transaction_id;

    -- Create transfer record
    INSERT INTO merchant_to_main_transfers (
        merchant_wallet_id,
        user_wallet_id,
        transfer_id,
        amount,
        status,
        merchant_transaction_id,
        main_transaction_id,
        completed_at
    ) VALUES (
        p_merchant_wallet_id,
        v_user_wallet_id,
        v_transfer_id,
        p_amount,
        'completed',
        v_merchant_transaction_id,
        v_main_transaction_id,
        NOW()
    ) RETURNING id INTO v_transfer_record_id;

    RETURN v_transfer_record_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
