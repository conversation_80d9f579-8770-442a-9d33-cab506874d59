'use client'

import React, { useState, useEffect } from 'react'
import { 
  Bar<PERSON>hart3, 
  TrendingUp, 
  Users, 
  DollarSign, 
  Download, 
  Calendar, 
  Filter, 
  Eye,
  Package,
  Receipt,
  ChevronDown,
  ChevronUp,
  Search,
  RefreshCw,
  Crown,
  Gift,
  Shield,
  Heart,
  GraduationCap,
  CreditCard
} from 'lucide-react'
import AdminLayout from '@/components/admin/AdminLayout'
import Button from '@/components/ui/Button'
import Input from '@/components/ui/Input'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'

interface CommissionTransaction {
  id: string
  transaction_id: string
  user_id: string
  beneficiary_id: string
  subscription_purchase_id: string
  commission_type: string
  commission_level: number
  package_value: number
  commission_rate: number
  commission_amount: number
  status: string
  created_at: string
  processed_at?: string
  user: {
    email: string
    full_name?: string
    user_type?: string
  }
  beneficiary: {
    email: string
    full_name?: string
    user_type?: string
  }
}

interface SubscriptionPurchase {
  id: string
  user_id: string
  package_id: string
  purchased_at: string
  user: {
    email: string
    full_name?: string
    user_type?: string
  }
  package: {
    name: string
    price: number
  }
  commission_transactions: CommissionTransaction[]
}

interface CommissionStats {
  totalPurchases: number
  totalCommissions: number
  totalTransactions: number
  uniqueUsers: number
  byType: Record<string, { count: number; amount: number }>
  byStatus: Record<string, { count: number; amount: number }>
}

export default function CommissionReportsPage() {
  const [subscriptionPurchases, setSubscriptionPurchases] = useState<SubscriptionPurchase[]>([])
  const [stats, setStats] = useState<CommissionStats>({
    totalPurchases: 0,
    totalCommissions: 0,
    totalTransactions: 0,
    uniqueUsers: 0,
    byType: {},
    byStatus: {}
  })
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedCommissionType, setSelectedCommissionType] = useState('')
  const [selectedStatus, setSelectedStatus] = useState('')
  const [expandedPurchases, setExpandedPurchases] = useState<Set<string>>(new Set())
  const [currentPage, setCurrentPage] = useState(1)
  const itemsPerPage = 10

  const commissionTypes = [
    'direct_commission',
    'level_commission', 
    'voucher',
    'festival_bonus',
    'saving',
    'gift_center',
    'entertainment',
    'medical',
    'education',
    'credit',
    'zm_bonus',
    'rsm_bonus',
    'present_leader',
    'annual_present_user',
    'annual_present_leader'
  ]

  useEffect(() => {
    fetchCommissionData()
  }, [])

  const fetchCommissionData = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/admin/commission-reports')
      const data = await response.json()
      
      if (data.success) {
        setSubscriptionPurchases(data.data.purchases)
        setStats(data.data.stats)
      } else {
        console.error('Failed to fetch commission data:', data.error)
      }
    } catch (error) {
      console.error('Error fetching commission data:', error)
    } finally {
      setLoading(false)
    }
  }

  const togglePurchaseExpansion = (purchaseId: string) => {
    const newExpanded = new Set(expandedPurchases)
    if (newExpanded.has(purchaseId)) {
      newExpanded.delete(purchaseId)
    } else {
      newExpanded.add(purchaseId)
    }
    setExpandedPurchases(newExpanded)
  }

  const getCommissionTypeColor = (type: string) => {
    const colorMap: Record<string, string> = {
      'direct_commission': 'bg-blue-100 text-blue-800',
      'level_commission': 'bg-green-100 text-green-800',
      'voucher': 'bg-purple-100 text-purple-800',
      'festival_bonus': 'bg-yellow-100 text-yellow-800',
      'saving': 'bg-indigo-100 text-indigo-800',
      'gift_center': 'bg-pink-100 text-pink-800',
      'entertainment': 'bg-red-100 text-red-800',
      'medical': 'bg-teal-100 text-teal-800',
      'education': 'bg-orange-100 text-orange-800',
      'credit': 'bg-gray-100 text-gray-800',
      'zm_bonus': 'bg-amber-100 text-amber-800',
      'rsm_bonus': 'bg-emerald-100 text-emerald-800',
      'present_leader': 'bg-rose-100 text-rose-800',
      'annual_present_user': 'bg-cyan-100 text-cyan-800',
      'annual_present_leader': 'bg-violet-100 text-violet-800'
    }
    return colorMap[type] || 'bg-gray-100 text-gray-800'
  }

  const getStatusColor = (status: string) => {
    const colorMap: Record<string, string> = {
      'pending': 'bg-yellow-100 text-yellow-800',
      'processed': 'bg-green-100 text-green-800',
      'failed': 'bg-red-100 text-red-800',
      'cancelled': 'bg-gray-100 text-gray-800'
    }
    return colorMap[status] || 'bg-gray-100 text-gray-800'
  }

  const formatCurrency = (amount: number) => {
    return `Rs ${amount.toLocaleString()}`
  }

  const filteredPurchases = subscriptionPurchases.filter(purchase => {
    const matchesSearch = !searchTerm || 
      purchase.user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      purchase.user.full_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      purchase.package.name.toLowerCase().includes(searchTerm.toLowerCase())

    const matchesCommissionType = !selectedCommissionType ||
      purchase.commission_transactions.some(t => t.commission_type === selectedCommissionType)

    const matchesStatus = !selectedStatus ||
      purchase.commission_transactions.some(t => t.status === selectedStatus)

    return matchesSearch && matchesCommissionType && matchesStatus
  })

  const paginatedPurchases = filteredPurchases.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  )

  const totalPages = Math.ceil(filteredPurchases.length / itemsPerPage)

  if (loading) {
    return (
      <AdminLayout>
        <div className="flex items-center justify-center h-64">
          <div className="rounded-full h-32 w-32 border-b-2 border-amber-600"></div>
        </div>
      </AdminLayout>
    )
  }

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Commission Reports</h1>
            <p className="text-gray-600 mt-1">
              View commission transactions organized by subscription purchases
            </p>
          </div>
          <div className="flex items-center gap-2">
            <Button variant="outline" onClick={fetchCommissionData} animated={false}>
              <RefreshCw className="w-4 h-4 mr-2" />
              Refresh
            </Button>
            <Button className="bg-amber-600" animated={false}>
              <Download className="w-4 h-4 mr-2" />
              Export
            </Button>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card animated={false} hover={false}>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                  <Package className="h-6 w-6 text-blue-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Total Purchases</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.totalPurchases}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card animated={false} hover={false}>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                  <Receipt className="h-6 w-6 text-green-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Total Transactions</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.totalTransactions}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card animated={false} hover={false}>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="w-12 h-12 bg-amber-100 rounded-lg flex items-center justify-center">
                  <DollarSign className="h-6 w-6 text-amber-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Total Commissions</p>
                  <p className="text-2xl font-bold text-gray-900">{formatCurrency(stats.totalCommissions)}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card animated={false} hover={false}>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                  <Users className="h-6 w-6 text-purple-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Unique Users</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.uniqueUsers}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Filters */}
        <Card animated={false} hover={false}>
          <CardContent className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Search</label>
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    placeholder="Search by user, email, or package..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Commission Type</label>
                <select
                  value={selectedCommissionType}
                  onChange={(e) => setSelectedCommissionType(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500"
                >
                  <option value="">All Types</option>
                  {commissionTypes.map(type => (
                    <option key={type} value={type}>
                      {type.replace('_', ' ').toUpperCase()}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Status</label>
                <select
                  value={selectedStatus}
                  onChange={(e) => setSelectedStatus(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-amber-500"
                >
                  <option value="">All Status</option>
                  <option value="pending">Pending</option>
                  <option value="processed">Processed</option>
                  <option value="failed">Failed</option>
                  <option value="cancelled">Cancelled</option>
                </select>
              </div>

              <div className="flex items-end">
                <Button
                  variant="outline"
                  onClick={() => {
                    setSearchTerm('')
                    setSelectedCommissionType('')
                    setSelectedStatus('')
                  }}
                  className="w-full"
                  animated={false}
                >
                  <Filter className="w-4 h-4 mr-2" />
                  Clear Filters
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Subscription Purchases with Commission Transactions */}
        <div className="space-y-4">
          {filteredPurchases.length === 0 ? (
            <Card animated={false} hover={false}>
              <CardContent className="p-12 text-center">
                <Receipt className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No transactions found</h3>
                <p className="text-gray-500">
                  No commission transactions match your current filters.
                </p>
              </CardContent>
            </Card>
          ) : (
            paginatedPurchases.map((purchase) => (
              <Card key={purchase.id} className="overflow-hidden" animated={false} hover={false}>
                <CardHeader
                  onClick={() => togglePurchaseExpansion(purchase.id)}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                      <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                        <Package className="h-6 w-6 text-blue-600" />
                      </div>
                      <div>
                        <CardTitle className="text-lg">
                          {purchase.package.name} - {formatCurrency(purchase.package.price)}
                        </CardTitle>
                        <div className="flex items-center space-x-4 text-sm text-gray-600 mt-1">
                          <span className="flex items-center">
                            <Users className="h-4 w-4 mr-1" />
                            {purchase.user.full_name || purchase.user.email}
                          </span>
                          <span className="flex items-center">
                            <Calendar className="h-4 w-4 mr-1" />
                            {new Date(purchase.purchased_at).toLocaleDateString()}
                          </span>
                          <Badge variant="outline" className="capitalize">
                            {purchase.user.user_type || 'user'}
                          </Badge>
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center space-x-4">
                      <div className="text-right">
                        <p className="text-sm text-gray-600">Commission Transactions</p>
                        <p className="text-lg font-semibold text-gray-900">
                          {purchase.commission_transactions.length}
                        </p>
                      </div>
                      <div className="text-right">
                        <p className="text-sm text-gray-600">Total Commission</p>
                        <p className="text-lg font-semibold text-green-600">
                          {formatCurrency(
                            purchase.commission_transactions.reduce(
                              (sum, t) => sum + t.commission_amount, 0
                            )
                          )}
                        </p>
                      </div>
                      {expandedPurchases.has(purchase.id) ? (
                        <ChevronUp className="h-5 w-5 text-gray-400" />
                      ) : (
                        <ChevronDown className="h-5 w-5 text-gray-400" />
                      )}
                    </div>
                  </div>
                </CardHeader>

                {expandedPurchases.has(purchase.id) && (
                  <CardContent className="pt-0">
                    <div className="border-t pt-6">
                      <h4 className="text-lg font-semibold text-gray-900 mb-4">
                        Commission Distribution Details
                      </h4>

                      {/* Commission Categories */}
                      <div className="space-y-6">
                        {Object.entries(
                          purchase.commission_transactions.reduce((acc, transaction) => {
                            const category = transaction.commission_type
                            if (!acc[category]) {
                              acc[category] = []
                            }
                            acc[category].push(transaction)
                            return acc
                          }, {} as Record<string, CommissionTransaction[]>)
                        ).map(([category, transactions]) => (
                          <div key={category} className="bg-gray-50 rounded-lg p-4">
                            <div className="flex items-center justify-between mb-4">
                              <div className="flex items-center space-x-3">
                                <Badge className={getCommissionTypeColor(category)}>
                                  {category.replace('_', ' ').toUpperCase()}
                                </Badge>
                                <span className="text-sm text-gray-600">
                                  {transactions.length} transaction{transactions.length > 1 ? 's' : ''}
                                </span>
                              </div>
                              <div className="text-right">
                                <p className="text-sm text-gray-600">Category Total</p>
                                <p className="text-lg font-semibold text-gray-900">
                                  {formatCurrency(
                                    transactions.reduce((sum, t) => sum + t.commission_amount, 0)
                                  )}
                                </p>
                              </div>
                            </div>

                            <div className="space-y-3">
                              {transactions.map((transaction) => (
                                <div
                                  key={transaction.id}
                                  className="bg-white rounded-lg p-4 border border-gray-200"
                                >
                                  <div className="grid grid-cols-1 md:grid-cols-6 gap-4">
                                    <div>
                                      <p className="text-xs text-gray-500 uppercase tracking-wide">Beneficiary</p>
                                      <p className="text-sm font-medium text-gray-900">
                                        {transaction.beneficiary.full_name || transaction.beneficiary.email}
                                      </p>
                                      <p className="text-xs text-gray-500">
                                        {transaction.beneficiary.email}
                                      </p>
                                    </div>

                                    <div>
                                      <p className="text-xs text-gray-500 uppercase tracking-wide">Level</p>
                                      <p className="text-sm font-medium text-gray-900">
                                        Level {transaction.commission_level}
                                      </p>
                                    </div>

                                    <div>
                                      <p className="text-xs text-gray-500 uppercase tracking-wide">Rate</p>
                                      <p className="text-sm font-medium text-gray-900">
                                        {(transaction.commission_rate * 100).toFixed(2)}%
                                      </p>
                                    </div>

                                    <div>
                                      <p className="text-xs text-gray-500 uppercase tracking-wide">Amount</p>
                                      <p className="text-sm font-semibold text-green-600">
                                        {formatCurrency(transaction.commission_amount)}
                                      </p>
                                    </div>

                                    <div>
                                      <p className="text-xs text-gray-500 uppercase tracking-wide">Status</p>
                                      <Badge className={getStatusColor(transaction.status)}>
                                        {transaction.status}
                                      </Badge>
                                    </div>

                                    <div>
                                      <p className="text-xs text-gray-500 uppercase tracking-wide">Date</p>
                                      <p className="text-sm text-gray-900">
                                        {new Date(transaction.created_at).toLocaleDateString()}
                                      </p>
                                      {transaction.processed_at && (
                                        <p className="text-xs text-gray-500">
                                          Processed: {new Date(transaction.processed_at).toLocaleDateString()}
                                        </p>
                                      )}
                                    </div>
                                  </div>
                                </div>
                              ))}
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  </CardContent>
                )}
              </Card>
            ))
          )}
        </div>

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="flex items-center justify-between">
            <div className="text-sm text-gray-700">
              Showing {((currentPage - 1) * itemsPerPage) + 1} to {Math.min(currentPage * itemsPerPage, filteredPurchases.length)} of {filteredPurchases.length} results
            </div>
            <div className="flex space-x-2">
              <Button
                variant="outline"
                onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                disabled={currentPage === 1}
                animated={false}
              >
                Previous
              </Button>
              <Button
                variant="outline"
                onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                disabled={currentPage === totalPages}
                animated={false}
              >
                Next
              </Button>
            </div>
          </div>
        )}
      </div>
    </AdminLayout>
  )
}
