import { NextRequest, NextResponse } from 'next/server'
import { supabaseAdmin, TABLES } from '@/lib/supabase'
import { User } from '@/types'

/**
 * Get eligible users for RSM upgrade under a specific ZM
 * This API route handles the server-side logic that requires admin privileges
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const zonalManagerId = searchParams.get('zmId')

    if (!zonalManagerId) {
      return NextResponse.json(
        { success: false, error: 'Zonal Manager ID is required' },
        { status: 400 }
      )
    }

    if (!supabaseAdmin) {
      console.error('Supabase admin client not available')
      return NextResponse.json(
        { success: false, error: 'Admin client not configured' },
        { status: 500 }
      )
    }

    // Get the ZM's user_id
    const { data: zmData, error: zmError } = await supabaseAdmin
      .from(TABLES.ZONAL_MANAGERS)
      .select('user_id')
      .eq('id', zonalManagerId)
      .single()

    if (zmError) {
      console.error('ZM data query error:', zmError)
      return NextResponse.json(
        { success: false, error: `Failed to get ZM data: ${zmError.message}` },
        { status: 400 }
      )
    }

    if (!zmData || !zmData.user_id) {
      return NextResponse.json(
        { success: false, error: 'Zonal Manager not found or invalid' },
        { status: 404 }
      )
    }

    // Get all users in the ZM's network using referral_hierarchy
    const { data: networkUsers, error: networkError } = await supabaseAdmin
      .from(TABLES.REFERRAL_HIERARCHY)
      .select(`
        user_id,
        level_difference,
        user:user_id!inner(
          id,
          email,
          full_name,
          user_type,
          referred_by_id
        )
      `)
      .eq('ancestor_id', zmData.user_id)
      .order('level_difference', { ascending: true })

    if (networkError) {
      console.error('Network users query error:', networkError)
      return NextResponse.json(
        { success: false, error: `Failed to get network users: ${networkError.message}` },
        { status: 500 }
      )
    }

    // Validate and filter network users
    const validNetworkUsers = (networkUsers || []).filter(nu => 
      nu && nu.user && nu.user.id && typeof nu.user.id === 'string'
    )

    if (validNetworkUsers.length === 0) {
      return NextResponse.json({
        success: true,
        data: []
      })
    }

    // Get direct referrals of ZM
    const { data: directReferrals, error: directError } = await supabaseAdmin
      .from(TABLES.USERS)
      .select('id')
      .eq('referred_by_id', zmData.user_id)

    if (directError) {
      console.error('Direct referrals query error:', directError)
      return NextResponse.json(
        { success: false, error: `Failed to get direct referrals: ${directError.message}` },
        { status: 500 }
      )
    }

    const directReferralIds = (directReferrals || []).map(user => user?.id).filter(Boolean)

    // Get existing RSMs under this ZM
    const { data: existingRSMs, error: rsmError } = await supabaseAdmin
      .from(TABLES.REGIONAL_SALES_MANAGERS)
      .select(`
        user_id,
        user:user_id(referred_by_id)
      `)
      .eq('zonal_manager_id', zonalManagerId)
      .eq('is_active', true)

    if (rsmError) {
      console.error('Error getting existing RSMs:', rsmError)
    }

    // Find which direct lines already have RSMs
    const directLinesWithRSM = new Set<string>()
    if (existingRSMs && Array.isArray(existingRSMs)) {
      for (const rsm of existingRSMs) {
        if (rsm && rsm.user_id) {
          const rsmDirectLine = await findDirectLineForUser(rsm.user_id, zmData.user_id, directReferralIds)
          if (rsmDirectLine) {
            directLinesWithRSM.add(rsmDirectLine)
          }
        }
      }
    }

    // Filter eligible users
    const eligibleUsers: User[] = []
    const existingRSMUserIds = (existingRSMs || []).map(rsm => rsm?.user_id).filter(Boolean)

    for (const networkUser of validNetworkUsers) {
      try {
        const user = networkUser.user

        // Skip if already RSM or ZM - only regular users can be upgraded
        if (existingRSMUserIds.includes(user.id) || user.user_type !== 'user') {
          continue
        }

        // Find which direct line this user belongs to
        const userDirectLine = await findDirectLineForUser(user.id, zmData.user_id, directReferralIds)

        // Only include if the direct line doesn't already have an RSM
        if (userDirectLine && !directLinesWithRSM.has(userDirectLine)) {
          const eligibleUser: User = {
            ...user,
            full_name: user.full_name || 'N/A',
            email: user.email || 'N/A'
          }
          eligibleUsers.push(eligibleUser)
        }
      } catch (error) {
        console.error('Error processing network user:', networkUser, error)
        continue
      }
    }

    return NextResponse.json({
      success: true,
      data: eligibleUsers
    })

  } catch (error) {
    console.error('Error in eligible users API:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Internal server error' 
      },
      { status: 500 }
    )
  }
}

/**
 * Helper function to find which direct line a user belongs to under a ZM
 */
async function findDirectLineForUser(userId: string, zmUserId: string, directReferralIds: string[]): Promise<string | null> {
  try {
    if (!userId || !zmUserId || !Array.isArray(directReferralIds) || !supabaseAdmin) {
      return null
    }

    const { data: userData, error } = await supabaseAdmin
      .from(TABLES.USERS)
      .select('referral_path, referred_by_id')
      .eq('id', userId)
      .single()

    if (error || !userData) {
      return null
    }

    // If user is a direct referral of ZM
    if (userData.referred_by_id === zmUserId) {
      return userId
    }

    // Parse referral path to find the direct line
    if (userData.referral_path && typeof userData.referral_path === 'string') {
      try {
        const pathIds = userData.referral_path.split(',').filter(Boolean)
        const zmIndex = pathIds.indexOf(zmUserId)

        if (zmIndex !== -1 && zmIndex < pathIds.length - 1) {
          const directLineId = pathIds[zmIndex + 1]
          if (directReferralIds.includes(directLineId)) {
            return directLineId
          }
        }
      } catch (pathError) {
        console.warn('Error parsing referral path:', userData.referral_path, pathError)
      }
    }

    // Fallback: trace upwards through referral hierarchy
    let currentUserId = userId
    let currentReferredBy = userData.referred_by_id
    let iterations = 0
    const maxIterations = 20

    while (currentReferredBy && currentReferredBy !== zmUserId && iterations < maxIterations) {
      iterations++
      
      try {
        const { data: parentData, error: parentError } = await supabaseAdmin
          .from(TABLES.USERS)
          .select('referred_by_id')
          .eq('id', currentReferredBy)
          .single()

        if (parentError || !parentData) {
          break
        }

        currentUserId = currentReferredBy
        currentReferredBy = parentData.referred_by_id
      } catch (parentError) {
        break
      }
    }

    // If we reached the ZM, the current user is the direct line
    if (currentReferredBy === zmUserId && directReferralIds.includes(currentUserId)) {
      return currentUserId
    }

    return null
  } catch (error) {
    console.error('Error finding direct line for user:', { userId, zmUserId, error })
    return null
  }
}
