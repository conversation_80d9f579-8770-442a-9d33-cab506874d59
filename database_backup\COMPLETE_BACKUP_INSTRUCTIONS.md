# Complete Supabase Backup Instructions

## What We Have Backed Up

### ✅ Successfully Backed Up:
1. **All Table Data** (53 tables, 1,201+ records)
   - Users, ads, categories, shops, products, orders
   - Wallet transactions, commission data
   - KYC submissions, referral hierarchy
   - Admin settings and configurations

2. **Storage Buckets** (2 buckets)
   - ad-images: 11 files (public bucket)
   - kyc-documents: 3 files (private bucket)

3. **Migration Files** (56 migration files)
   - Complete database evolution history
   - All schema changes preserved

### ⚠️ Partially Backed Up (Manual Steps Required):
1. **RLS Policies** - Need manual extraction
2. **Database Functions** - Need manual extraction  
3. **Triggers** - Need manual extraction
4. **Views** - Need manual extraction
5. **Indexes** - Need manual extraction
6. **Constraints** - Need manual extraction

## Complete Backup Process

### Step 1: Use Supabase CLI for Schema
```bash
# Update Supabase CLI first
npm install -g @supabase/cli@latest

# Create complete schema backup
supabase db dump --schema-only --file schema_complete.sql

# Create complete data backup  
supabase db dump --data-only --file data_complete.sql

# Create complete backup (schema + data)
supabase db dump --file complete_database.sql
```

### Step 2: Manual RLS Policies Extraction
1. Go to Supabase Dashboard → Authentication → Policies
2. Copy each policy definition
3. Save to `rls_policies.sql`

### Step 3: Manual Functions Extraction
1. Go to Supabase Dashboard → SQL Editor
2. Run: `SELECT * FROM pg_proc WHERE pronamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'public');`
3. Extract function definitions
4. Save to `functions.sql`

### Step 4: Storage Bucket Policies
1. Go to Supabase Dashboard → Storage
2. Copy bucket policies for each bucket
3. Save to `storage_policies.sql`

### Step 5: Edge Functions (if any)
```bash
# List edge functions
supabase functions list

# Download each function
supabase functions download <function-name>
```

## Restore Process

### 1. Create New Supabase Project
```bash
supabase projects create "okdoi-restore"
supabase link --project-ref <new-project-ref>
```

### 2. Restore Schema
```bash
supabase db reset
psql -h <host> -U postgres -d postgres -f schema_complete.sql
```

### 3. Restore Data
```bash
# Use our JSON restore script
node restore_backup.js complete_backup_2025-09-08T19-32-54-897Z.json

# Or use SQL dump
psql -h <host> -U postgres -d postgres -f data_complete.sql
```

### 4. Recreate Storage Buckets
- Manually create buckets in dashboard
- Upload files from backup
- Apply storage policies

### 5. Apply RLS Policies
```bash
psql -h <host> -U postgres -d postgres -f rls_policies.sql
```

### 6. Restore Functions and Triggers
```bash
psql -h <host> -U postgres -d postgres -f functions.sql
```

## Files in This Backup

### Data Backups:
- `complete_backup_2025-09-08T19-32-54-897Z.json` - **MAIN DATA BACKUP**
- `complete_supabase_backup_2025-09-08T19-33-13-857Z.json` - **COMPLETE BACKUP**

### Schema Backups:
- `migrations_backup/` - All 56 migration files
- `database_schema_backup_*.json` - Schema metadata

### Storage:
- Storage bucket information included in complete backup
- File lists and metadata preserved

### Tools:
- `restore_backup.js` - Data restoration script
- `verify_backup.js` - Backup verification script

## Security Notes

🔒 **IMPORTANT**: 
- Backup files contain sensitive user data
- Store in encrypted, secure locations
- Limit access to authorized personnel only
- Consider encrypting backup files

## Testing Restore

Always test restore process on a development environment first:
1. Create test Supabase project
2. Restore schema and data
3. Verify all functionality works
4. Test user authentication and permissions
5. Verify storage bucket access

## Support

For issues with backup/restore:
1. Check Supabase CLI version (`supabase --version`)
2. Verify database connection
3. Check file permissions
4. Review error logs carefully

---
Generated: 2025-09-08T19:34:58.184Z
Project: vnmydqbwjjufnxngpnqo (OKDOI Marketplace)
