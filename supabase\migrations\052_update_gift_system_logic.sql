-- Update Gift System Logic
-- Based on Commission_Distribution_Explained.md requirements
-- Date: 2025-01-09

-- =====================================================
-- 1. UPDATE GIFT SYSTEM COMMISSION DISTRIBUTION
-- =====================================================

-- Create function to handle gift system commissions
CREATE OR REPLACE FUNCTION distribute_gift_system_commissions(
    purchaser_id UUID,
    package_id UUID,
    package_amount DECIMAL(12,2)
) RETURNS VOID AS $$
DECLARE
    commission_record RECORD;
    beneficiary_record RECORD;
    commission_amount DECIMAL(12,2);
    transaction_id_val VARCHAR(50);
    beneficiary_wallet_id UUID;
    wallet_transaction_id UUID;
    direct_referrer_id UUID;
BEGIN
    -- Generate unique transaction ID
    transaction_id_val := 'GIFT-' || TO_CHAR(NOW(), 'YYYYMMDD') || '-' || UPPER(SUBSTRING(MD5(RANDOM()::TEXT) FROM 1 FOR 8));

    -- Get commission structure for this package value
    FOR commission_record IN
        SELECT * FROM commission_structure
        WHERE package_value <= package_amount
        AND is_active = true
        ORDER BY package_value DESC
        LIMIT 1
    LOOP
        -- =====================================================
        -- PRESENT USER & ANNUAL PRESENT USER (Direct Sales Only, Level 10 limit)
        -- =====================================================
        
        -- Get direct referrer (level 1)
        SELECT u.id INTO direct_referrer_id
        FROM users u
        JOIN referral_hierarchy rh ON rh.ancestor_id = u.id
        WHERE rh.user_id = purchaser_id AND rh.level_difference = 1
        LIMIT 1;
        
        IF direct_referrer_id IS NOT NULL THEN
            -- Get the direct referrer details
            SELECT * INTO beneficiary_record FROM users WHERE id = direct_referrer_id;
            
            -- Check if beneficiary is within level 10 limit and is a regular user
            IF beneficiary_record.user_type = 'user' AND beneficiary_record.referral_level <= 10 THEN
                -- Present User Commission (from direct sales only)
                IF commission_record.present_user_rate > 0 THEN
                    commission_amount := package_amount * commission_record.present_user_rate;
                    
                    -- Get beneficiary's wallet
                    SELECT id INTO beneficiary_wallet_id 
                    FROM user_wallets 
                    WHERE user_id = beneficiary_record.id;
                    
                    IF beneficiary_wallet_id IS NOT NULL THEN
                        -- Create wallet transaction
                        INSERT INTO wallet_transactions (
                            wallet_id, transaction_type, amount, description, 
                            reference_id, reference_type, status, metadata
                        ) VALUES (
                            beneficiary_wallet_id, 'commission', commission_amount,
                            'Present User Commission (Direct Sales)',
                            package_id, 'subscription', 'completed',
                            '{"commission_type": "present_user", "source": "direct_sales_only"}'
                        ) RETURNING id INTO wallet_transaction_id;
                        
                        -- Update wallet balance
                        UPDATE user_wallets 
                        SET balance = balance + commission_amount,
                            updated_at = NOW()
                        WHERE id = beneficiary_wallet_id;
                        
                        -- Create commission transaction record
                        INSERT INTO commission_transactions (
                            user_id, beneficiary_id, subscription_purchase_id, commission_type,
                            commission_level, commission_rate, commission_amount, status, 
                            wallet_transaction_id, transaction_id, created_at, metadata
                        ) VALUES (
                            purchaser_id, beneficiary_record.id, package_id, 'present_user',
                            1, commission_record.present_user_rate, commission_amount, 
                            'processed', wallet_transaction_id, transaction_id_val || '-PRESENT-USER', NOW(),
                            '{"source": "direct_sales_only", "beneficiary_type": "' || beneficiary_record.user_type || '"}'
                        );
                        
                        -- Update beneficiary's total commission earned
                        UPDATE users 
                        SET total_commission_earned = COALESCE(total_commission_earned, 0) + commission_amount,
                            updated_at = NOW()
                        WHERE id = beneficiary_record.id;
                    END IF;
                END IF;
                
                -- Annual Present User Commission (from direct sales only)
                IF commission_record.annual_present_user_rate > 0 THEN
                    commission_amount := package_amount * commission_record.annual_present_user_rate;
                    
                    -- Get beneficiary's wallet
                    SELECT id INTO beneficiary_wallet_id 
                    FROM user_wallets 
                    WHERE user_id = beneficiary_record.id;
                    
                    IF beneficiary_wallet_id IS NOT NULL THEN
                        -- Create wallet transaction
                        INSERT INTO wallet_transactions (
                            wallet_id, transaction_type, amount, description, 
                            reference_id, reference_type, status, metadata
                        ) VALUES (
                            beneficiary_wallet_id, 'commission', commission_amount,
                            'Annual Present User Commission (Direct Sales)',
                            package_id, 'subscription', 'completed',
                            '{"commission_type": "annual_present_user", "source": "direct_sales_only"}'
                        ) RETURNING id INTO wallet_transaction_id;
                        
                        -- Update wallet balance
                        UPDATE user_wallets 
                        SET balance = balance + commission_amount,
                            updated_at = NOW()
                        WHERE id = beneficiary_wallet_id;
                        
                        -- Create commission transaction record
                        INSERT INTO commission_transactions (
                            user_id, beneficiary_id, subscription_purchase_id, commission_type,
                            commission_level, commission_rate, commission_amount, status, 
                            wallet_transaction_id, transaction_id, created_at, metadata
                        ) VALUES (
                            purchaser_id, beneficiary_record.id, package_id, 'annual_present_user',
                            1, commission_record.annual_present_user_rate, commission_amount, 
                            'processed', wallet_transaction_id, transaction_id_val || '-ANNUAL-PRESENT-USER', NOW(),
                            '{"source": "direct_sales_only", "beneficiary_type": "' || beneficiary_record.user_type || '"}'
                        );
                        
                        -- Update beneficiary's total commission earned
                        UPDATE users 
                        SET total_commission_earned = COALESCE(total_commission_earned, 0) + commission_amount,
                            updated_at = NOW()
                        WHERE id = beneficiary_record.id;
                    END IF;
                END IF;
            END IF;
        END IF;
        
        -- =====================================================
        -- PRESENT LEADER & ANNUAL PRESENT LEADER (Entire Network for RSM/ZM)
        -- =====================================================
        
        -- Loop through all ancestors to find RSM and ZM leaders
        FOR beneficiary_record IN
            SELECT u.*, rh.level_difference
            FROM users u
            JOIN referral_hierarchy rh ON rh.ancestor_id = u.id
            WHERE rh.user_id = purchaser_id 
            AND u.user_type IN ('rsm', 'zonal_manager')
            ORDER BY rh.level_difference ASC
        LOOP
            -- Present Leader Commission (from entire network)
            IF commission_record.present_leader_rate > 0 THEN
                commission_amount := package_amount * commission_record.present_leader_rate;
                
                -- Get beneficiary's wallet
                SELECT id INTO beneficiary_wallet_id 
                FROM user_wallets 
                WHERE user_id = beneficiary_record.id;
                
                IF beneficiary_wallet_id IS NOT NULL THEN
                    -- Create wallet transaction
                    INSERT INTO wallet_transactions (
                        wallet_id, transaction_type, amount, description, 
                        reference_id, reference_type, status, metadata
                    ) VALUES (
                        beneficiary_wallet_id, 'commission', commission_amount,
                        'Present Leader Commission (Network Sales)',
                        package_id, 'subscription', 'completed',
                        '{"commission_type": "present_leader", "source": "entire_network"}'
                    ) RETURNING id INTO wallet_transaction_id;
                    
                    -- Update wallet balance
                    UPDATE user_wallets 
                    SET balance = balance + commission_amount,
                        updated_at = NOW()
                    WHERE id = beneficiary_wallet_id;
                    
                    -- Create commission transaction record
                    INSERT INTO commission_transactions (
                        user_id, beneficiary_id, subscription_purchase_id, commission_type,
                        commission_level, commission_rate, commission_amount, status, 
                        wallet_transaction_id, transaction_id, created_at, metadata
                    ) VALUES (
                        purchaser_id, beneficiary_record.id, package_id, 'present_leader',
                        beneficiary_record.level_difference, commission_record.present_leader_rate, commission_amount, 
                        'processed', wallet_transaction_id, transaction_id_val || '-PRESENT-LEADER-' || beneficiary_record.level_difference, NOW(),
                        '{"source": "entire_network", "beneficiary_type": "' || beneficiary_record.user_type || '"}'
                    );
                    
                    -- Update beneficiary's total commission earned
                    UPDATE users 
                    SET total_commission_earned = COALESCE(total_commission_earned, 0) + commission_amount,
                        updated_at = NOW()
                    WHERE id = beneficiary_record.id;
                END IF;
            END IF;
            
            -- Annual Present Leader Commission (from entire network)
            IF commission_record.annual_present_leader_rate > 0 THEN
                commission_amount := package_amount * commission_record.annual_present_leader_rate;
                
                -- Get beneficiary's wallet
                SELECT id INTO beneficiary_wallet_id 
                FROM user_wallets 
                WHERE user_id = beneficiary_record.id;
                
                IF beneficiary_wallet_id IS NOT NULL THEN
                    -- Create wallet transaction
                    INSERT INTO wallet_transactions (
                        wallet_id, transaction_type, amount, description, 
                        reference_id, reference_type, status, metadata
                    ) VALUES (
                        beneficiary_wallet_id, 'commission', commission_amount,
                        'Annual Present Leader Commission (Network Sales)',
                        package_id, 'subscription', 'completed',
                        '{"commission_type": "annual_present_leader", "source": "entire_network"}'
                    ) RETURNING id INTO wallet_transaction_id;
                    
                    -- Update wallet balance
                    UPDATE user_wallets 
                    SET balance = balance + commission_amount,
                        updated_at = NOW()
                    WHERE id = beneficiary_wallet_id;
                    
                    -- Create commission transaction record
                    INSERT INTO commission_transactions (
                        user_id, beneficiary_id, subscription_purchase_id, commission_type,
                        commission_level, commission_rate, commission_amount, status, 
                        wallet_transaction_id, transaction_id, created_at, metadata
                    ) VALUES (
                        purchaser_id, beneficiary_record.id, package_id, 'annual_present_leader',
                        beneficiary_record.level_difference, commission_record.annual_present_leader_rate, commission_amount, 
                        'processed', wallet_transaction_id, transaction_id_val || '-ANNUAL-PRESENT-LEADER-' || beneficiary_record.level_difference, NOW(),
                        '{"source": "entire_network", "beneficiary_type": "' || beneficiary_record.user_type || '"}'
                    );
                    
                    -- Update beneficiary's total commission earned
                    UPDATE users 
                    SET total_commission_earned = COALESCE(total_commission_earned, 0) + commission_amount,
                        updated_at = NOW()
                    WHERE id = beneficiary_record.id;
                END IF;
            END IF;
        END LOOP;
    END LOOP;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- 2. INTEGRATE GIFT SYSTEM WITH MAIN COMMISSION DISTRIBUTION
-- =====================================================

-- Update the main commission distribution function to include gift system
CREATE OR REPLACE FUNCTION calculate_commission_distribution_with_gifts(
    purchaser_id UUID,
    package_id UUID,
    package_amount DECIMAL(12,2)
) RETURNS VOID AS $$
BEGIN
    -- First, run the main commission distribution
    PERFORM calculate_commission_distribution_new(purchaser_id, package_id, package_amount);
    
    -- Then, run the gift system commission distribution
    PERFORM distribute_gift_system_commissions(purchaser_id, package_id, package_amount);
END;
$$ LANGUAGE plpgsql;

-- Replace the main function to include gift system
DROP FUNCTION IF EXISTS calculate_commission_distribution(UUID, UUID, DECIMAL);

CREATE OR REPLACE FUNCTION calculate_commission_distribution(
    purchaser_id UUID,
    package_id UUID,
    package_amount DECIMAL(12,2)
) RETURNS VOID AS $$
BEGIN
    -- Call the comprehensive implementation with gift system
    PERFORM calculate_commission_distribution_with_gifts(purchaser_id, package_id, package_amount);
END;
$$ LANGUAGE plpgsql;

-- Add comments
COMMENT ON FUNCTION distribute_gift_system_commissions(UUID, UUID, DECIMAL) IS 
'Distributes gift system commissions according to new requirements:
- Present User & Annual Present User: Direct sales only, Level 10 limit
- Present Leader & Annual Present Leader: Entire network for RSM/ZM';

COMMENT ON FUNCTION calculate_commission_distribution_with_gifts(UUID, UUID, DECIMAL) IS 
'Complete commission distribution including main commissions and gift system commissions';
