"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/subscription/purchase/route";
exports.ids = ["app/api/subscription/purchase/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

module.exports = require("https");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("stream");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fsubscription%2Fpurchase%2Froute&page=%2Fapi%2Fsubscription%2Fpurchase%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsubscription%2Fpurchase%2Froute.ts&appDir=C%3A%5CUsers%5Cdream%5CDesktop%5Cokdoi%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cdream%5CDesktop%5Cokdoi&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fsubscription%2Fpurchase%2Froute&page=%2Fapi%2Fsubscription%2Fpurchase%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsubscription%2Fpurchase%2Froute.ts&appDir=C%3A%5CUsers%5Cdream%5CDesktop%5Cokdoi%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cdream%5CDesktop%5Cokdoi&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_dream_Desktop_okdoi_src_app_api_subscription_purchase_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/subscription/purchase/route.ts */ \"(rsc)/./src/app/api/subscription/purchase/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/subscription/purchase/route\",\n        pathname: \"/api/subscription/purchase\",\n        filename: \"route\",\n        bundlePath: \"app/api/subscription/purchase/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\api\\\\subscription\\\\purchase\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_dream_Desktop_okdoi_src_app_api_subscription_purchase_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/subscription/purchase/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fsubscription%2Fpurchase%2Froute&page=%2Fapi%2Fsubscription%2Fpurchase%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsubscription%2Fpurchase%2Froute.ts&appDir=C%3A%5CUsers%5Cdream%5CDesktop%5Cokdoi%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cdream%5CDesktop%5Cokdoi&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/subscription/purchase/route.ts":
/*!****************************************************!*\
  !*** ./src/app/api/subscription/purchase/route.ts ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/supabase */ \"(rsc)/./src/lib/supabase.ts\");\n/* harmony import */ var _lib_services_commissionSystem__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/services/commissionSystem */ \"(rsc)/./src/lib/services/commissionSystem.ts\");\n/* harmony import */ var _lib_services_presentAllocationService__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/services/presentAllocationService */ \"(rsc)/./src/lib/services/presentAllocationService.ts\");\n\n\n\n\nasync function POST(request) {\n    try {\n        // Get the authorization header\n        const authHeader = request.headers.get(\"authorization\");\n        if (!authHeader) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Authorization header required\"\n            }, {\n                status: 401\n            });\n        }\n        // Verify the user token\n        const token = authHeader.replace(\"Bearer \", \"\");\n        const { data: { user }, error: authError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.auth.getUser(token);\n        if (authError || !user) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Invalid token\"\n            }, {\n                status: 401\n            });\n        }\n        // Get request body\n        const body = await request.json();\n        const { packageId, walletId } = body;\n        if (!packageId || !walletId) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Package ID and Wallet ID are required\"\n            }, {\n                status: 400\n            });\n        }\n        console.log(`Processing subscription purchase for user ${user.id}, package ${packageId}`);\n        // Get package details using admin client\n        const { data: package_data, error: packageError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabaseAdmin.from(\"subscription_packages\").select(\"*\").eq(\"id\", packageId).eq(\"is_active\", true).single();\n        if (packageError || !package_data) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Subscription package not found or inactive\"\n            }, {\n                status: 404\n            });\n        }\n        // Check wallet balance using admin client\n        const { data: wallet, error: walletError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabaseAdmin.from(\"user_wallets\").select(\"balance\").eq(\"id\", walletId).eq(\"user_id\", user.id).single();\n        if (walletError || !wallet) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Wallet not found\"\n            }, {\n                status: 404\n            });\n        }\n        if (wallet.balance < package_data.price) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Insufficient wallet balance\"\n            }, {\n                status: 400\n            });\n        }\n        // Calculate expiration date\n        const expiresAt = new Date();\n        expiresAt.setDate(expiresAt.getDate() + package_data.duration_days);\n        // Create subscription using admin client\n        const { data: subscription, error: subscriptionError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabaseAdmin.from(\"user_subscriptions\").insert({\n            user_id: user.id,\n            package_id: packageId,\n            expires_at: expiresAt.toISOString()\n        }).select(`\n        *,\n        package:subscription_packages(*)\n      `).single();\n        if (subscriptionError) {\n            console.error(\"Failed to create subscription:\", subscriptionError);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: `Failed to create subscription: ${subscriptionError.message}`\n            }, {\n                status: 500\n            });\n        }\n        // Deduct from wallet using admin client\n        const { error: walletUpdateError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabaseAdmin.rpc(\"update_wallet_balance\", {\n            p_wallet_id: walletId,\n            p_amount: package_data.price,\n            p_transaction_type: \"purchase\",\n            p_description: `Subscription purchase: ${package_data.name}`,\n            p_reference_id: subscription.id,\n            p_reference_type: \"subscription\",\n            p_metadata: {\n                package_id: packageId,\n                package_name: package_data.name,\n                package_price: package_data.price,\n                duration_days: package_data.duration_days\n            }\n        });\n        if (walletUpdateError) {\n            console.error(\"Failed to process payment:\", walletUpdateError);\n            // Rollback subscription creation\n            await _lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabaseAdmin.from(\"user_subscriptions\").delete().eq(\"id\", subscription.id);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: `Failed to process payment: ${walletUpdateError.message}`\n            }, {\n                status: 500\n            });\n        }\n        console.log(`Subscription ${subscription.id} created successfully, processing commissions...`);\n        // Process commission distribution (server-side with admin client)\n        try {\n            const commissionResult = await _lib_services_commissionSystem__WEBPACK_IMPORTED_MODULE_2__.CommissionSystemService.distributeCommissions(user.id, subscription.id, package_data.price);\n            console.log(`Commission distribution completed for subscription ${subscription.id}:`, commissionResult);\n        } catch (commissionError) {\n            console.error(\"Failed to distribute commissions for subscription:\", commissionError);\n        // Don't fail the subscription purchase, but log the error\n        // Commissions can be processed later if needed\n        }\n        // Process present allocations (server-side with admin client)\n        try {\n            await _lib_services_presentAllocationService__WEBPACK_IMPORTED_MODULE_3__.PresentAllocationService.allocatePresentsFromPurchase(user.id, package_data.price, subscription.id, `SUB_${subscription.id}`);\n            console.log(`Present allocation completed for subscription ${subscription.id}`);\n        } catch (presentError) {\n            console.error(\"Present allocation failed:\", presentError);\n        // Don't fail the subscription purchase if present allocation fails\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            message: \"Subscription purchased successfully\",\n            data: subscription\n        });\n    } catch (error) {\n        console.error(\"Error in subscription purchase API:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: error instanceof Error ? error.message : \"Internal server error\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/subscription/purchase/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/services/commissionSystem.ts":
/*!**********************************************!*\
  !*** ./src/lib/services/commissionSystem.ts ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CommissionSystemService: () => (/* binding */ CommissionSystemService)\n/* harmony export */ });\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/supabase */ \"(rsc)/./src/lib/supabase.ts\");\n\n/**\n * CommissionSystemService - Handles commission calculations and distributions\n */ class CommissionSystemService {\n    /**\n   * Get commission structure for a package value\n   */ static async getCommissionStructure(packageValue) {\n        try {\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.COMMISSION_STRUCTURE).select(\"*\").lte(\"package_value\", packageValue).eq(\"is_active\", true).order(\"package_value\", {\n                ascending: false\n            });\n            if (error) {\n                throw new Error(`Failed to get commission structure: ${error.message}`);\n            }\n            return data || [];\n        } catch (error) {\n            console.error(\"Error getting commission structure:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Get all commission structures (for admin)\n   */ static async getAllCommissionStructures() {\n        try {\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.COMMISSION_STRUCTURE).select(\"*\").order(\"package_value\", {\n                ascending: true\n            });\n            if (error) {\n                throw new Error(`Failed to get all commission structures: ${error.message}`);\n            }\n            return data || [];\n        } catch (error) {\n            console.error(\"Error getting all commission structures:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Create new commission structure\n   */ static async createCommissionStructure(structure) {\n        try {\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.COMMISSION_STRUCTURE).insert(structure).select().single();\n            if (error) {\n                throw new Error(`Failed to create commission structure: ${error.message}`);\n            }\n            return data;\n        } catch (error) {\n            console.error(\"Error creating commission structure:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Calculate and distribute commissions for a subscription purchase\n   * NEW: Handles leftover commissions by sending them to company wallet\n   */ static async distributeCommissions(purchaserId, subscriptionId, packageAmount) {\n        try {\n            if (!_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin) {\n                throw new Error(\"Admin client not available\");\n            }\n            console.log(`Starting commission distribution for subscription ${subscriptionId}, package amount: Rs ${packageAmount}`);\n            // Get commission distributions using the new absolute function\n            const { data: distributions, error: distributionError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.rpc(\"calculate_commission_distribution_absolute\", {\n                p_purchaser_id: purchaserId,\n                p_subscription_id: subscriptionId,\n                p_package_price: packageAmount\n            });\n            if (distributionError) {\n                throw new Error(`Failed to calculate commission distributions: ${distributionError.message}`);\n            }\n            if (!distributions || distributions.length === 0) {\n                console.warn(\"No commission distributions calculated\");\n                return {\n                    totalDistributed: 0,\n                    transactionsCreated: 0,\n                    unallocatedAmount: 0,\n                    distributionDetails: []\n                };\n            }\n            let totalDistributed = 0;\n            let distributionCount = 0;\n            let companyLeftoverAmount = 0;\n            // Process each commission distribution\n            for (const distribution of distributions){\n                // Handle company leftover allocation separately\n                if (distribution.beneficiary_id === null && distribution.commission_type === \"company_leftover_allocation\") {\n                    // ✅ Use new function to properly update company wallet balance\n                    const { error: companyWalletError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.rpc(\"update_company_wallet_balance\", {\n                        p_amount: distribution.commission_amount,\n                        p_transaction_type: \"company_profit\",\n                        p_description: `Leftover commission allocation from network distribution (${distribution.metadata?.missing_positions || 0} missing positions)`,\n                        p_subscription_id: subscriptionId,\n                        p_metadata: {\n                            ...distribution.metadata,\n                            subscription_id: subscriptionId,\n                            package_price: packageAmount\n                        }\n                    });\n                    if (companyWalletError) {\n                        console.error(\"Failed to credit company wallet with leftover:\", companyWalletError);\n                        throw new Error(`Failed to credit company wallet: ${companyWalletError.message}`);\n                    }\n                    companyLeftoverAmount += parseFloat(distribution.commission_amount.toString());\n                    console.log(`Credited Rs ${distribution.commission_amount} leftover to company wallet`);\n                    continue; // Skip normal commission processing for company allocations\n                }\n                const transactionId = `comm_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n                // Insert commission transaction (let id auto-generate as UUID)\n                const { error: commissionError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from(\"commission_transactions\").insert({\n                    transaction_id: transactionId,\n                    user_id: distribution.beneficiary_id,\n                    beneficiary_id: distribution.beneficiary_id,\n                    subscription_purchase_id: subscriptionId,\n                    commission_type: distribution.commission_type,\n                    commission_level: distribution.level_position,\n                    package_value: packageAmount,\n                    commission_rate: 0,\n                    commission_amount: distribution.commission_amount,\n                    status: \"processed\",\n                    metadata: distribution.metadata\n                });\n                if (commissionError) {\n                    console.error(\"Failed to insert commission transaction:\", commissionError);\n                    throw new Error(`Failed to insert commission transaction: ${commissionError.message}`);\n                }\n                // Get user's wallet ID first\n                const { data: userWallet, error: walletFetchError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from(\"user_wallets\").select(\"id\").eq(\"user_id\", distribution.beneficiary_id).single();\n                if (walletFetchError || !userWallet) {\n                    console.error(`Failed to get wallet for user ${distribution.beneficiary_id}:`, walletFetchError);\n                    throw new Error(`Failed to get wallet for user ${distribution.beneficiary_id}: ${walletFetchError?.message || \"Wallet not found\"}`);\n                }\n                // ✅ CORRECTED: Check if this is a gift system commission\n                const isGiftSystemCommission = distribution.metadata?.gift_system === true || [\n                    \"present_user\",\n                    \"annual_present_user\",\n                    \"present_leader\",\n                    \"annual_present_leader\"\n                ].includes(distribution.commission_type);\n                // Only update wallet balance for NON-gift system commissions\n                if (!isGiftSystemCommission) {\n                    // Update user's wallet balance using correct wallet_id\n                    const { error: walletError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.rpc(\"update_wallet_balance\", {\n                        p_wallet_id: userWallet.id,\n                        p_amount: distribution.commission_amount,\n                        p_transaction_type: \"deposit\",\n                        p_description: `${distribution.commission_type} commission from subscription`,\n                        p_reference_id: subscriptionId,\n                        p_reference_type: \"subscription\",\n                        p_metadata: {\n                            commission_type: distribution.commission_type,\n                            commission_level: distribution.level_position,\n                            subscription_id: subscriptionId\n                        }\n                    });\n                    if (walletError) {\n                        console.error(\"Failed to update wallet balance:\", walletError);\n                        throw new Error(`Failed to update wallet balance: ${walletError.message}`);\n                    }\n                    console.log(`✅ Added Rs ${distribution.commission_amount} to wallet for ${distribution.commission_type}`);\n                } else {\n                    console.log(`⚠️ Skipped wallet update for gift system commission: ${distribution.commission_type} (Rs ${distribution.commission_amount})`);\n                }\n                totalDistributed += parseFloat(distribution.commission_amount.toString());\n                distributionCount++;\n            }\n            // Process gift system commissions\n            try {\n                await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.rpc(\"distribute_gift_system_commissions\", {\n                    p_purchaser_id: purchaserId,\n                    p_subscription_id: subscriptionId,\n                    p_package_price: packageAmount\n                });\n                console.log(\"Gift system commissions distributed successfully\");\n            } catch (giftError) {\n                console.error(\"Failed to distribute gift system commissions:\", giftError);\n            // Don't fail the main distribution, but log the error\n            }\n            // Credit company profit to company wallet\n            const { data: packageStructure } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from(\"commission_structure\").select(\"company_wallet_amount\").eq(\"package_value\", packageAmount).eq(\"commission_type\", \"unified_structure\").single();\n            if (packageStructure?.company_wallet_amount) {\n                // ✅ Use new function to properly update company wallet balance\n                const { error: companyProfitError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.rpc(\"update_company_wallet_balance\", {\n                    p_amount: packageStructure.company_wallet_amount,\n                    p_transaction_type: \"company_profit\",\n                    p_description: `Company profit from subscription package Rs. ${packageAmount}`,\n                    p_subscription_id: subscriptionId,\n                    p_metadata: {\n                        package_price: packageAmount,\n                        subscription_id: subscriptionId\n                    }\n                });\n                if (companyProfitError) {\n                    console.error(\"Failed to credit company profit:\", companyProfitError);\n                    throw new Error(`Failed to credit company profit: ${companyProfitError.message}`);\n                }\n            }\n            console.log(`Commission distribution completed: Rs ${totalDistributed} to ${distributionCount} recipients, Rs ${companyLeftoverAmount} leftover to company`);\n            return {\n                totalDistributed,\n                transactionsCreated: distributionCount,\n                unallocatedAmount: companyLeftoverAmount,\n                distributionDetails: distributions.filter((d)=>d.beneficiary_id !== null).map((d)=>({\n                        level: d.level_position,\n                        beneficiaryId: d.beneficiary_id,\n                        amount: parseFloat(d.commission_amount.toString()),\n                        rate: 0 // Not applicable for absolute amounts\n                    }))\n            };\n        } catch (error) {\n            console.error(\"Error distributing commissions:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Process pending commission transactions and credit user wallets\n   */ static async processPendingCommissions() {\n        try {\n            if (!_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin) {\n                throw new Error(\"Admin client not available\");\n            }\n            // Get all pending commission transactions\n            const { data: pendingCommissions, error: fetchError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.COMMISSION_TRANSACTIONS).select(\"*\").eq(\"status\", \"pending\").order(\"created_at\", {\n                ascending: true\n            });\n            if (fetchError) {\n                throw new Error(`Failed to fetch pending commissions: ${fetchError.message}`);\n            }\n            if (!pendingCommissions || pendingCommissions.length === 0) {\n                return {\n                    processed: 0,\n                    totalAmount: 0,\n                    errors: []\n                };\n            }\n            let processedCount = 0;\n            let totalAmount = 0;\n            const errors = [];\n            for (const commission of pendingCommissions){\n                try {\n                    // Get beneficiary's wallet\n                    const { data: wallet, error: walletError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from(\"user_wallets\").select(\"id, balance\").eq(\"user_id\", commission.beneficiary_id).single();\n                    if (walletError || !wallet) {\n                        errors.push(`No wallet found for beneficiary ${commission.beneficiary_id}`);\n                        continue;\n                    }\n                    // Create wallet transaction\n                    const { data: walletTransaction, error: walletTransError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from(\"wallet_transactions\").insert({\n                        wallet_id: wallet.id,\n                        transaction_type: \"commission\",\n                        amount: commission.commission_amount,\n                        balance_before: wallet.balance,\n                        balance_after: parseFloat(wallet.balance) + parseFloat(commission.commission_amount),\n                        description: `Level ${commission.commission_level} commission from subscription`,\n                        reference_id: commission.subscription_purchase_id,\n                        reference_type: \"subscription\",\n                        status: \"completed\",\n                        metadata: {\n                            commission_type: commission.commission_type,\n                            commission_level: commission.commission_level,\n                            original_transaction_id: commission.transaction_id\n                        }\n                    }).select().single();\n                    if (walletTransError) {\n                        errors.push(`Failed to create wallet transaction for ${commission.beneficiary_id}: ${walletTransError.message}`);\n                        continue;\n                    }\n                    // Update wallet balance\n                    const { error: balanceError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from(\"user_wallets\").update({\n                        balance: parseFloat(wallet.balance) + parseFloat(commission.commission_amount),\n                        updated_at: new Date().toISOString()\n                    }).eq(\"id\", wallet.id);\n                    if (balanceError) {\n                        errors.push(`Failed to update wallet balance for ${commission.beneficiary_id}: ${balanceError.message}`);\n                        continue;\n                    }\n                    // Update commission transaction status\n                    const { error: commissionUpdateError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.COMMISSION_TRANSACTIONS).update({\n                        status: \"processed\",\n                        processed_at: new Date().toISOString(),\n                        wallet_transaction_id: walletTransaction.id\n                    }).eq(\"id\", commission.id);\n                    if (commissionUpdateError) {\n                        errors.push(`Failed to update commission status for ${commission.id}: ${commissionUpdateError.message}`);\n                        continue;\n                    }\n                    processedCount++;\n                    totalAmount += parseFloat(commission.commission_amount);\n                } catch (error) {\n                    errors.push(`Error processing commission ${commission.id}: ${error instanceof Error ? error.message : \"Unknown error\"}`);\n                }\n            }\n            return {\n                processed: processedCount,\n                totalAmount,\n                errors\n            };\n        } catch (error) {\n            console.error(\"Error processing pending commissions:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Get commission summary for admin dashboard\n   */ static async getCommissionSummary() {\n        try {\n            const [totalPaidResult, pendingResult, failedResult, totalTransactionsResult, topEarnersResult] = await Promise.all([\n                _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.COMMISSION_TRANSACTIONS).select(\"commission_amount\").eq(\"status\", \"processed\"),\n                _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.COMMISSION_TRANSACTIONS).select(\"commission_amount\").eq(\"status\", \"pending\"),\n                _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.COMMISSION_TRANSACTIONS).select(\"*\", {\n                    count: \"exact\",\n                    head: true\n                }).eq(\"status\", \"failed\"),\n                _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.COMMISSION_TRANSACTIONS).select(\"*\", {\n                    count: \"exact\",\n                    head: true\n                }),\n                _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.USERS).select(\"id, full_name, total_commission_earned\").gt(\"total_commission_earned\", 0).order(\"total_commission_earned\", {\n                    ascending: false\n                }).limit(10)\n            ]);\n            const totalCommissionsPaid = totalPaidResult.data?.reduce((sum, t)=>sum + t.commission_amount, 0) || 0;\n            const pendingCommissions = pendingResult.data?.reduce((sum, t)=>sum + t.commission_amount, 0) || 0;\n            const failedCommissions = failedResult.count || 0;\n            const totalTransactions = totalTransactionsResult.count || 0;\n            const topEarners = topEarnersResult.data?.map((user)=>({\n                    userId: user.id,\n                    fullName: user.full_name || \"Unknown\",\n                    totalEarned: user.total_commission_earned || 0\n                })) || [];\n            return {\n                totalCommissionsPaid,\n                pendingCommissions,\n                failedCommissions,\n                totalTransactions,\n                topEarners\n            };\n        } catch (error) {\n            console.error(\"Error getting commission summary:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Update commission structure rates\n   */ static async updateCommissionStructure(id, updates) {\n        try {\n            const { error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.COMMISSION_STRUCTURE).update(updates).eq(\"id\", id);\n            if (error) {\n                throw new Error(`Failed to update commission structure: ${error.message}`);\n            }\n        } catch (error) {\n            console.error(\"Error updating commission structure:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Get commission transactions for a specific user with filters\n   * Excludes gift system commissions from user wallet view\n   */ static async getUserCommissionTransactions(userId, filters = {}, page = 1, limit = 20) {\n        try {\n            const offset = (page - 1) * limit;\n            let query = _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.COMMISSION_TRANSACTIONS).select(\"*\").eq(\"beneficiary_id\", userId)// Exclude gift system commissions from user wallet view\n            .not(\"commission_type\", \"in\", \"(present_user,annual_present_user,present_leader,annual_present_leader)\");\n            // Apply filters\n            if (filters.status) {\n                query = query.eq(\"status\", filters.status);\n            }\n            if (filters.commissionType) {\n                query = query.eq(\"commission_type\", filters.commissionType);\n            }\n            if (filters.dateFrom) {\n                query = query.gte(\"created_at\", filters.dateFrom);\n            }\n            if (filters.dateTo) {\n                query = query.lte(\"created_at\", filters.dateTo);\n            }\n            const [dataResult, countResult] = await Promise.all([\n                query.order(\"created_at\", {\n                    ascending: false\n                }).range(offset, offset + limit - 1),\n                query.select(\"*\", {\n                    count: \"exact\",\n                    head: true\n                })\n            ]);\n            if (dataResult.error) {\n                throw new Error(`Failed to get commission transactions: ${dataResult.error.message}`);\n            }\n            if (countResult.error) {\n                throw new Error(`Failed to count commission transactions: ${countResult.error.message}`);\n            }\n            return {\n                transactions: dataResult.data || [],\n                total: countResult.count || 0\n            };\n        } catch (error) {\n            console.error(\"Error getting user commission transactions:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Get ALL commission transactions for a specific user (including gift system commissions)\n   * This method is for admin use only\n   */ static async getAllUserCommissionTransactions(userId, filters = {}, page = 1, limit = 20) {\n        try {\n            const offset = (page - 1) * limit;\n            let query = _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.COMMISSION_TRANSACTIONS).select(\"*\").eq(\"beneficiary_id\", userId);\n            // Include ALL commission types (including gift system commissions)\n            // Apply filters\n            if (filters.status) {\n                query = query.eq(\"status\", filters.status);\n            }\n            if (filters.commissionType) {\n                query = query.eq(\"commission_type\", filters.commissionType);\n            }\n            if (filters.dateFrom) {\n                query = query.gte(\"created_at\", filters.dateFrom);\n            }\n            if (filters.dateTo) {\n                query = query.lte(\"created_at\", filters.dateTo);\n            }\n            const [dataResult, countResult] = await Promise.all([\n                query.order(\"created_at\", {\n                    ascending: false\n                }).range(offset, offset + limit - 1),\n                query.select(\"*\", {\n                    count: \"exact\",\n                    head: true\n                })\n            ]);\n            if (dataResult.error) {\n                throw new Error(`Failed to get all commission transactions: ${dataResult.error.message}`);\n            }\n            if (countResult.error) {\n                throw new Error(`Failed to count all commission transactions: ${countResult.error.message}`);\n            }\n            return {\n                transactions: dataResult.data || [],\n                total: countResult.count || 0\n            };\n        } catch (error) {\n            console.error(\"Error getting all user commission transactions:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Get detailed commission analytics for admin reports\n   */ static async getCommissionAnalytics(filters = {}) {\n        try {\n            let query = _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.COMMISSION_TRANSACTIONS).select(\"*\");\n            // Apply filters\n            if (filters.status) {\n                query = query.eq(\"status\", filters.status);\n            }\n            if (filters.commissionType) {\n                query = query.eq(\"commission_type\", filters.commissionType);\n            }\n            if (filters.level) {\n                query = query.eq(\"commission_level\", parseInt(filters.level));\n            }\n            if (filters.dateFrom) {\n                query = query.gte(\"created_at\", filters.dateFrom);\n            }\n            if (filters.dateTo) {\n                query = query.lte(\"created_at\", filters.dateTo);\n            }\n            const [dataResult, countResult] = await Promise.all([\n                query.order(\"created_at\", {\n                    ascending: false\n                }).limit(1000),\n                query.select(\"*\", {\n                    count: \"exact\",\n                    head: true\n                })\n            ]);\n            if (dataResult.error) {\n                throw new Error(`Failed to get commission analytics: ${dataResult.error.message}`);\n            }\n            if (countResult.error) {\n                throw new Error(`Failed to count commission analytics: ${countResult.error.message}`);\n            }\n            const transactions = dataResult.data || [];\n            // Calculate monthly data\n            const monthlyMap = new Map();\n            transactions.forEach((t)=>{\n                const month = new Date(t.created_at).toISOString().slice(0, 7) // YYYY-MM\n                ;\n                const existing = monthlyMap.get(month) || {\n                    amount: 0,\n                    count: 0\n                };\n                monthlyMap.set(month, {\n                    amount: existing.amount + t.commission_amount,\n                    count: existing.count + 1\n                });\n            });\n            const monthlyData = Array.from(monthlyMap.entries()).map(([month, data])=>({\n                    month,\n                    ...data\n                })).sort((a, b)=>a.month.localeCompare(b.month));\n            // Calculate level distribution\n            const levelMap = new Map();\n            transactions.forEach((t)=>{\n                const existing = levelMap.get(t.commission_level) || {\n                    amount: 0,\n                    count: 0\n                };\n                levelMap.set(t.commission_level, {\n                    amount: existing.amount + t.commission_amount,\n                    count: existing.count + 1\n                });\n            });\n            const levelDistribution = Array.from(levelMap.entries()).map(([level, data])=>({\n                    level,\n                    ...data\n                })).sort((a, b)=>a.level - b.level);\n            // Calculate type distribution\n            const typeMap = new Map();\n            transactions.forEach((t)=>{\n                const existing = typeMap.get(t.commission_type) || {\n                    amount: 0,\n                    count: 0\n                };\n                typeMap.set(t.commission_type, {\n                    amount: existing.amount + t.commission_amount,\n                    count: existing.count + 1\n                });\n            });\n            const typeDistribution = Array.from(typeMap.entries()).map(([type, data])=>({\n                    type,\n                    ...data\n                }));\n            return {\n                transactions,\n                total: countResult.count || 0,\n                monthlyData,\n                levelDistribution,\n                typeDistribution\n            };\n        } catch (error) {\n            console.error(\"Error getting commission analytics:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Get commission transactions with pagination and filters\n   */ static async getCommissionTransactionsWithFilters(filters = {}, page = 1, limit = 50) {\n        try {\n            const offset = (page - 1) * limit;\n            let query = _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.COMMISSION_TRANSACTIONS).select(\"*\");\n            // Apply filters\n            if (filters.status) {\n                query = query.eq(\"status\", filters.status);\n            }\n            if (filters.commissionType) {\n                query = query.eq(\"commission_type\", filters.commissionType);\n            }\n            if (filters.level) {\n                query = query.eq(\"commission_level\", parseInt(filters.level));\n            }\n            if (filters.dateFrom) {\n                query = query.gte(\"created_at\", filters.dateFrom);\n            }\n            if (filters.dateTo) {\n                query = query.lte(\"created_at\", filters.dateTo);\n            }\n            const [dataResult, countResult] = await Promise.all([\n                query.order(\"created_at\", {\n                    ascending: false\n                }).range(offset, offset + limit - 1),\n                query.select(\"*\", {\n                    count: \"exact\",\n                    head: true\n                })\n            ]);\n            if (dataResult.error) {\n                throw new Error(`Failed to get commission transactions: ${dataResult.error.message}`);\n            }\n            if (countResult.error) {\n                throw new Error(`Failed to count commission transactions: ${countResult.error.message}`);\n            }\n            return {\n                transactions: dataResult.data || [],\n                total: countResult.count || 0\n            };\n        } catch (error) {\n            console.error(\"Error getting commission transactions with filters:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Get commission breakdown by type for a user\n   */ static async getCommissionBreakdown(userId) {\n        try {\n            const response = await fetch(`/api/commission-breakdown?userId=${userId}`);\n            const result = await response.json();\n            if (!response.ok) {\n                throw new Error(result.error || \"Failed to fetch commission breakdown\");\n            }\n            if (!result.success) {\n                throw new Error(result.error || \"API request failed\");\n            }\n            // Map the extended data to the basic breakdown format\n            const data = result.data;\n            const breakdown = {\n                directCommission: data.directCommission || 0,\n                levelCommission: data.levelCommission || 0,\n                rsmBonus: data.rsmBonuses || 0,\n                zmBonus: data.zmBonuses || 0,\n                okdoiHeadCommission: data.okdoiHeadCommission || 0,\n                totalCommissions: data.totalCommissions || 0\n            };\n            return breakdown;\n        } catch (error) {\n            console.error(\"Error getting commission breakdown:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Get extended commission breakdown with all commission types\n   */ static async getExtendedCommissionBreakdown(userId) {\n        try {\n            const response = await fetch(`/api/commission-breakdown?userId=${userId}`);\n            const result = await response.json();\n            if (!response.ok) {\n                throw new Error(result.error || \"Failed to fetch commission breakdown\");\n            }\n            if (!result.success) {\n                throw new Error(result.error || \"API request failed\");\n            }\n            return result.data;\n        } catch (error) {\n            console.error(\"Error getting extended commission breakdown:\", error);\n            throw error;\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/services/commissionSystem.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/services/presentAllocationService.ts":
/*!******************************************************!*\
  !*** ./src/lib/services/presentAllocationService.ts ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PresentAllocationService: () => (/* binding */ PresentAllocationService)\n/* harmony export */ });\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/supabase */ \"(rsc)/./src/lib/supabase.ts\");\n\nclass PresentAllocationService {\n    /**\n   * Calculate and allocate presents from a package purchase\n   * This function is called automatically during commission distribution\n   */ static async allocatePresentsFromPurchase(purchaserId, packageAmount, subscriptionId, transactionId) {\n        try {\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.rpc(\"calculate_present_allocations\", {\n                purchaser_id: purchaserId,\n                package_amount: packageAmount,\n                subscription_id: subscriptionId,\n                transaction_id: transactionId\n            });\n            if (error) {\n                throw new Error(`Failed to allocate presents: ${error.message}`);\n            }\n            return data || [];\n        } catch (error) {\n            console.error(\"PresentAllocationService.allocatePresentsFromPurchase:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Get present pool summary (admin-only)\n   */ static async getPresentPoolSummary() {\n        try {\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.rpc(\"get_present_pool_summary\");\n            if (error) {\n                throw new Error(`Failed to get present pool summary: ${error.message}`);\n            }\n            return data || [];\n        } catch (error) {\n            console.error(\"PresentAllocationService.getPresentPoolSummary:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Get user present balance (admin-only)\n   */ static async getUserPresentBalance(userId) {\n        try {\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.rpc(\"get_user_present_balance\", {\n                target_user_id: userId\n            });\n            if (error) {\n                throw new Error(`Failed to get user present balance: ${error.message}`);\n            }\n            return data || [];\n        } catch (error) {\n            console.error(\"PresentAllocationService.getUserPresentBalance:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Get present allocations for a user (admin-only)\n   */ static async getUserPresentAllocations(userId, allocationType, limit = 50, offset = 0) {\n        try {\n            let query = _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from(\"present_allocations\").select(\"*\", {\n                count: \"exact\"\n            }).eq(\"user_id\", userId).order(\"allocation_date\", {\n                ascending: false\n            }).range(offset, offset + limit - 1);\n            if (allocationType) {\n                query = query.eq(\"allocation_type\", allocationType);\n            }\n            const { data, error, count } = await query;\n            if (error) {\n                throw new Error(`Failed to get present allocations: ${error.message}`);\n            }\n            return {\n                allocations: data || [],\n                total: count || 0\n            };\n        } catch (error) {\n            console.error(\"PresentAllocationService.getUserPresentAllocations:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Redeem annual present for a user (admin action)\n   */ static async redeemAnnualPresent(userId, allocationIds, redeemAmount, adminId) {\n        try {\n            // Start transaction\n            const { error: updateError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from(\"present_allocations\").update({\n                is_redeemed: true,\n                redeemed_at: new Date().toISOString(),\n                redeemed_amount: redeemAmount\n            }).in(\"id\", allocationIds).eq(\"user_id\", userId).eq(\"is_redeemed\", false);\n            if (updateError) {\n                throw new Error(`Failed to update present allocations: ${updateError.message}`);\n            }\n            // Create gift transaction record\n            const transactionId = `ANNUAL_${Date.now()}_${userId.substring(0, 8)}`;\n            const { error: giftError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from(\"gift_transactions\").insert({\n                transaction_id: transactionId,\n                user_id: userId,\n                gift_type: \"annual_present\",\n                gift_amount: redeemAmount,\n                gift_description: `Annual present redemption for ${allocationIds.length} allocations`,\n                status: \"processed\",\n                processed_at: new Date().toISOString(),\n                processed_by: adminId,\n                metadata: {\n                    allocation_ids: allocationIds,\n                    redemption_type: \"annual_present\"\n                }\n            });\n            if (giftError) {\n                throw new Error(`Failed to create gift transaction: ${giftError.message}`);\n            }\n        } catch (error) {\n            console.error(\"PresentAllocationService.redeemAnnualPresent:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Get present allocation statistics (admin-only)\n   */ static async getPresentAllocationStats() {\n        try {\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from(\"present_allocations\").select(\"allocation_type, allocation_amount, is_redeemed, redeemed_amount\");\n            if (error) {\n                throw new Error(`Failed to get present allocation stats: ${error.message}`);\n            }\n            const stats = {\n                totalAllocations: data.length,\n                totalAmount: 0,\n                totalRedeemed: 0,\n                totalPending: 0,\n                byType: {}\n            };\n            data.forEach((allocation)=>{\n                stats.totalAmount += allocation.allocation_amount;\n                if (allocation.is_redeemed) {\n                    stats.totalRedeemed += allocation.redeemed_amount;\n                } else {\n                    stats.totalPending += allocation.allocation_amount;\n                }\n                if (!stats.byType[allocation.allocation_type]) {\n                    stats.byType[allocation.allocation_type] = {\n                        count: 0,\n                        amount: 0,\n                        redeemed: 0\n                    };\n                }\n                stats.byType[allocation.allocation_type].count++;\n                stats.byType[allocation.allocation_type].amount += allocation.allocation_amount;\n                if (allocation.is_redeemed) {\n                    stats.byType[allocation.allocation_type].redeemed += allocation.redeemed_amount;\n                }\n            });\n            return stats;\n        } catch (error) {\n            console.error(\"PresentAllocationService.getPresentAllocationStats:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Get all present allocations with filters (admin-only)\n   */ static async getAllPresentAllocations(filters) {\n        try {\n            let query = _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from(\"present_allocations\").select(`\n          *,\n          user:users!present_allocations_user_id_fkey(email, full_name)\n        `, {\n                count: \"exact\"\n            }).order(\"allocation_date\", {\n                ascending: false\n            });\n            // Apply filters\n            if (filters.allocationType) {\n                query = query.eq(\"allocation_type\", filters.allocationType);\n            }\n            if (filters.isRedeemed) {\n                query = query.eq(\"is_redeemed\", filters.isRedeemed === \"true\");\n            }\n            if (filters.dateFrom) {\n                query = query.gte(\"allocation_date\", filters.dateFrom);\n            }\n            if (filters.dateTo) {\n                query = query.lte(\"allocation_date\", filters.dateTo);\n            }\n            // Apply pagination\n            const limit = filters.limit || 50;\n            const offset = filters.offset || 0;\n            query = query.range(offset, offset + limit - 1);\n            const { data, error, count } = await query;\n            if (error) {\n                throw new Error(`Failed to get present allocations: ${error.message}`);\n            }\n            return {\n                allocations: data || [],\n                total: count || 0\n            };\n        } catch (error) {\n            console.error(\"PresentAllocationService.getAllPresentAllocations:\", error);\n            throw error;\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/services/presentAllocationService.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/supabase.ts":
/*!*****************************!*\
  !*** ./src/lib/supabase.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TABLES: () => (/* binding */ TABLES),\n/* harmony export */   supabase: () => (/* binding */ supabase),\n/* harmony export */   supabaseAdmin: () => (/* binding */ supabaseAdmin)\n/* harmony export */ });\n/* harmony import */ var _supabase_ssr__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/ssr */ \"(rsc)/./node_modules/@supabase/ssr/dist/module/index.js\");\n/* harmony import */ var _barrel_optimize_names_createClient_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! __barrel_optimize__?names=createClient!=!@supabase/supabase-js */ \"(rsc)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n\n\n// Environment variables with validation\nconst supabaseUrl = \"https://vnmydqbwjjufnxngpnqo.supabase.co\";\nconst supabaseAnonKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InZubXlkcWJ3amp1Zm54bmdwbnFvIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTYyODkyNjgsImV4cCI6MjA3MTg2NTI2OH0.23oAdwSQ11jasIhrtZf71oeC6ehBsgTda1iCRU8myCo\";\nconst supabaseServiceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;\n// Validate required environment variables\nif (!supabaseUrl) {\n    console.error(\"Missing NEXT_PUBLIC_SUPABASE_URL environment variable\");\n    throw new Error(\"Missing NEXT_PUBLIC_SUPABASE_URL environment variable\");\n}\nif (!supabaseAnonKey) {\n    console.error(\"Missing NEXT_PUBLIC_SUPABASE_ANON_KEY environment variable\");\n    throw new Error(\"Missing NEXT_PUBLIC_SUPABASE_ANON_KEY environment variable\");\n}\nif (!supabaseServiceRoleKey) {\n    console.warn(\"Missing SUPABASE_SERVICE_ROLE_KEY environment variable - admin functions will not work\");\n}\n// Create browser client with error handling\nlet supabase;\ntry {\n    supabase = (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createBrowserClient)(supabaseUrl, supabaseAnonKey, {\n        cookies: {\n            get (name) {\n                if (typeof document !== \"undefined\") {\n                    const value = document.cookie.split(\"; \").find((row)=>row.startsWith(`${name}=`))?.split(\"=\")[1];\n                    return value ? decodeURIComponent(value) : undefined;\n                }\n                return undefined;\n            },\n            set (name, value, options) {\n                if (typeof document !== \"undefined\") {\n                    let cookieString = `${name}=${encodeURIComponent(value)}`;\n                    if (options?.maxAge) cookieString += `; max-age=${options.maxAge}`;\n                    if (options?.path) cookieString += `; path=${options.path}`;\n                    if (options?.domain) cookieString += `; domain=${options.domain}`;\n                    if (options?.secure) cookieString += \"; secure\";\n                    if (options?.httpOnly) cookieString += \"; httponly\";\n                    if (options?.sameSite) cookieString += `; samesite=${options.sameSite}`;\n                    document.cookie = cookieString;\n                }\n            },\n            remove (name, options) {\n                if (typeof document !== \"undefined\") {\n                    let cookieString = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 GMT`;\n                    if (options?.path) cookieString += `; path=${options.path}`;\n                    if (options?.domain) cookieString += `; domain=${options.domain}`;\n                    document.cookie = cookieString;\n                }\n            }\n        }\n    });\n} catch (error) {\n    console.error(\"Failed to create Supabase browser client:\", error);\n    // Fallback to basic client without SSR\n    supabase = (0,_barrel_optimize_names_createClient_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_1__.createClient)(supabaseUrl, supabaseAnonKey);\n}\n// Admin client with service role key for bypassing RLS\n// Note: This will be null on client-side for security reasons\nconst supabaseAdmin = supabaseServiceRoleKey ? (0,_barrel_optimize_names_createClient_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_1__.createClient)(supabaseUrl, supabaseServiceRoleKey, {\n    auth: {\n        autoRefreshToken: false,\n        persistSession: false\n    }\n}) : null;\n\n// Database table names\nconst TABLES = {\n    CATEGORIES: \"categories\",\n    SUBCATEGORIES: \"subcategories\",\n    ADS: \"ads\",\n    USERS: \"users\",\n    AD_IMAGES: \"ad_images\",\n    DISTRICTS: \"districts\",\n    CITIES: \"cities\",\n    USER_FAVORITES: \"user_favorites\",\n    VENDOR_SHOPS: \"vendor_shops\",\n    SHOP_PRODUCTS: \"shop_products\",\n    SHOP_PRODUCT_IMAGES: \"shop_product_images\",\n    SHOP_REVIEWS: \"shop_reviews\",\n    SHOP_FOLLOWERS: \"shop_followers\",\n    SHOP_CATEGORIES: \"shop_categories\",\n    SHOP_SUBCATEGORIES: \"shop_subcategories\",\n    PRODUCT_REVIEWS: \"product_reviews\",\n    CHAT_CONVERSATIONS: \"chat_conversations\",\n    CHAT_MESSAGES: \"chat_messages\",\n    USER_WALLETS: \"user_wallets\",\n    WALLET_TRANSACTIONS: \"wallet_transactions\",\n    P2P_TRANSFERS: \"p2p_transfers\",\n    DEPOSIT_REQUESTS: \"deposit_requests\",\n    WITHDRAWAL_REQUESTS: \"withdrawal_requests\",\n    SUBSCRIPTION_PACKAGES: \"subscription_packages\",\n    USER_SUBSCRIPTIONS: \"user_subscriptions\",\n    AD_BOOSTS: \"ad_boosts\",\n    BOOST_PACKAGES: \"boost_packages\",\n    // Order Management System\n    CART_ITEMS: \"cart_items\",\n    SHOP_ORDERS: \"shop_orders\",\n    ORDER_ITEMS: \"order_items\",\n    ORDER_STATUS_HISTORY: \"order_status_history\",\n    // Merchant Wallet System\n    MERCHANT_WALLETS: \"merchant_wallets\",\n    MERCHANT_WALLET_TRANSACTIONS: \"merchant_wallet_transactions\",\n    MERCHANT_TO_MAIN_TRANSFERS: \"merchant_to_main_transfers\",\n    // Referral & Commission System\n    REFERRAL_HIERARCHY: \"referral_hierarchy\",\n    COMMISSION_STRUCTURE: \"commission_structure\",\n    COMMISSION_TRANSACTIONS: \"commission_transactions\",\n    REFERRAL_PLACEMENTS: \"referral_placements\",\n    // KYC System\n    KYC_SUBMISSIONS: \"kyc_submissions\",\n    KYC_STATUS_HISTORY: \"kyc_status_history\",\n    KYC_DOCUMENT_TYPES: \"kyc_document_types\",\n    ZONAL_MANAGERS: \"zonal_managers\",\n    REGIONAL_SALES_MANAGERS: \"regional_sales_managers\"\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/supabase.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/whatwg-url","vendor-chunks/cookie","vendor-chunks/webidl-conversions"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fsubscription%2Fpurchase%2Froute&page=%2Fapi%2Fsubscription%2Fpurchase%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsubscription%2Fpurchase%2Froute.ts&appDir=C%3A%5CUsers%5Cdream%5CDesktop%5Cokdoi%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cdream%5CDesktop%5Cokdoi&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();