"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/subscription/purchase/route";
exports.ids = ["app/api/subscription/purchase/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

module.exports = require("https");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("stream");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fsubscription%2Fpurchase%2Froute&page=%2Fapi%2Fsubscription%2Fpurchase%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsubscription%2Fpurchase%2Froute.ts&appDir=C%3A%5CUsers%5Cdream%5CDesktop%5Cokdoi%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cdream%5CDesktop%5Cokdoi&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fsubscription%2Fpurchase%2Froute&page=%2Fapi%2Fsubscription%2Fpurchase%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsubscription%2Fpurchase%2Froute.ts&appDir=C%3A%5CUsers%5Cdream%5CDesktop%5Cokdoi%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cdream%5CDesktop%5Cokdoi&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_dream_Desktop_okdoi_src_app_api_subscription_purchase_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/subscription/purchase/route.ts */ \"(rsc)/./src/app/api/subscription/purchase/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/subscription/purchase/route\",\n        pathname: \"/api/subscription/purchase\",\n        filename: \"route\",\n        bundlePath: \"app/api/subscription/purchase/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\api\\\\subscription\\\\purchase\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_dream_Desktop_okdoi_src_app_api_subscription_purchase_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/subscription/purchase/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fsubscription%2Fpurchase%2Froute&page=%2Fapi%2Fsubscription%2Fpurchase%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsubscription%2Fpurchase%2Froute.ts&appDir=C%3A%5CUsers%5Cdream%5CDesktop%5Cokdoi%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cdream%5CDesktop%5Cokdoi&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/subscription/purchase/route.ts":
/*!****************************************************!*\
  !*** ./src/app/api/subscription/purchase/route.ts ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/supabase */ \"(rsc)/./src/lib/supabase.ts\");\n\n\nasync function POST(request) {\n    try {\n        // Get the authorization header\n        const authHeader = request.headers.get(\"authorization\");\n        if (!authHeader) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Authorization header required\"\n            }, {\n                status: 401\n            });\n        }\n        // Verify the user token\n        const token = authHeader.replace(\"Bearer \", \"\");\n        const { data: { user }, error: authError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.auth.getUser(token);\n        if (authError || !user) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Invalid token\"\n            }, {\n                status: 401\n            });\n        }\n        // Get request body\n        const body = await request.json();\n        const { packageId, walletId } = body;\n        if (!packageId || !walletId) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Package ID and Wallet ID are required\"\n            }, {\n                status: 400\n            });\n        }\n        console.log(`Processing subscription purchase for user ${user.id}, package ${packageId}`);\n        // Get package details using admin client\n        const { data: package_data, error: packageError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabaseAdmin.from(\"subscription_packages\").select(\"*\").eq(\"id\", packageId).eq(\"is_active\", true).single();\n        if (packageError || !package_data) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Subscription package not found or inactive\"\n            }, {\n                status: 404\n            });\n        }\n        // Check wallet balance using admin client\n        const { data: wallet, error: walletError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabaseAdmin.from(\"user_wallets\").select(\"balance\").eq(\"id\", walletId).eq(\"user_id\", user.id).single();\n        if (walletError || !wallet) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Wallet not found\"\n            }, {\n                status: 404\n            });\n        }\n        if (wallet.balance < package_data.price) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Insufficient wallet balance\"\n            }, {\n                status: 400\n            });\n        }\n        // Calculate expiration date\n        const expiresAt = new Date();\n        expiresAt.setDate(expiresAt.getDate() + package_data.duration_days);\n        // Create subscription using admin client\n        const { data: subscription, error: subscriptionError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabaseAdmin.from(\"user_subscriptions\").insert({\n            user_id: user.id,\n            package_id: packageId,\n            expires_at: expiresAt.toISOString()\n        }).select(`\n        *,\n        package:subscription_packages(*)\n      `).single();\n        if (subscriptionError) {\n            console.error(\"Failed to create subscription:\", subscriptionError);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: `Failed to create subscription: ${subscriptionError.message}`\n            }, {\n                status: 500\n            });\n        }\n        // Deduct from wallet using admin client\n        const { error: walletUpdateError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabaseAdmin.rpc(\"update_wallet_balance\", {\n            p_wallet_id: walletId,\n            p_amount: package_data.price,\n            p_transaction_type: \"purchase\",\n            p_description: `Subscription purchase: ${package_data.name}`,\n            p_reference_id: subscription.id,\n            p_reference_type: \"subscription\",\n            p_metadata: {\n                package_id: packageId,\n                package_name: package_data.name,\n                package_price: package_data.price,\n                duration_days: package_data.duration_days\n            }\n        });\n        if (walletUpdateError) {\n            console.error(\"Failed to process payment:\", walletUpdateError);\n            // Rollback subscription creation\n            await _lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabaseAdmin.from(\"user_subscriptions\").delete().eq(\"id\", subscription.id);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: `Failed to process payment: ${walletUpdateError.message}`\n            }, {\n                status: 500\n            });\n        }\n        console.log(`Subscription ${subscription.id} created successfully, processing commissions in background...`);\n        // Return success immediately to user\n        const response = next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            message: \"Subscription purchased successfully\",\n            data: subscription\n        });\n        // Process commission distribution asynchronously in background\n        // This doesn't block the user response\n        setImmediate(async ()=>{\n            try {\n                console.log(`Starting background commission processing for subscription ${subscription.id}`);\n                // Call the dedicated commission processing endpoint\n                const response = await fetch(`${process.env.NEXT_PUBLIC_SITE_URL || \"http://localhost:3000\"}/api/subscription/process-commissions`, {\n                    method: \"POST\",\n                    headers: {\n                        \"Content-Type\": \"application/json\"\n                    },\n                    body: JSON.stringify({\n                        subscriptionId: subscription.id,\n                        userId: user.id,\n                        packagePrice: package_data.price\n                    })\n                });\n                if (!response.ok) {\n                    throw new Error(`Commission processing failed: ${response.statusText}`);\n                }\n                const result = await response.json();\n                console.log(`Background commission processing completed for subscription ${subscription.id}:`, result);\n            } catch (error) {\n                console.error(\"Failed to process commissions in background:\", error);\n            // Could implement retry logic or queue system here\n            }\n        });\n        return response;\n    } catch (error) {\n        console.error(\"Error in subscription purchase API:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: error instanceof Error ? error.message : \"Internal server error\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/subscription/purchase/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/supabase.ts":
/*!*****************************!*\
  !*** ./src/lib/supabase.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TABLES: () => (/* binding */ TABLES),\n/* harmony export */   supabase: () => (/* binding */ supabase),\n/* harmony export */   supabaseAdmin: () => (/* binding */ supabaseAdmin)\n/* harmony export */ });\n/* harmony import */ var _supabase_ssr__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/ssr */ \"(rsc)/./node_modules/@supabase/ssr/dist/module/index.js\");\n/* harmony import */ var _barrel_optimize_names_createClient_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! __barrel_optimize__?names=createClient!=!@supabase/supabase-js */ \"(rsc)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n\n\n// Environment variables with validation\nconst supabaseUrl = \"https://vnmydqbwjjufnxngpnqo.supabase.co\";\nconst supabaseAnonKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InZubXlkcWJ3amp1Zm54bmdwbnFvIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTYyODkyNjgsImV4cCI6MjA3MTg2NTI2OH0.23oAdwSQ11jasIhrtZf71oeC6ehBsgTda1iCRU8myCo\";\nconst supabaseServiceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;\n// Validate required environment variables\nif (!supabaseUrl) {\n    console.error(\"Missing NEXT_PUBLIC_SUPABASE_URL environment variable\");\n    throw new Error(\"Missing NEXT_PUBLIC_SUPABASE_URL environment variable\");\n}\nif (!supabaseAnonKey) {\n    console.error(\"Missing NEXT_PUBLIC_SUPABASE_ANON_KEY environment variable\");\n    throw new Error(\"Missing NEXT_PUBLIC_SUPABASE_ANON_KEY environment variable\");\n}\nif (!supabaseServiceRoleKey) {\n    console.warn(\"Missing SUPABASE_SERVICE_ROLE_KEY environment variable - admin functions will not work\");\n}\n// Create browser client with error handling\nlet supabase;\ntry {\n    supabase = (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createBrowserClient)(supabaseUrl, supabaseAnonKey, {\n        cookies: {\n            get (name) {\n                if (typeof document !== \"undefined\") {\n                    const value = document.cookie.split(\"; \").find((row)=>row.startsWith(`${name}=`))?.split(\"=\")[1];\n                    return value ? decodeURIComponent(value) : undefined;\n                }\n                return undefined;\n            },\n            set (name, value, options) {\n                if (typeof document !== \"undefined\") {\n                    let cookieString = `${name}=${encodeURIComponent(value)}`;\n                    if (options?.maxAge) cookieString += `; max-age=${options.maxAge}`;\n                    if (options?.path) cookieString += `; path=${options.path}`;\n                    if (options?.domain) cookieString += `; domain=${options.domain}`;\n                    if (options?.secure) cookieString += \"; secure\";\n                    if (options?.httpOnly) cookieString += \"; httponly\";\n                    if (options?.sameSite) cookieString += `; samesite=${options.sameSite}`;\n                    document.cookie = cookieString;\n                }\n            },\n            remove (name, options) {\n                if (typeof document !== \"undefined\") {\n                    let cookieString = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 GMT`;\n                    if (options?.path) cookieString += `; path=${options.path}`;\n                    if (options?.domain) cookieString += `; domain=${options.domain}`;\n                    document.cookie = cookieString;\n                }\n            }\n        }\n    });\n} catch (error) {\n    console.error(\"Failed to create Supabase browser client:\", error);\n    // Fallback to basic client without SSR\n    supabase = (0,_barrel_optimize_names_createClient_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_1__.createClient)(supabaseUrl, supabaseAnonKey);\n}\n// Admin client with service role key for bypassing RLS\n// Note: This will be null on client-side for security reasons\nconst supabaseAdmin = supabaseServiceRoleKey ? (0,_barrel_optimize_names_createClient_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_1__.createClient)(supabaseUrl, supabaseServiceRoleKey, {\n    auth: {\n        autoRefreshToken: false,\n        persistSession: false\n    }\n}) : null;\n\n// Database table names\nconst TABLES = {\n    CATEGORIES: \"categories\",\n    SUBCATEGORIES: \"subcategories\",\n    ADS: \"ads\",\n    USERS: \"users\",\n    AD_IMAGES: \"ad_images\",\n    DISTRICTS: \"districts\",\n    CITIES: \"cities\",\n    USER_FAVORITES: \"user_favorites\",\n    VENDOR_SHOPS: \"vendor_shops\",\n    SHOP_PRODUCTS: \"shop_products\",\n    SHOP_PRODUCT_IMAGES: \"shop_product_images\",\n    SHOP_REVIEWS: \"shop_reviews\",\n    SHOP_FOLLOWERS: \"shop_followers\",\n    SHOP_CATEGORIES: \"shop_categories\",\n    SHOP_SUBCATEGORIES: \"shop_subcategories\",\n    PRODUCT_REVIEWS: \"product_reviews\",\n    CHAT_CONVERSATIONS: \"chat_conversations\",\n    CHAT_MESSAGES: \"chat_messages\",\n    USER_WALLETS: \"user_wallets\",\n    WALLET_TRANSACTIONS: \"wallet_transactions\",\n    P2P_TRANSFERS: \"p2p_transfers\",\n    DEPOSIT_REQUESTS: \"deposit_requests\",\n    WITHDRAWAL_REQUESTS: \"withdrawal_requests\",\n    SUBSCRIPTION_PACKAGES: \"subscription_packages\",\n    USER_SUBSCRIPTIONS: \"user_subscriptions\",\n    AD_BOOSTS: \"ad_boosts\",\n    BOOST_PACKAGES: \"boost_packages\",\n    // Order Management System\n    CART_ITEMS: \"cart_items\",\n    SHOP_ORDERS: \"shop_orders\",\n    ORDER_ITEMS: \"order_items\",\n    ORDER_STATUS_HISTORY: \"order_status_history\",\n    // Merchant Wallet System\n    MERCHANT_WALLETS: \"merchant_wallets\",\n    MERCHANT_WALLET_TRANSACTIONS: \"merchant_wallet_transactions\",\n    MERCHANT_TO_MAIN_TRANSFERS: \"merchant_to_main_transfers\",\n    // Referral & Commission System\n    REFERRAL_HIERARCHY: \"referral_hierarchy\",\n    COMMISSION_STRUCTURE: \"commission_structure\",\n    COMMISSION_TRANSACTIONS: \"commission_transactions\",\n    REFERRAL_PLACEMENTS: \"referral_placements\",\n    // KYC System\n    KYC_SUBMISSIONS: \"kyc_submissions\",\n    KYC_STATUS_HISTORY: \"kyc_status_history\",\n    KYC_DOCUMENT_TYPES: \"kyc_document_types\",\n    ZONAL_MANAGERS: \"zonal_managers\",\n    REGIONAL_SALES_MANAGERS: \"regional_sales_managers\"\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3N1cGFiYXNlLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQW1EO0FBQ0M7QUFFcEQsd0NBQXdDO0FBQ3hDLE1BQU1FLGNBQWNDLDBDQUFvQztBQUN4RCxNQUFNRyxrQkFBa0JILGtOQUF5QztBQUNqRSxNQUFNSyx5QkFBeUJMLFFBQVFDLEdBQUcsQ0FBQ0sseUJBQXlCO0FBRXBFLDBDQUEwQztBQUMxQyxJQUFJLENBQUNQLGFBQWE7SUFDaEJRLFFBQVFDLEtBQUssQ0FBQztJQUNkLE1BQU0sSUFBSUMsTUFBTTtBQUNsQjtBQUVBLElBQUksQ0FBQ04saUJBQWlCO0lBQ3BCSSxRQUFRQyxLQUFLLENBQUM7SUFDZCxNQUFNLElBQUlDLE1BQU07QUFDbEI7QUFFQSxJQUFJLENBQUNKLHdCQUF3QjtJQUMzQkUsUUFBUUcsSUFBSSxDQUFDO0FBQ2Y7QUFFQSw0Q0FBNEM7QUFDNUMsSUFBSUM7QUFDSixJQUFJO0lBQ0ZBLFdBQVdkLGtFQUFtQkEsQ0FBQ0UsYUFBYUksaUJBQWlCO1FBQzNEUyxTQUFTO1lBQ1BDLEtBQUlDLElBQVk7Z0JBQ2QsSUFBSSxPQUFPQyxhQUFhLGFBQWE7b0JBQ25DLE1BQU1DLFFBQVFELFNBQVNFLE1BQU0sQ0FDMUJDLEtBQUssQ0FBQyxNQUNOQyxJQUFJLENBQUNDLENBQUFBLE1BQU9BLElBQUlDLFVBQVUsQ0FBQyxDQUFDLEVBQUVQLEtBQUssQ0FBQyxDQUFDLElBQ3BDSSxNQUFNLElBQUksQ0FBQyxFQUFFO29CQUNqQixPQUFPRixRQUFRTSxtQkFBbUJOLFNBQVNPO2dCQUM3QztnQkFDQSxPQUFPQTtZQUNUO1lBQ0FDLEtBQUlWLElBQVksRUFBRUUsS0FBYSxFQUFFUyxPQUFZO2dCQUMzQyxJQUFJLE9BQU9WLGFBQWEsYUFBYTtvQkFDbkMsSUFBSVcsZUFBZSxDQUFDLEVBQUVaLEtBQUssQ0FBQyxFQUFFYSxtQkFBbUJYLE9BQU8sQ0FBQztvQkFDekQsSUFBSVMsU0FBU0csUUFBUUYsZ0JBQWdCLENBQUMsVUFBVSxFQUFFRCxRQUFRRyxNQUFNLENBQUMsQ0FBQztvQkFDbEUsSUFBSUgsU0FBU0ksTUFBTUgsZ0JBQWdCLENBQUMsT0FBTyxFQUFFRCxRQUFRSSxJQUFJLENBQUMsQ0FBQztvQkFDM0QsSUFBSUosU0FBU0ssUUFBUUosZ0JBQWdCLENBQUMsU0FBUyxFQUFFRCxRQUFRSyxNQUFNLENBQUMsQ0FBQztvQkFDakUsSUFBSUwsU0FBU00sUUFBUUwsZ0JBQWdCO29CQUNyQyxJQUFJRCxTQUFTTyxVQUFVTixnQkFBZ0I7b0JBQ3ZDLElBQUlELFNBQVNRLFVBQVVQLGdCQUFnQixDQUFDLFdBQVcsRUFBRUQsUUFBUVEsUUFBUSxDQUFDLENBQUM7b0JBQ3ZFbEIsU0FBU0UsTUFBTSxHQUFHUztnQkFDcEI7WUFDRjtZQUNBUSxRQUFPcEIsSUFBWSxFQUFFVyxPQUFZO2dCQUMvQixJQUFJLE9BQU9WLGFBQWEsYUFBYTtvQkFDbkMsSUFBSVcsZUFBZSxDQUFDLEVBQUVaLEtBQUssd0NBQXdDLENBQUM7b0JBQ3BFLElBQUlXLFNBQVNJLE1BQU1ILGdCQUFnQixDQUFDLE9BQU8sRUFBRUQsUUFBUUksSUFBSSxDQUFDLENBQUM7b0JBQzNELElBQUlKLFNBQVNLLFFBQVFKLGdCQUFnQixDQUFDLFNBQVMsRUFBRUQsUUFBUUssTUFBTSxDQUFDLENBQUM7b0JBQ2pFZixTQUFTRSxNQUFNLEdBQUdTO2dCQUNwQjtZQUNGO1FBQ0Y7SUFDRjtBQUNGLEVBQUUsT0FBT2xCLE9BQU87SUFDZEQsUUFBUUMsS0FBSyxDQUFDLDZDQUE2Q0E7SUFDM0QsdUNBQXVDO0lBQ3ZDRyxXQUFXYixzR0FBWUEsQ0FBQ0MsYUFBYUk7QUFDdkM7QUFFQSx1REFBdUQ7QUFDdkQsOERBQThEO0FBQ3ZELE1BQU1nQyxnQkFBZ0I5Qix5QkFDekJQLHNHQUFZQSxDQUFDQyxhQUFhTSx3QkFBd0I7SUFDaEQrQixNQUFNO1FBQ0pDLGtCQUFrQjtRQUNsQkMsZ0JBQWdCO0lBQ2xCO0FBQ0YsS0FDQSxLQUFJO0FBRVc7QUFFbkIsdUJBQXVCO0FBQ2hCLE1BQU1DLFNBQVM7SUFDcEJDLFlBQVk7SUFDWkMsZUFBZTtJQUNmQyxLQUFLO0lBQ0xDLE9BQU87SUFDUEMsV0FBVztJQUNYQyxXQUFXO0lBQ1hDLFFBQVE7SUFDUkMsZ0JBQWdCO0lBQ2hCQyxjQUFjO0lBQ2RDLGVBQWU7SUFDZkMscUJBQXFCO0lBQ3JCQyxjQUFjO0lBQ2RDLGdCQUFnQjtJQUNoQkMsaUJBQWlCO0lBQ2pCQyxvQkFBb0I7SUFDcEJDLGlCQUFpQjtJQUNqQkMsb0JBQW9CO0lBQ3BCQyxlQUFlO0lBQ2ZDLGNBQWM7SUFDZEMscUJBQXFCO0lBQ3JCQyxlQUFlO0lBQ2ZDLGtCQUFrQjtJQUNsQkMscUJBQXFCO0lBQ3JCQyx1QkFBdUI7SUFDdkJDLG9CQUFvQjtJQUNwQkMsV0FBVztJQUNYQyxnQkFBZ0I7SUFDaEIsMEJBQTBCO0lBQzFCQyxZQUFZO0lBQ1pDLGFBQWE7SUFDYkMsYUFBYTtJQUNiQyxzQkFBc0I7SUFDdEIseUJBQXlCO0lBQ3pCQyxrQkFBa0I7SUFDbEJDLDhCQUE4QjtJQUM5QkMsNEJBQTRCO0lBQzVCLCtCQUErQjtJQUMvQkMsb0JBQW9CO0lBQ3BCQyxzQkFBc0I7SUFDdEJDLHlCQUF5QjtJQUN6QkMscUJBQXFCO0lBQ3JCLGFBQWE7SUFDYkMsaUJBQWlCO0lBQ2pCQyxvQkFBb0I7SUFDcEJDLG9CQUFvQjtJQUNwQkMsZ0JBQWdCO0lBQ2hCQyx5QkFBeUI7QUFDM0IsRUFBVSIsInNvdXJjZXMiOlsid2VicGFjazovL29rZG9pLW1hcmtldHBsYWNlLy4vc3JjL2xpYi9zdXBhYmFzZS50cz8wNmUxIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGNyZWF0ZUJyb3dzZXJDbGllbnQgfSBmcm9tICdAc3VwYWJhc2Uvc3NyJ1xuaW1wb3J0IHsgY3JlYXRlQ2xpZW50IH0gZnJvbSAnQHN1cGFiYXNlL3N1cGFiYXNlLWpzJ1xuXG4vLyBFbnZpcm9ubWVudCB2YXJpYWJsZXMgd2l0aCB2YWxpZGF0aW9uXG5jb25zdCBzdXBhYmFzZVVybCA9IHByb2Nlc3MuZW52Lk5FWFRfUFVCTElDX1NVUEFCQVNFX1VSTFxuY29uc3Qgc3VwYWJhc2VBbm9uS2V5ID0gcHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfU1VQQUJBU0VfQU5PTl9LRVlcbmNvbnN0IHN1cGFiYXNlU2VydmljZVJvbGVLZXkgPSBwcm9jZXNzLmVudi5TVVBBQkFTRV9TRVJWSUNFX1JPTEVfS0VZXG5cbi8vIFZhbGlkYXRlIHJlcXVpcmVkIGVudmlyb25tZW50IHZhcmlhYmxlc1xuaWYgKCFzdXBhYmFzZVVybCkge1xuICBjb25zb2xlLmVycm9yKCdNaXNzaW5nIE5FWFRfUFVCTElDX1NVUEFCQVNFX1VSTCBlbnZpcm9ubWVudCB2YXJpYWJsZScpXG4gIHRocm93IG5ldyBFcnJvcignTWlzc2luZyBORVhUX1BVQkxJQ19TVVBBQkFTRV9VUkwgZW52aXJvbm1lbnQgdmFyaWFibGUnKVxufVxuXG5pZiAoIXN1cGFiYXNlQW5vbktleSkge1xuICBjb25zb2xlLmVycm9yKCdNaXNzaW5nIE5FWFRfUFVCTElDX1NVUEFCQVNFX0FOT05fS0VZIGVudmlyb25tZW50IHZhcmlhYmxlJylcbiAgdGhyb3cgbmV3IEVycm9yKCdNaXNzaW5nIE5FWFRfUFVCTElDX1NVUEFCQVNFX0FOT05fS0VZIGVudmlyb25tZW50IHZhcmlhYmxlJylcbn1cblxuaWYgKCFzdXBhYmFzZVNlcnZpY2VSb2xlS2V5KSB7XG4gIGNvbnNvbGUud2FybignTWlzc2luZyBTVVBBQkFTRV9TRVJWSUNFX1JPTEVfS0VZIGVudmlyb25tZW50IHZhcmlhYmxlIC0gYWRtaW4gZnVuY3Rpb25zIHdpbGwgbm90IHdvcmsnKVxufVxuXG4vLyBDcmVhdGUgYnJvd3NlciBjbGllbnQgd2l0aCBlcnJvciBoYW5kbGluZ1xubGV0IHN1cGFiYXNlOiBSZXR1cm5UeXBlPHR5cGVvZiBjcmVhdGVCcm93c2VyQ2xpZW50PlxudHJ5IHtcbiAgc3VwYWJhc2UgPSBjcmVhdGVCcm93c2VyQ2xpZW50KHN1cGFiYXNlVXJsLCBzdXBhYmFzZUFub25LZXksIHtcbiAgICBjb29raWVzOiB7XG4gICAgICBnZXQobmFtZTogc3RyaW5nKSB7XG4gICAgICAgIGlmICh0eXBlb2YgZG9jdW1lbnQgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgY29uc3QgdmFsdWUgPSBkb2N1bWVudC5jb29raWVcbiAgICAgICAgICAgIC5zcGxpdCgnOyAnKVxuICAgICAgICAgICAgLmZpbmQocm93ID0+IHJvdy5zdGFydHNXaXRoKGAke25hbWV9PWApKVxuICAgICAgICAgICAgPy5zcGxpdCgnPScpWzFdXG4gICAgICAgICAgcmV0dXJuIHZhbHVlID8gZGVjb2RlVVJJQ29tcG9uZW50KHZhbHVlKSA6IHVuZGVmaW5lZFxuICAgICAgICB9XG4gICAgICAgIHJldHVybiB1bmRlZmluZWRcbiAgICAgIH0sXG4gICAgICBzZXQobmFtZTogc3RyaW5nLCB2YWx1ZTogc3RyaW5nLCBvcHRpb25zOiBhbnkpIHtcbiAgICAgICAgaWYgKHR5cGVvZiBkb2N1bWVudCAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICBsZXQgY29va2llU3RyaW5nID0gYCR7bmFtZX09JHtlbmNvZGVVUklDb21wb25lbnQodmFsdWUpfWBcbiAgICAgICAgICBpZiAob3B0aW9ucz8ubWF4QWdlKSBjb29raWVTdHJpbmcgKz0gYDsgbWF4LWFnZT0ke29wdGlvbnMubWF4QWdlfWBcbiAgICAgICAgICBpZiAob3B0aW9ucz8ucGF0aCkgY29va2llU3RyaW5nICs9IGA7IHBhdGg9JHtvcHRpb25zLnBhdGh9YFxuICAgICAgICAgIGlmIChvcHRpb25zPy5kb21haW4pIGNvb2tpZVN0cmluZyArPSBgOyBkb21haW49JHtvcHRpb25zLmRvbWFpbn1gXG4gICAgICAgICAgaWYgKG9wdGlvbnM/LnNlY3VyZSkgY29va2llU3RyaW5nICs9ICc7IHNlY3VyZSdcbiAgICAgICAgICBpZiAob3B0aW9ucz8uaHR0cE9ubHkpIGNvb2tpZVN0cmluZyArPSAnOyBodHRwb25seSdcbiAgICAgICAgICBpZiAob3B0aW9ucz8uc2FtZVNpdGUpIGNvb2tpZVN0cmluZyArPSBgOyBzYW1lc2l0ZT0ke29wdGlvbnMuc2FtZVNpdGV9YFxuICAgICAgICAgIGRvY3VtZW50LmNvb2tpZSA9IGNvb2tpZVN0cmluZ1xuICAgICAgICB9XG4gICAgICB9LFxuICAgICAgcmVtb3ZlKG5hbWU6IHN0cmluZywgb3B0aW9uczogYW55KSB7XG4gICAgICAgIGlmICh0eXBlb2YgZG9jdW1lbnQgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICAgICAgbGV0IGNvb2tpZVN0cmluZyA9IGAke25hbWV9PTsgZXhwaXJlcz1UaHUsIDAxIEphbiAxOTcwIDAwOjAwOjAwIEdNVGBcbiAgICAgICAgICBpZiAob3B0aW9ucz8ucGF0aCkgY29va2llU3RyaW5nICs9IGA7IHBhdGg9JHtvcHRpb25zLnBhdGh9YFxuICAgICAgICAgIGlmIChvcHRpb25zPy5kb21haW4pIGNvb2tpZVN0cmluZyArPSBgOyBkb21haW49JHtvcHRpb25zLmRvbWFpbn1gXG4gICAgICAgICAgZG9jdW1lbnQuY29va2llID0gY29va2llU3RyaW5nXG4gICAgICAgIH1cbiAgICAgIH1cbiAgICB9XG4gIH0pXG59IGNhdGNoIChlcnJvcikge1xuICBjb25zb2xlLmVycm9yKCdGYWlsZWQgdG8gY3JlYXRlIFN1cGFiYXNlIGJyb3dzZXIgY2xpZW50OicsIGVycm9yKVxuICAvLyBGYWxsYmFjayB0byBiYXNpYyBjbGllbnQgd2l0aG91dCBTU1JcbiAgc3VwYWJhc2UgPSBjcmVhdGVDbGllbnQoc3VwYWJhc2VVcmwsIHN1cGFiYXNlQW5vbktleSkgYXMgYW55XG59XG5cbi8vIEFkbWluIGNsaWVudCB3aXRoIHNlcnZpY2Ugcm9sZSBrZXkgZm9yIGJ5cGFzc2luZyBSTFNcbi8vIE5vdGU6IFRoaXMgd2lsbCBiZSBudWxsIG9uIGNsaWVudC1zaWRlIGZvciBzZWN1cml0eSByZWFzb25zXG5leHBvcnQgY29uc3Qgc3VwYWJhc2VBZG1pbiA9IHN1cGFiYXNlU2VydmljZVJvbGVLZXlcbiAgPyBjcmVhdGVDbGllbnQoc3VwYWJhc2VVcmwsIHN1cGFiYXNlU2VydmljZVJvbGVLZXksIHtcbiAgICAgIGF1dGg6IHtcbiAgICAgICAgYXV0b1JlZnJlc2hUb2tlbjogZmFsc2UsXG4gICAgICAgIHBlcnNpc3RTZXNzaW9uOiBmYWxzZVxuICAgICAgfVxuICAgIH0pXG4gIDogbnVsbFxuXG5leHBvcnQgeyBzdXBhYmFzZSB9XG5cbi8vIERhdGFiYXNlIHRhYmxlIG5hbWVzXG5leHBvcnQgY29uc3QgVEFCTEVTID0ge1xuICBDQVRFR09SSUVTOiAnY2F0ZWdvcmllcycsXG4gIFNVQkNBVEVHT1JJRVM6ICdzdWJjYXRlZ29yaWVzJyxcbiAgQURTOiAnYWRzJyxcbiAgVVNFUlM6ICd1c2VycycsXG4gIEFEX0lNQUdFUzogJ2FkX2ltYWdlcycsXG4gIERJU1RSSUNUUzogJ2Rpc3RyaWN0cycsXG4gIENJVElFUzogJ2NpdGllcycsXG4gIFVTRVJfRkFWT1JJVEVTOiAndXNlcl9mYXZvcml0ZXMnLFxuICBWRU5ET1JfU0hPUFM6ICd2ZW5kb3Jfc2hvcHMnLFxuICBTSE9QX1BST0RVQ1RTOiAnc2hvcF9wcm9kdWN0cycsXG4gIFNIT1BfUFJPRFVDVF9JTUFHRVM6ICdzaG9wX3Byb2R1Y3RfaW1hZ2VzJyxcbiAgU0hPUF9SRVZJRVdTOiAnc2hvcF9yZXZpZXdzJyxcbiAgU0hPUF9GT0xMT1dFUlM6ICdzaG9wX2ZvbGxvd2VycycsXG4gIFNIT1BfQ0FURUdPUklFUzogJ3Nob3BfY2F0ZWdvcmllcycsXG4gIFNIT1BfU1VCQ0FURUdPUklFUzogJ3Nob3Bfc3ViY2F0ZWdvcmllcycsXG4gIFBST0RVQ1RfUkVWSUVXUzogJ3Byb2R1Y3RfcmV2aWV3cycsXG4gIENIQVRfQ09OVkVSU0FUSU9OUzogJ2NoYXRfY29udmVyc2F0aW9ucycsXG4gIENIQVRfTUVTU0FHRVM6ICdjaGF0X21lc3NhZ2VzJyxcbiAgVVNFUl9XQUxMRVRTOiAndXNlcl93YWxsZXRzJyxcbiAgV0FMTEVUX1RSQU5TQUNUSU9OUzogJ3dhbGxldF90cmFuc2FjdGlvbnMnLFxuICBQMlBfVFJBTlNGRVJTOiAncDJwX3RyYW5zZmVycycsXG4gIERFUE9TSVRfUkVRVUVTVFM6ICdkZXBvc2l0X3JlcXVlc3RzJyxcbiAgV0lUSERSQVdBTF9SRVFVRVNUUzogJ3dpdGhkcmF3YWxfcmVxdWVzdHMnLFxuICBTVUJTQ1JJUFRJT05fUEFDS0FHRVM6ICdzdWJzY3JpcHRpb25fcGFja2FnZXMnLFxuICBVU0VSX1NVQlNDUklQVElPTlM6ICd1c2VyX3N1YnNjcmlwdGlvbnMnLFxuICBBRF9CT09TVFM6ICdhZF9ib29zdHMnLFxuICBCT09TVF9QQUNLQUdFUzogJ2Jvb3N0X3BhY2thZ2VzJyxcbiAgLy8gT3JkZXIgTWFuYWdlbWVudCBTeXN0ZW1cbiAgQ0FSVF9JVEVNUzogJ2NhcnRfaXRlbXMnLFxuICBTSE9QX09SREVSUzogJ3Nob3Bfb3JkZXJzJyxcbiAgT1JERVJfSVRFTVM6ICdvcmRlcl9pdGVtcycsXG4gIE9SREVSX1NUQVRVU19ISVNUT1JZOiAnb3JkZXJfc3RhdHVzX2hpc3RvcnknLFxuICAvLyBNZXJjaGFudCBXYWxsZXQgU3lzdGVtXG4gIE1FUkNIQU5UX1dBTExFVFM6ICdtZXJjaGFudF93YWxsZXRzJyxcbiAgTUVSQ0hBTlRfV0FMTEVUX1RSQU5TQUNUSU9OUzogJ21lcmNoYW50X3dhbGxldF90cmFuc2FjdGlvbnMnLFxuICBNRVJDSEFOVF9UT19NQUlOX1RSQU5TRkVSUzogJ21lcmNoYW50X3RvX21haW5fdHJhbnNmZXJzJyxcbiAgLy8gUmVmZXJyYWwgJiBDb21taXNzaW9uIFN5c3RlbVxuICBSRUZFUlJBTF9ISUVSQVJDSFk6ICdyZWZlcnJhbF9oaWVyYXJjaHknLFxuICBDT01NSVNTSU9OX1NUUlVDVFVSRTogJ2NvbW1pc3Npb25fc3RydWN0dXJlJyxcbiAgQ09NTUlTU0lPTl9UUkFOU0FDVElPTlM6ICdjb21taXNzaW9uX3RyYW5zYWN0aW9ucycsXG4gIFJFRkVSUkFMX1BMQUNFTUVOVFM6ICdyZWZlcnJhbF9wbGFjZW1lbnRzJyxcbiAgLy8gS1lDIFN5c3RlbVxuICBLWUNfU1VCTUlTU0lPTlM6ICdreWNfc3VibWlzc2lvbnMnLFxuICBLWUNfU1RBVFVTX0hJU1RPUlk6ICdreWNfc3RhdHVzX2hpc3RvcnknLFxuICBLWUNfRE9DVU1FTlRfVFlQRVM6ICdreWNfZG9jdW1lbnRfdHlwZXMnLFxuICBaT05BTF9NQU5BR0VSUzogJ3pvbmFsX21hbmFnZXJzJyxcbiAgUkVHSU9OQUxfU0FMRVNfTUFOQUdFUlM6ICdyZWdpb25hbF9zYWxlc19tYW5hZ2Vycydcbn0gYXMgY29uc3RcbiJdLCJuYW1lcyI6WyJjcmVhdGVCcm93c2VyQ2xpZW50IiwiY3JlYXRlQ2xpZW50Iiwic3VwYWJhc2VVcmwiLCJwcm9jZXNzIiwiZW52IiwiTkVYVF9QVUJMSUNfU1VQQUJBU0VfVVJMIiwic3VwYWJhc2VBbm9uS2V5IiwiTkVYVF9QVUJMSUNfU1VQQUJBU0VfQU5PTl9LRVkiLCJzdXBhYmFzZVNlcnZpY2VSb2xlS2V5IiwiU1VQQUJBU0VfU0VSVklDRV9ST0xFX0tFWSIsImNvbnNvbGUiLCJlcnJvciIsIkVycm9yIiwid2FybiIsInN1cGFiYXNlIiwiY29va2llcyIsImdldCIsIm5hbWUiLCJkb2N1bWVudCIsInZhbHVlIiwiY29va2llIiwic3BsaXQiLCJmaW5kIiwicm93Iiwic3RhcnRzV2l0aCIsImRlY29kZVVSSUNvbXBvbmVudCIsInVuZGVmaW5lZCIsInNldCIsIm9wdGlvbnMiLCJjb29raWVTdHJpbmciLCJlbmNvZGVVUklDb21wb25lbnQiLCJtYXhBZ2UiLCJwYXRoIiwiZG9tYWluIiwic2VjdXJlIiwiaHR0cE9ubHkiLCJzYW1lU2l0ZSIsInJlbW92ZSIsInN1cGFiYXNlQWRtaW4iLCJhdXRoIiwiYXV0b1JlZnJlc2hUb2tlbiIsInBlcnNpc3RTZXNzaW9uIiwiVEFCTEVTIiwiQ0FURUdPUklFUyIsIlNVQkNBVEVHT1JJRVMiLCJBRFMiLCJVU0VSUyIsIkFEX0lNQUdFUyIsIkRJU1RSSUNUUyIsIkNJVElFUyIsIlVTRVJfRkFWT1JJVEVTIiwiVkVORE9SX1NIT1BTIiwiU0hPUF9QUk9EVUNUUyIsIlNIT1BfUFJPRFVDVF9JTUFHRVMiLCJTSE9QX1JFVklFV1MiLCJTSE9QX0ZPTExPV0VSUyIsIlNIT1BfQ0FURUdPUklFUyIsIlNIT1BfU1VCQ0FURUdPUklFUyIsIlBST0RVQ1RfUkVWSUVXUyIsIkNIQVRfQ09OVkVSU0FUSU9OUyIsIkNIQVRfTUVTU0FHRVMiLCJVU0VSX1dBTExFVFMiLCJXQUxMRVRfVFJBTlNBQ1RJT05TIiwiUDJQX1RSQU5TRkVSUyIsIkRFUE9TSVRfUkVRVUVTVFMiLCJXSVRIRFJBV0FMX1JFUVVFU1RTIiwiU1VCU0NSSVBUSU9OX1BBQ0tBR0VTIiwiVVNFUl9TVUJTQ1JJUFRJT05TIiwiQURfQk9PU1RTIiwiQk9PU1RfUEFDS0FHRVMiLCJDQVJUX0lURU1TIiwiU0hPUF9PUkRFUlMiLCJPUkRFUl9JVEVNUyIsIk9SREVSX1NUQVRVU19ISVNUT1JZIiwiTUVSQ0hBTlRfV0FMTEVUUyIsIk1FUkNIQU5UX1dBTExFVF9UUkFOU0FDVElPTlMiLCJNRVJDSEFOVF9UT19NQUlOX1RSQU5TRkVSUyIsIlJFRkVSUkFMX0hJRVJBUkNIWSIsIkNPTU1JU1NJT05fU1RSVUNUVVJFIiwiQ09NTUlTU0lPTl9UUkFOU0FDVElPTlMiLCJSRUZFUlJBTF9QTEFDRU1FTlRTIiwiS1lDX1NVQk1JU1NJT05TIiwiS1lDX1NUQVRVU19ISVNUT1JZIiwiS1lDX0RPQ1VNRU5UX1RZUEVTIiwiWk9OQUxfTUFOQUdFUlMiLCJSRUdJT05BTF9TQUxFU19NQU5BR0VSUyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/supabase.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/whatwg-url","vendor-chunks/cookie","vendor-chunks/webidl-conversions"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fsubscription%2Fpurchase%2Froute&page=%2Fapi%2Fsubscription%2Fpurchase%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsubscription%2Fpurchase%2Froute.ts&appDir=C%3A%5CUsers%5Cdream%5CDesktop%5Cokdoi%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cdream%5CDesktop%5Cokdoi&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();