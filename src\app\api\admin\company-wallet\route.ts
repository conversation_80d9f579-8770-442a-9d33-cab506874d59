import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'
import { AdminService } from '@/lib/services/admin'

// Create admin Supabase client with service role key
const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  }
)

export async function GET(request: NextRequest) {
  try {
    // Check admin authentication
    const isAdmin = await AdminService.isAdminServerSide()
    if (!isAdmin) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    const filterType = searchParams.get('filterType') || 'all'
    const searchTerm = searchParams.get('searchTerm') || ''
    const dateFrom = searchParams.get('dateFrom') || ''
    const dateTo = searchParams.get('dateTo') || ''

    const offset = (page - 1) * limit

    // Fetch company wallet info
    const { data: walletData, error: walletError } = await supabaseAdmin
      .from('company_wallet')
      .select('*')
      .single()

    if (walletError && walletError.code !== 'PGRST116') {
      throw walletError
    }

    // Build query for transactions
    let query = supabaseAdmin
      .from('company_wallet_transactions')
      .select('*', { count: 'exact' })
      .order('created_at', { ascending: false })

    // Apply filters
    if (filterType !== 'all') {
      query = query.eq('transaction_type', filterType)
    }

    if (searchTerm) {
      query = query.or(`transaction_id.ilike.%${searchTerm}%,description.ilike.%${searchTerm}%`)
    }

    if (dateFrom) {
      query = query.gte('created_at', dateFrom)
    }

    if (dateTo) {
      query = query.lte('created_at', dateTo)
    }

    // Apply pagination
    query = query.range(offset, offset + limit - 1)

    const { data: transactions, error: transactionError, count } = await query

    if (transactionError) {
      throw transactionError
    }

    // Calculate stats
    let stats = {
      totalBalance: 0,
      totalReceived: 0,
      totalTransactions: 0,
      monthlyProfit: 0,
      averagePackageProfit: 0
    }

    if (walletData) {
      // Get monthly profit (current month)
      const currentMonth = new Date().toISOString().slice(0, 7) // YYYY-MM format
      const { data: monthlyData } = await supabaseAdmin
        .from('company_wallet_transactions')
        .select('amount')
        .gte('created_at', currentMonth + '-01')
        .lt('created_at', new Date(new Date().getFullYear(), new Date().getMonth() + 1, 1).toISOString())

      const monthlyProfit = monthlyData?.reduce((sum, t) => sum + parseFloat(t.amount), 0) || 0

      // Get total transactions count
      const { count: totalTransactions } = await supabaseAdmin
        .from('company_wallet_transactions')
        .select('*', { count: 'exact', head: true })

      // Calculate average package profit
      const { data: allTransactions } = await supabaseAdmin
        .from('company_wallet_transactions')
        .select('amount')
        .eq('transaction_type', 'company_profit')

      const averagePackageProfit = allTransactions && allTransactions.length > 0
        ? allTransactions.reduce((sum, t) => sum + parseFloat(t.amount), 0) / allTransactions.length
        : 0

      stats = {
        totalBalance: parseFloat(walletData.balance),
        totalReceived: parseFloat(walletData.total_received),
        totalTransactions: totalTransactions || 0,
        monthlyProfit,
        averagePackageProfit
      }
    }

    return NextResponse.json({
      success: true,
      data: {
        wallet: walletData,
        transactions: transactions || [],
        stats,
        pagination: {
          page,
          limit,
          total: count || 0,
          totalPages: Math.ceil((count || 0) / limit)
        }
      }
    })

  } catch (error) {
    console.error('GET /api/admin/company-wallet error:', error)
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to fetch company wallet data'
      },
      { status: 500 }
    )
  }
}
