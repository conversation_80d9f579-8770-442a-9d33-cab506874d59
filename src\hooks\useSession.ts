'use client'

import { useState, useEffect } from 'react'
import { sessionManager, type SessionState } from '@/lib/sessionManager'

export function useSession() {
  const [sessionState, setSessionState] = useState<SessionState>(() => 
    sessionManager.getState()
  )

  useEffect(() => {
    const unsubscribe = sessionManager.subscribe(setSessionState)
    return unsubscribe
  }, [])

  const refreshSession = async () => {
    return await sessionManager.refreshSession()
  }

  const signOut = async () => {
    await sessionManager.signOut()
  }

  const isSessionValid = async () => {
    return await sessionManager.isSessionValid()
  }

  const clearError = () => {
    sessionManager.clearError()
  }

  return {
    session: sessionState.session,
    user: sessionState.user,
    isLoading: sessionState.isLoading,
    error: sessionState.error,
    refreshSession,
    signOut,
    isSessionValid,
    clearError
  }
}
