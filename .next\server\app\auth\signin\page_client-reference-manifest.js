globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/auth/signin/page"]={"moduleLoading":{"prefix":"/_next/","crossOrigin":null},"ssrModuleMapping":{"(app-pages-browser)/./node_modules/next/dist/client/image-component.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/image-component.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/link.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/link.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/home/<USER>":{"*":{"id":"(ssr)/./src/components/home/<USER>","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/home/<USER>":{"*":{"id":"(ssr)/./src/components/home/<USER>","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/home/<USER>":{"*":{"id":"(ssr)/./src/components/home/<USER>","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/layout/Header.tsx":{"*":{"id":"(ssr)/./src/components/layout/Header.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/providers/QueryProvider.tsx":{"*":{"id":"(ssr)/./src/components/providers/QueryProvider.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/ui/ConfirmationDialog.tsx":{"*":{"id":"(ssr)/./src/components/ui/ConfirmationDialog.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/ui/ErrorBoundary.tsx":{"*":{"id":"(ssr)/./src/components/ui/ErrorBoundary.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/contexts/AlertContext.tsx":{"*":{"id":"(ssr)/./src/contexts/AlertContext.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/contexts/AuthContext.tsx":{"*":{"id":"(ssr)/./src/contexts/AuthContext.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/contexts/CartContext.tsx":{"*":{"id":"(ssr)/./src/contexts/CartContext.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/lib/performanceOptimizations.ts":{"*":{"id":"(ssr)/./src/lib/performanceOptimizations.ts","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/admin/page.tsx":{"*":{"id":"(ssr)/./src/app/admin/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/admin/users/page.tsx":{"*":{"id":"(ssr)/./src/app/admin/users/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/dashboard/wallet/page.tsx":{"*":{"id":"(ssr)/./src/app/dashboard/wallet/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/auth/GuestGuard.tsx":{"*":{"id":"(ssr)/./src/components/auth/GuestGuard.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/auth/NewSignInForm.tsx":{"*":{"id":"(ssr)/./src/components/auth/NewSignInForm.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/admin/company-wallet/page.tsx":{"*":{"id":"(ssr)/./src/app/admin/company-wallet/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/admin/commission-reports/page.tsx":{"*":{"id":"(ssr)/./src/app/admin/commission-reports/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/dashboard/page.tsx":{"*":{"id":"(ssr)/./src/app/dashboard/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/admin/deposits/page.tsx":{"*":{"id":"(ssr)/./src/app/admin/deposits/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/auth/NewSignUpContent.tsx":{"*":{"id":"(ssr)/./src/components/auth/NewSignUpContent.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/dashboard/subscription/page.tsx":{"*":{"id":"(ssr)/./src/app/dashboard/subscription/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/admin/analytics/page.tsx":{"*":{"id":"(ssr)/./src/app/admin/analytics/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/dashboard/settings/page.tsx":{"*":{"id":"(ssr)/./src/app/dashboard/settings/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/admin/commission-structure/page.tsx":{"*":{"id":"(ssr)/./src/app/admin/commission-structure/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/dashboard/referrals/page.tsx":{"*":{"id":"(ssr)/./src/app/dashboard/referrals/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/auth/verify-otp/page.tsx":{"*":{"id":"(ssr)/./src/app/auth/verify-otp/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/dashboard/profile/page.tsx":{"*":{"id":"(ssr)/./src/app/dashboard/profile/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/admin/referrals/page.tsx":{"*":{"id":"(ssr)/./src/app/admin/referrals/page.tsx","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"C:\\Users\\<USER>\\Desktop\\okdoi\\node_modules\\next\\dist\\client\\image-component.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/image-component.js","name":"*","chunks":["app/auth/signin/page","static/chunks/app/auth/signin/page.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\okdoi\\node_modules\\next\\dist\\esm\\client\\image-component.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/image-component.js","name":"*","chunks":["app/auth/signin/page","static/chunks/app/auth/signin/page.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\okdoi\\node_modules\\next\\dist\\client\\link.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/link.js","name":"*","chunks":["app/auth/signin/page","static/chunks/app/auth/signin/page.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\okdoi\\node_modules\\next\\dist\\esm\\client\\link.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/link.js","name":"*","chunks":["app/auth/signin/page","static/chunks/app/auth/signin/page.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\okdoi\\src\\components\\home\\CategoryGrid.tsx":{"id":"(app-pages-browser)/./src/components/home/<USER>","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\okdoi\\src\\components\\home\\FeaturedAds.tsx":{"id":"(app-pages-browser)/./src/components/home/<USER>","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\okdoi\\src\\components\\home\\Hero.tsx":{"id":"(app-pages-browser)/./src/components/home/<USER>","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\okdoi\\src\\components\\layout\\Header.tsx":{"id":"(app-pages-browser)/./src/components/layout/Header.tsx","name":"*","chunks":["app/auth/signin/page","static/chunks/app/auth/signin/page.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\okdoi\\node_modules\\next\\font\\google\\target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-inter\",\"display\":\"swap\"}],\"variableName\":\"inter\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-inter\",\"display\":\"swap\"}],\"variableName\":\"inter\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\okdoi\\node_modules\\next\\font\\google\\target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Poppins\",\"arguments\":[{\"subsets\":[\"latin\"],\"weight\":[\"300\",\"400\",\"500\",\"600\",\"700\"],\"variable\":\"--font-poppins\",\"display\":\"swap\"}],\"variableName\":\"poppins\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Poppins\",\"arguments\":[{\"subsets\":[\"latin\"],\"weight\":[\"300\",\"400\",\"500\",\"600\",\"700\"],\"variable\":\"--font-poppins\",\"display\":\"swap\"}],\"variableName\":\"poppins\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\okdoi\\node_modules\\next\\font\\google\\target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Manrope\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-manrope\",\"display\":\"swap\"}],\"variableName\":\"manrope\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Manrope\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-manrope\",\"display\":\"swap\"}],\"variableName\":\"manrope\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\okdoi\\src\\app\\globals.css":{"id":"(app-pages-browser)/./src/app/globals.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\okdoi\\src\\components\\providers\\QueryProvider.tsx":{"id":"(app-pages-browser)/./src/components/providers/QueryProvider.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\okdoi\\src\\components\\ui\\ConfirmationDialog.tsx":{"id":"(app-pages-browser)/./src/components/ui/ConfirmationDialog.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\okdoi\\src\\components\\ui\\ErrorBoundary.tsx":{"id":"(app-pages-browser)/./src/components/ui/ErrorBoundary.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\okdoi\\src\\contexts\\AlertContext.tsx":{"id":"(app-pages-browser)/./src/contexts/AlertContext.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\okdoi\\src\\contexts\\AuthContext.tsx":{"id":"(app-pages-browser)/./src/contexts/AuthContext.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\okdoi\\src\\contexts\\CartContext.tsx":{"id":"(app-pages-browser)/./src/contexts/CartContext.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\okdoi\\src\\lib\\performanceOptimizations.ts":{"id":"(app-pages-browser)/./src/lib/performanceOptimizations.ts","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\okdoi\\node_modules\\next\\dist\\client\\components\\app-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\okdoi\\node_modules\\next\\dist\\esm\\client\\components\\app-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\okdoi\\node_modules\\next\\dist\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\okdoi\\node_modules\\next\\dist\\esm\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\okdoi\\node_modules\\next\\dist\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\okdoi\\node_modules\\next\\dist\\esm\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\okdoi\\node_modules\\next\\dist\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\okdoi\\node_modules\\next\\dist\\esm\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\okdoi\\node_modules\\next\\dist\\client\\components\\not-found-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\okdoi\\node_modules\\next\\dist\\esm\\client\\components\\not-found-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\okdoi\\node_modules\\next\\dist\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\okdoi\\node_modules\\next\\dist\\esm\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\okdoi\\src\\app\\admin\\page.tsx":{"id":"(app-pages-browser)/./src/app/admin/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\okdoi\\src\\app\\admin\\users\\page.tsx":{"id":"(app-pages-browser)/./src/app/admin/users/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\okdoi\\src\\app\\dashboard\\wallet\\page.tsx":{"id":"(app-pages-browser)/./src/app/dashboard/wallet/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\okdoi\\src\\components\\auth\\GuestGuard.tsx":{"id":"(app-pages-browser)/./src/components/auth/GuestGuard.tsx","name":"*","chunks":["app/auth/signin/page","static/chunks/app/auth/signin/page.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\okdoi\\src\\components\\auth\\NewSignInForm.tsx":{"id":"(app-pages-browser)/./src/components/auth/NewSignInForm.tsx","name":"*","chunks":["app/auth/signin/page","static/chunks/app/auth/signin/page.js"],"async":false},"C:\\Users\\<USER>\\Desktop\\okdoi\\src\\app\\admin\\company-wallet\\page.tsx":{"id":"(app-pages-browser)/./src/app/admin/company-wallet/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\okdoi\\src\\app\\admin\\commission-reports\\page.tsx":{"id":"(app-pages-browser)/./src/app/admin/commission-reports/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\okdoi\\src\\app\\dashboard\\page.tsx":{"id":"(app-pages-browser)/./src/app/dashboard/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\okdoi\\src\\app\\admin\\deposits\\page.tsx":{"id":"(app-pages-browser)/./src/app/admin/deposits/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\okdoi\\src\\components\\auth\\NewSignUpContent.tsx":{"id":"(app-pages-browser)/./src/components/auth/NewSignUpContent.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\okdoi\\src\\app\\dashboard\\subscription\\page.tsx":{"id":"(app-pages-browser)/./src/app/dashboard/subscription/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\okdoi\\src\\app\\admin\\analytics\\page.tsx":{"id":"(app-pages-browser)/./src/app/admin/analytics/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\okdoi\\src\\app\\dashboard\\settings\\page.tsx":{"id":"(app-pages-browser)/./src/app/dashboard/settings/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\okdoi\\src\\app\\admin\\commission-structure\\page.tsx":{"id":"(app-pages-browser)/./src/app/admin/commission-structure/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\okdoi\\src\\app\\dashboard\\referrals\\page.tsx":{"id":"(app-pages-browser)/./src/app/dashboard/referrals/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\okdoi\\src\\app\\auth\\verify-otp\\page.tsx":{"id":"(app-pages-browser)/./src/app/auth/verify-otp/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\okdoi\\src\\app\\dashboard\\profile\\page.tsx":{"id":"(app-pages-browser)/./src/app/dashboard/profile/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\Desktop\\okdoi\\src\\app\\admin\\referrals\\page.tsx":{"id":"(app-pages-browser)/./src/app/admin/referrals/page.tsx","name":"*","chunks":[],"async":false}},"entryCSSFiles":{"C:\\Users\\<USER>\\Desktop\\okdoi\\src\\":[],"C:\\Users\\<USER>\\Desktop\\okdoi\\src\\app\\page":["static/css/app/page.css"],"C:\\Users\\<USER>\\Desktop\\okdoi\\src\\app\\layout":["static/css/app/layout.css"],"C:\\Users\\<USER>\\Desktop\\okdoi\\src\\app\\auth\\signin\\page":[]}}