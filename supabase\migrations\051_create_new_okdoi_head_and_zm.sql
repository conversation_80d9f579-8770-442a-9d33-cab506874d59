-- Create New OKDOI Head and ZM Accounts
-- Based on Commission_Distribution_Explained.md requirements
-- Date: 2025-01-09

-- =====================================================
-- 1. REMOVE EXISTING OKDOI HEAD
-- =====================================================

-- First, backup existing OKDOI Head data (if any)
CREATE TABLE IF NOT EXISTS okdoi_head_backup AS
SELECT * FROM users WHERE user_type = 'okdoi_head';

-- Remove existing OKDOI Head user (this will cascade to related records)
DELETE FROM users WHERE user_type = 'okdoi_head';

-- =====================================================
-- 2. CREATE NEW OKDOI HEAD ACCOUNT
-- =====================================================

-- Create the new OKDOI Head account
-- Email: <EMAIL>
-- Password: Company432OK

DO $$
DECLARE
    new_okdoi_head_id UUID;
    new_okdoi_head_wallet_id UUID;
    okdoi_head_referral_code VARCHAR(20);
BEGIN
    -- Generate unique referral code for OKDOI Head
    okdoi_head_referral_code := 'OKDOI' || LPAD(FLOOR(RANDOM() * 10000)::TEXT, 4, '0');
    
    -- Ensure referral code is unique
    WHILE EXISTS (SELECT 1 FROM users WHERE referral_code = okdoi_head_referral_code) LOOP
        okdoi_head_referral_code := 'OKDOI' || LPAD(FLOOR(RANDOM() * 10000)::TEXT, 4, '0');
    END LOOP;
    
    -- Generate a UUID for the new OKDOI Head
    new_okdoi_head_id := gen_random_uuid();
    
    -- Insert new OKDOI Head user
    INSERT INTO users (
        id, email, full_name, user_type, role, is_verified, is_referral_active,
        referral_level, referral_path, direct_referrals_count, total_downline_count,
        total_commission_earned, referral_code, created_at, updated_at
    ) VALUES (
        new_okdoi_head_id,
        '<EMAIL>',
        'OKDOI Company Head',
        'okdoi_head',
        'admin',
        true,
        true,
        0,
        '',
        0,
        0,
        0,
        okdoi_head_referral_code,
        NOW(),
        NOW()
    );
    
    -- Create wallet for OKDOI Head
    INSERT INTO user_wallets (
        user_id, balance, created_at, updated_at
    ) VALUES (
        new_okdoi_head_id, 0.00, NOW(), NOW()
    ) RETURNING id INTO new_okdoi_head_wallet_id;
    
    -- Log the creation
    RAISE NOTICE 'Created new OKDOI Head with ID: %, Referral Code: %', new_okdoi_head_id, okdoi_head_referral_code;
END $$;

-- =====================================================
-- 3. CREATE NEW ZM ACCOUNT UNDER OKDOI HEAD
-- =====================================================

-- Create the new ZM account
-- Email: <EMAIL>
-- Password: ZmOkdoi455ER

DO $$
DECLARE
    okdoi_head_id UUID;
    new_zm_id UUID;
    new_zm_wallet_id UUID;
    zm_referral_code VARCHAR(20);
BEGIN
    -- Get the OKDOI Head ID
    SELECT id INTO okdoi_head_id FROM users WHERE user_type = 'okdoi_head' LIMIT 1;
    
    IF okdoi_head_id IS NULL THEN
        RAISE EXCEPTION 'OKDOI Head not found. Please create OKDOI Head first.';
    END IF;
    
    -- Generate unique referral code for ZM
    zm_referral_code := 'ZM' || LPAD(FLOOR(RANDOM() * 100000)::TEXT, 5, '0');
    
    -- Ensure referral code is unique
    WHILE EXISTS (SELECT 1 FROM users WHERE referral_code = zm_referral_code) LOOP
        zm_referral_code := 'ZM' || LPAD(FLOOR(RANDOM() * 100000)::TEXT, 5, '0');
    END LOOP;
    
    -- Generate a UUID for the new ZM
    new_zm_id := gen_random_uuid();
    
    -- Insert new ZM user
    INSERT INTO users (
        id, email, full_name, user_type, role, is_verified, is_referral_active,
        referred_by_id, referral_level, referral_path, direct_referrals_count, 
        total_downline_count, total_commission_earned, referral_code, created_at, updated_at
    ) VALUES (
        new_zm_id,
        '<EMAIL>',
        'OKDOI Default Zonal Manager',
        'zonal_manager',
        'user',
        true,
        true,
        okdoi_head_id,
        1,
        okdoi_head_id::TEXT,
        0,
        0,
        0,
        zm_referral_code,
        NOW(),
        NOW()
    );
    
    -- Create wallet for ZM
    INSERT INTO user_wallets (
        user_id, balance, created_at, updated_at
    ) VALUES (
        new_zm_id, 0.00, NOW(), NOW()
    ) RETURNING id INTO new_zm_wallet_id;
    
    -- Create referral hierarchy entry
    INSERT INTO referral_hierarchy (
        user_id, ancestor_id, level_difference, created_at
    ) VALUES (
        new_zm_id, okdoi_head_id, 1, NOW()
    );
    
    -- Create referral placement entry
    INSERT INTO referral_placements (
        parent_id, child_id, position, placement_type, created_at
    ) VALUES (
        okdoi_head_id, new_zm_id, 1, 'direct', NOW()
    );
    
    -- Update OKDOI Head's referral counts
    UPDATE users 
    SET direct_referrals_count = direct_referrals_count + 1,
        total_downline_count = total_downline_count + 1,
        updated_at = NOW()
    WHERE id = okdoi_head_id;
    
    -- Create zonal manager entry
    INSERT INTO zonal_managers (
        user_id, zone_name, zone_description, territories, is_active, created_at, updated_at
    ) VALUES (
        new_zm_id,
        'Default Zone',
        'Default zone for direct registrations without referral links',
        ARRAY['all_districts'],
        true,
        NOW(),
        NOW()
    );
    
    -- Log the creation
    RAISE NOTICE 'Created new ZM with ID: %, Referral Code: %, under OKDOI Head: %', new_zm_id, zm_referral_code, okdoi_head_id;
END $$;

-- =====================================================
-- 4. UPDATE SYSTEM SETTINGS FOR DEFAULT ZM
-- =====================================================

-- Create or update admin setting for default ZM
INSERT INTO admin_settings (setting_key, setting_value, description, created_at, updated_at)
VALUES (
    'default_zm_for_direct_registrations',
    (SELECT id::TEXT FROM users WHERE email = '<EMAIL>'),
    'Default ZM account for users who register without referral links',
    NOW(),
    NOW()
) ON CONFLICT (setting_key) DO UPDATE SET
    setting_value = EXCLUDED.setting_value,
    updated_at = NOW();

-- =====================================================
-- 5. CREATE FUNCTION TO ASSIGN DIRECT USERS TO DEFAULT ZM
-- =====================================================

CREATE OR REPLACE FUNCTION assign_direct_users_to_default_zm()
RETURNS INTEGER AS $$
DECLARE
    default_zm_id UUID;
    okdoi_head_id UUID;
    direct_user RECORD;
    users_migrated INTEGER := 0;
BEGIN
    -- Get the default ZM and OKDOI Head IDs
    SELECT id INTO default_zm_id FROM users WHERE email = '<EMAIL>';
    SELECT id INTO okdoi_head_id FROM users WHERE user_type = 'okdoi_head' LIMIT 1;
    
    IF default_zm_id IS NULL THEN
        RAISE EXCEPTION 'Default ZM not found';
    END IF;
    
    IF okdoi_head_id IS NULL THEN
        RAISE EXCEPTION 'OKDOI Head not found';
    END IF;
    
    -- Find all users who registered directly (no referrer)
    FOR direct_user IN
        SELECT id, email, full_name 
        FROM users 
        WHERE referred_by_id IS NULL 
        AND user_type = 'user'
        AND id != okdoi_head_id
        AND id != default_zm_id
    LOOP
        -- Update user to be referred by default ZM
        UPDATE users 
        SET referred_by_id = default_zm_id,
            referral_level = 2,
            referral_path = okdoi_head_id::TEXT || ',' || default_zm_id::TEXT,
            updated_at = NOW()
        WHERE id = direct_user.id;
        
        -- Create referral hierarchy entries
        INSERT INTO referral_hierarchy (user_id, ancestor_id, level_difference, created_at)
        VALUES 
            (direct_user.id, default_zm_id, 1, NOW()),
            (direct_user.id, okdoi_head_id, 2, NOW())
        ON CONFLICT (user_id, ancestor_id) DO NOTHING;
        
        -- Find next available position under default ZM
        INSERT INTO referral_placements (parent_id, child_id, position, placement_type, created_at)
        SELECT default_zm_id, direct_user.id, 
               COALESCE((SELECT MAX(position) FROM referral_placements WHERE parent_id = default_zm_id), 0) + 1,
               'direct', NOW()
        ON CONFLICT (parent_id, child_id) DO NOTHING;
        
        users_migrated := users_migrated + 1;
    END LOOP;
    
    -- Update ZM's referral counts
    UPDATE users 
    SET direct_referrals_count = (
            SELECT COUNT(*) FROM referral_placements 
            WHERE parent_id = default_zm_id AND placement_type = 'direct'
        ),
        total_downline_count = (
            SELECT COUNT(*) FROM referral_hierarchy 
            WHERE ancestor_id = default_zm_id
        ),
        updated_at = NOW()
    WHERE id = default_zm_id;
    
    -- Update OKDOI Head's downline count
    UPDATE users 
    SET total_downline_count = (
            SELECT COUNT(*) FROM referral_hierarchy 
            WHERE ancestor_id = okdoi_head_id
        ),
        updated_at = NOW()
    WHERE id = okdoi_head_id;
    
    RETURN users_migrated;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- 6. EXECUTE THE MIGRATION
-- =====================================================

-- Run the migration to assign existing direct users to the new ZM
SELECT assign_direct_users_to_default_zm() as users_migrated;

-- =====================================================
-- 7. CREATE TRIGGER FOR FUTURE DIRECT REGISTRATIONS
-- =====================================================

CREATE OR REPLACE FUNCTION auto_assign_direct_users_to_zm()
RETURNS TRIGGER AS $$
DECLARE
    default_zm_id UUID;
    okdoi_head_id UUID;
BEGIN
    -- Only process if this is a new user without a referrer
    IF NEW.referred_by_id IS NULL AND NEW.user_type = 'user' THEN
        -- Get the default ZM ID
        SELECT setting_value::UUID INTO default_zm_id 
        FROM admin_settings 
        WHERE setting_key = 'default_zm_for_direct_registrations';
        
        SELECT id INTO okdoi_head_id FROM users WHERE user_type = 'okdoi_head' LIMIT 1;
        
        IF default_zm_id IS NOT NULL AND okdoi_head_id IS NOT NULL THEN
            -- Update the new user to be under default ZM
            NEW.referred_by_id := default_zm_id;
            NEW.referral_level := 2;
            NEW.referral_path := okdoi_head_id::TEXT || ',' || default_zm_id::TEXT;
        END IF;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create the trigger
DROP TRIGGER IF EXISTS auto_assign_direct_users_trigger ON users;
CREATE TRIGGER auto_assign_direct_users_trigger
    BEFORE INSERT ON users
    FOR EACH ROW
    EXECUTE FUNCTION auto_assign_direct_users_to_zm();

-- Add comment
COMMENT ON FUNCTION assign_direct_users_to_default_zm() IS 
'Migrates existing direct registered users to the default ZM account';

COMMENT ON FUNCTION auto_assign_direct_users_to_zm() IS 
'Automatically assigns new direct registrations to the default ZM account';
