"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/wallet/page",{

/***/ "(app-pages-browser)/./src/lib/services/commissionSystem.ts":
/*!**********************************************!*\
  !*** ./src/lib/services/commissionSystem.ts ***!
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CommissionSystemService: function() { return /* binding */ CommissionSystemService; }\n/* harmony export */ });\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/supabase */ \"(app-pages-browser)/./src/lib/supabase.ts\");\n\n/**\n * CommissionSystemService - Handles commission calculations and distributions\n */ class CommissionSystemService {\n    /**\n   * Get commission structure for a package value\n   */ static async getCommissionStructure(packageValue) {\n        try {\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.COMMISSION_STRUCTURE).select(\"*\").lte(\"package_value\", packageValue).eq(\"is_active\", true).order(\"package_value\", {\n                ascending: false\n            });\n            if (error) {\n                throw new Error(\"Failed to get commission structure: \".concat(error.message));\n            }\n            return data || [];\n        } catch (error) {\n            console.error(\"Error getting commission structure:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Get all commission structures (for admin)\n   */ static async getAllCommissionStructures() {\n        try {\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.COMMISSION_STRUCTURE).select(\"*\").order(\"package_value\", {\n                ascending: true\n            });\n            if (error) {\n                throw new Error(\"Failed to get all commission structures: \".concat(error.message));\n            }\n            return data || [];\n        } catch (error) {\n            console.error(\"Error getting all commission structures:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Create new commission structure\n   */ static async createCommissionStructure(structure) {\n        try {\n            const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.COMMISSION_STRUCTURE).insert(structure).select().single();\n            if (error) {\n                throw new Error(\"Failed to create commission structure: \".concat(error.message));\n            }\n            return data;\n        } catch (error) {\n            console.error(\"Error creating commission structure:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Calculate and distribute commissions for a subscription purchase\n   * NEW: Handles leftover commissions by sending them to company wallet\n   */ static async distributeCommissions(purchaserId, subscriptionId, packageAmount) {\n        try {\n            if (!_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin) {\n                throw new Error(\"Admin client not available\");\n            }\n            console.log(\"Starting commission distribution for subscription \".concat(subscriptionId, \", package amount: Rs \").concat(packageAmount));\n            // Get commission distributions using the new absolute function\n            const { data: distributions, error: distributionError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.rpc(\"calculate_commission_distribution_absolute\", {\n                p_purchaser_id: purchaserId,\n                p_subscription_id: subscriptionId,\n                p_package_price: packageAmount\n            });\n            if (distributionError) {\n                throw new Error(\"Failed to calculate commission distributions: \".concat(distributionError.message));\n            }\n            if (!distributions || distributions.length === 0) {\n                console.warn(\"No commission distributions calculated\");\n                return {\n                    totalDistributed: 0,\n                    transactionsCreated: 0,\n                    unallocatedAmount: 0,\n                    distributionDetails: []\n                };\n            }\n            let totalDistributed = 0;\n            let distributionCount = 0;\n            let companyLeftoverAmount = 0;\n            // Process each commission distribution\n            for (const distribution of distributions){\n                var _distribution_metadata;\n                // Handle company leftover allocation separately\n                if (distribution.beneficiary_id === null && distribution.commission_type === \"company_leftover_allocation\") {\n                    var _distribution_metadata1;\n                    // ✅ Use new function to properly update company wallet balance\n                    const { error: companyWalletError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.rpc(\"update_company_wallet_balance\", {\n                        p_amount: distribution.commission_amount,\n                        p_transaction_type: \"company_profit\",\n                        p_description: \"Leftover commission allocation from network distribution (\".concat(((_distribution_metadata1 = distribution.metadata) === null || _distribution_metadata1 === void 0 ? void 0 : _distribution_metadata1.missing_positions) || 0, \" missing positions)\"),\n                        p_subscription_id: subscriptionId,\n                        p_metadata: {\n                            ...distribution.metadata,\n                            subscription_id: subscriptionId,\n                            package_price: packageAmount\n                        }\n                    });\n                    if (companyWalletError) {\n                        console.error(\"Failed to credit company wallet with leftover:\", companyWalletError);\n                        throw new Error(\"Failed to credit company wallet: \".concat(companyWalletError.message));\n                    }\n                    companyLeftoverAmount += parseFloat(distribution.commission_amount.toString());\n                    console.log(\"Credited Rs \".concat(distribution.commission_amount, \" leftover to company wallet\"));\n                    continue; // Skip normal commission processing for company allocations\n                }\n                const transactionId = \"comm_\".concat(Date.now(), \"_\").concat(Math.random().toString(36).substr(2, 9));\n                // Insert commission transaction (let id auto-generate as UUID)\n                const { error: commissionError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from(\"commission_transactions\").insert({\n                    transaction_id: transactionId,\n                    user_id: distribution.beneficiary_id,\n                    beneficiary_id: distribution.beneficiary_id,\n                    subscription_purchase_id: subscriptionId,\n                    commission_type: distribution.commission_type,\n                    commission_level: distribution.level_position,\n                    package_value: packageAmount,\n                    commission_rate: 0,\n                    commission_amount: distribution.commission_amount,\n                    status: \"processed\",\n                    metadata: distribution.metadata\n                });\n                if (commissionError) {\n                    console.error(\"Failed to insert commission transaction:\", commissionError);\n                    throw new Error(\"Failed to insert commission transaction: \".concat(commissionError.message));\n                }\n                // Get user's wallet ID first\n                const { data: userWallet, error: walletFetchError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from(\"user_wallets\").select(\"id\").eq(\"user_id\", distribution.beneficiary_id).single();\n                if (walletFetchError || !userWallet) {\n                    console.error(\"Failed to get wallet for user \".concat(distribution.beneficiary_id, \":\"), walletFetchError);\n                    throw new Error(\"Failed to get wallet for user \".concat(distribution.beneficiary_id, \": \").concat((walletFetchError === null || walletFetchError === void 0 ? void 0 : walletFetchError.message) || \"Wallet not found\"));\n                }\n                // ✅ CORRECTED: Check if this is a gift system commission\n                const isGiftSystemCommission = ((_distribution_metadata = distribution.metadata) === null || _distribution_metadata === void 0 ? void 0 : _distribution_metadata.gift_system) === true || [\n                    \"present_user\",\n                    \"annual_present_user\",\n                    \"present_leader\",\n                    \"annual_present_leader\"\n                ].includes(distribution.commission_type);\n                // Only update wallet balance for NON-gift system commissions\n                if (!isGiftSystemCommission) {\n                    // Update user's wallet balance using correct wallet_id\n                    const { error: walletError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.rpc(\"update_wallet_balance\", {\n                        p_wallet_id: userWallet.id,\n                        p_amount: distribution.commission_amount,\n                        p_transaction_type: \"deposit\",\n                        p_description: \"\".concat(distribution.commission_type, \" commission from subscription\"),\n                        p_reference_id: subscriptionId,\n                        p_reference_type: \"subscription\",\n                        p_metadata: {\n                            commission_type: distribution.commission_type,\n                            commission_level: distribution.level_position,\n                            subscription_id: subscriptionId\n                        }\n                    });\n                    if (walletError) {\n                        console.error(\"Failed to update wallet balance:\", walletError);\n                        throw new Error(\"Failed to update wallet balance: \".concat(walletError.message));\n                    }\n                    console.log(\"✅ Added Rs \".concat(distribution.commission_amount, \" to wallet for \").concat(distribution.commission_type));\n                } else {\n                    console.log(\"⚠️ Skipped wallet update for gift system commission: \".concat(distribution.commission_type, \" (Rs \").concat(distribution.commission_amount, \")\"));\n                }\n                totalDistributed += parseFloat(distribution.commission_amount.toString());\n                distributionCount++;\n            }\n            // Process gift system commissions\n            try {\n                await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.rpc(\"distribute_gift_system_commissions\", {\n                    p_purchaser_id: purchaserId,\n                    p_subscription_id: subscriptionId,\n                    p_package_price: packageAmount\n                });\n                console.log(\"Gift system commissions distributed successfully\");\n            } catch (giftError) {\n                console.error(\"Failed to distribute gift system commissions:\", giftError);\n            // Don't fail the main distribution, but log the error\n            }\n            // Credit company profit to company wallet\n            const { data: packageStructure } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from(\"commission_structure\").select(\"company_wallet_amount\").eq(\"package_value\", packageAmount).eq(\"commission_type\", \"unified_structure\").single();\n            if (packageStructure === null || packageStructure === void 0 ? void 0 : packageStructure.company_wallet_amount) {\n                // ✅ Use new function to properly update company wallet balance\n                const { error: companyProfitError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.rpc(\"update_company_wallet_balance\", {\n                    p_amount: packageStructure.company_wallet_amount,\n                    p_transaction_type: \"company_profit\",\n                    p_description: \"Company profit from subscription package Rs. \".concat(packageAmount),\n                    p_subscription_id: subscriptionId,\n                    p_metadata: {\n                        package_price: packageAmount,\n                        subscription_id: subscriptionId\n                    }\n                });\n                if (companyProfitError) {\n                    console.error(\"Failed to credit company profit:\", companyProfitError);\n                    throw new Error(\"Failed to credit company profit: \".concat(companyProfitError.message));\n                }\n            }\n            console.log(\"Commission distribution completed: Rs \".concat(totalDistributed, \" to \").concat(distributionCount, \" recipients, Rs \").concat(companyLeftoverAmount, \" leftover to company\"));\n            return {\n                totalDistributed,\n                transactionsCreated: distributionCount,\n                unallocatedAmount: companyLeftoverAmount,\n                distributionDetails: distributions.filter((d)=>d.beneficiary_id !== null).map((d)=>({\n                        level: d.level_position,\n                        beneficiaryId: d.beneficiary_id,\n                        amount: parseFloat(d.commission_amount.toString()),\n                        rate: 0 // Not applicable for absolute amounts\n                    }))\n            };\n        } catch (error) {\n            console.error(\"Error distributing commissions:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Process pending commission transactions and credit user wallets\n   */ static async processPendingCommissions() {\n        try {\n            if (!_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin) {\n                throw new Error(\"Admin client not available\");\n            }\n            // Get all pending commission transactions\n            const { data: pendingCommissions, error: fetchError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.COMMISSION_TRANSACTIONS).select(\"*\").eq(\"status\", \"pending\").order(\"created_at\", {\n                ascending: true\n            });\n            if (fetchError) {\n                throw new Error(\"Failed to fetch pending commissions: \".concat(fetchError.message));\n            }\n            if (!pendingCommissions || pendingCommissions.length === 0) {\n                return {\n                    processed: 0,\n                    totalAmount: 0,\n                    errors: []\n                };\n            }\n            let processedCount = 0;\n            let totalAmount = 0;\n            const errors = [];\n            for (const commission of pendingCommissions){\n                try {\n                    // Get beneficiary's wallet\n                    const { data: wallet, error: walletError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from(\"user_wallets\").select(\"id, balance\").eq(\"user_id\", commission.beneficiary_id).single();\n                    if (walletError || !wallet) {\n                        errors.push(\"No wallet found for beneficiary \".concat(commission.beneficiary_id));\n                        continue;\n                    }\n                    // Create wallet transaction\n                    const { data: walletTransaction, error: walletTransError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from(\"wallet_transactions\").insert({\n                        wallet_id: wallet.id,\n                        transaction_type: \"commission\",\n                        amount: commission.commission_amount,\n                        balance_before: wallet.balance,\n                        balance_after: parseFloat(wallet.balance) + parseFloat(commission.commission_amount),\n                        description: \"Level \".concat(commission.commission_level, \" commission from subscription\"),\n                        reference_id: commission.subscription_purchase_id,\n                        reference_type: \"subscription\",\n                        status: \"completed\",\n                        metadata: {\n                            commission_type: commission.commission_type,\n                            commission_level: commission.commission_level,\n                            original_transaction_id: commission.transaction_id\n                        }\n                    }).select().single();\n                    if (walletTransError) {\n                        errors.push(\"Failed to create wallet transaction for \".concat(commission.beneficiary_id, \": \").concat(walletTransError.message));\n                        continue;\n                    }\n                    // Update wallet balance\n                    const { error: balanceError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from(\"user_wallets\").update({\n                        balance: parseFloat(wallet.balance) + parseFloat(commission.commission_amount),\n                        updated_at: new Date().toISOString()\n                    }).eq(\"id\", wallet.id);\n                    if (balanceError) {\n                        errors.push(\"Failed to update wallet balance for \".concat(commission.beneficiary_id, \": \").concat(balanceError.message));\n                        continue;\n                    }\n                    // Update commission transaction status\n                    const { error: commissionUpdateError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabaseAdmin.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.COMMISSION_TRANSACTIONS).update({\n                        status: \"processed\",\n                        processed_at: new Date().toISOString(),\n                        wallet_transaction_id: walletTransaction.id\n                    }).eq(\"id\", commission.id);\n                    if (commissionUpdateError) {\n                        errors.push(\"Failed to update commission status for \".concat(commission.id, \": \").concat(commissionUpdateError.message));\n                        continue;\n                    }\n                    processedCount++;\n                    totalAmount += parseFloat(commission.commission_amount);\n                } catch (error) {\n                    errors.push(\"Error processing commission \".concat(commission.id, \": \").concat(error instanceof Error ? error.message : \"Unknown error\"));\n                }\n            }\n            return {\n                processed: processedCount,\n                totalAmount,\n                errors\n            };\n        } catch (error) {\n            console.error(\"Error processing pending commissions:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Get commission summary for admin dashboard\n   */ static async getCommissionSummary() {\n        try {\n            var _totalPaidResult_data, _pendingResult_data, _topEarnersResult_data;\n            const [totalPaidResult, pendingResult, failedResult, totalTransactionsResult, topEarnersResult] = await Promise.all([\n                _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.COMMISSION_TRANSACTIONS).select(\"commission_amount\").eq(\"status\", \"processed\"),\n                _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.COMMISSION_TRANSACTIONS).select(\"commission_amount\").eq(\"status\", \"pending\"),\n                _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.COMMISSION_TRANSACTIONS).select(\"*\", {\n                    count: \"exact\",\n                    head: true\n                }).eq(\"status\", \"failed\"),\n                _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.COMMISSION_TRANSACTIONS).select(\"*\", {\n                    count: \"exact\",\n                    head: true\n                }),\n                _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.USERS).select(\"id, full_name, total_commission_earned\").gt(\"total_commission_earned\", 0).order(\"total_commission_earned\", {\n                    ascending: false\n                }).limit(10)\n            ]);\n            const totalCommissionsPaid = ((_totalPaidResult_data = totalPaidResult.data) === null || _totalPaidResult_data === void 0 ? void 0 : _totalPaidResult_data.reduce((sum, t)=>sum + t.commission_amount, 0)) || 0;\n            const pendingCommissions = ((_pendingResult_data = pendingResult.data) === null || _pendingResult_data === void 0 ? void 0 : _pendingResult_data.reduce((sum, t)=>sum + t.commission_amount, 0)) || 0;\n            const failedCommissions = failedResult.count || 0;\n            const totalTransactions = totalTransactionsResult.count || 0;\n            const topEarners = ((_topEarnersResult_data = topEarnersResult.data) === null || _topEarnersResult_data === void 0 ? void 0 : _topEarnersResult_data.map((user)=>({\n                    userId: user.id,\n                    fullName: user.full_name || \"Unknown\",\n                    totalEarned: user.total_commission_earned || 0\n                }))) || [];\n            return {\n                totalCommissionsPaid,\n                pendingCommissions,\n                failedCommissions,\n                totalTransactions,\n                topEarners\n            };\n        } catch (error) {\n            console.error(\"Error getting commission summary:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Update commission structure rates\n   */ static async updateCommissionStructure(id, updates) {\n        try {\n            const { error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.COMMISSION_STRUCTURE).update(updates).eq(\"id\", id);\n            if (error) {\n                throw new Error(\"Failed to update commission structure: \".concat(error.message));\n            }\n        } catch (error) {\n            console.error(\"Error updating commission structure:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Get commission transactions for a specific user with filters\n   * Excludes gift system commissions from user wallet view\n   */ static async getUserCommissionTransactions(userId) {\n        let filters = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {}, page = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 1, limit = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : 20;\n        try {\n            const offset = (page - 1) * limit;\n            let query = _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.COMMISSION_TRANSACTIONS).select(\"*\").eq(\"beneficiary_id\", userId)// Exclude gift system commissions from user wallet view\n            .not(\"commission_type\", \"in\", \"(present_user,annual_present_user,present_leader,annual_present_leader)\");\n            // Apply filters\n            if (filters.status) {\n                query = query.eq(\"status\", filters.status);\n            }\n            if (filters.commissionType) {\n                query = query.eq(\"commission_type\", filters.commissionType);\n            }\n            if (filters.dateFrom) {\n                query = query.gte(\"created_at\", filters.dateFrom);\n            }\n            if (filters.dateTo) {\n                query = query.lte(\"created_at\", filters.dateTo);\n            }\n            const [dataResult, countResult] = await Promise.all([\n                query.order(\"created_at\", {\n                    ascending: false\n                }).range(offset, offset + limit - 1),\n                query.select(\"*\", {\n                    count: \"exact\",\n                    head: true\n                })\n            ]);\n            if (dataResult.error) {\n                throw new Error(\"Failed to get commission transactions: \".concat(dataResult.error.message));\n            }\n            if (countResult.error) {\n                throw new Error(\"Failed to count commission transactions: \".concat(countResult.error.message));\n            }\n            return {\n                transactions: dataResult.data || [],\n                total: countResult.count || 0\n            };\n        } catch (error) {\n            console.error(\"Error getting user commission transactions:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Get ALL commission transactions for a specific user (including gift system commissions)\n   * This method is for admin use only\n   */ static async getAllUserCommissionTransactions(userId) {\n        let filters = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {}, page = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 1, limit = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : 20;\n        try {\n            const offset = (page - 1) * limit;\n            let query = _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.COMMISSION_TRANSACTIONS).select(\"*\").eq(\"beneficiary_id\", userId);\n            // Include ALL commission types (including gift system commissions)\n            // Apply filters\n            if (filters.status) {\n                query = query.eq(\"status\", filters.status);\n            }\n            if (filters.commissionType) {\n                query = query.eq(\"commission_type\", filters.commissionType);\n            }\n            if (filters.dateFrom) {\n                query = query.gte(\"created_at\", filters.dateFrom);\n            }\n            if (filters.dateTo) {\n                query = query.lte(\"created_at\", filters.dateTo);\n            }\n            const [dataResult, countResult] = await Promise.all([\n                query.order(\"created_at\", {\n                    ascending: false\n                }).range(offset, offset + limit - 1),\n                query.select(\"*\", {\n                    count: \"exact\",\n                    head: true\n                })\n            ]);\n            if (dataResult.error) {\n                throw new Error(\"Failed to get all commission transactions: \".concat(dataResult.error.message));\n            }\n            if (countResult.error) {\n                throw new Error(\"Failed to count all commission transactions: \".concat(countResult.error.message));\n            }\n            return {\n                transactions: dataResult.data || [],\n                total: countResult.count || 0\n            };\n        } catch (error) {\n            console.error(\"Error getting all user commission transactions:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Get detailed commission analytics for admin reports\n   */ static async getCommissionAnalytics() {\n        let filters = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n        try {\n            let query = _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.COMMISSION_TRANSACTIONS).select(\"*\");\n            // Apply filters\n            if (filters.status) {\n                query = query.eq(\"status\", filters.status);\n            }\n            if (filters.commissionType) {\n                query = query.eq(\"commission_type\", filters.commissionType);\n            }\n            if (filters.level) {\n                query = query.eq(\"commission_level\", parseInt(filters.level));\n            }\n            if (filters.dateFrom) {\n                query = query.gte(\"created_at\", filters.dateFrom);\n            }\n            if (filters.dateTo) {\n                query = query.lte(\"created_at\", filters.dateTo);\n            }\n            const [dataResult, countResult] = await Promise.all([\n                query.order(\"created_at\", {\n                    ascending: false\n                }).limit(1000),\n                query.select(\"*\", {\n                    count: \"exact\",\n                    head: true\n                })\n            ]);\n            if (dataResult.error) {\n                throw new Error(\"Failed to get commission analytics: \".concat(dataResult.error.message));\n            }\n            if (countResult.error) {\n                throw new Error(\"Failed to count commission analytics: \".concat(countResult.error.message));\n            }\n            const transactions = dataResult.data || [];\n            // Calculate monthly data\n            const monthlyMap = new Map();\n            transactions.forEach((t)=>{\n                const month = new Date(t.created_at).toISOString().slice(0, 7) // YYYY-MM\n                ;\n                const existing = monthlyMap.get(month) || {\n                    amount: 0,\n                    count: 0\n                };\n                monthlyMap.set(month, {\n                    amount: existing.amount + t.commission_amount,\n                    count: existing.count + 1\n                });\n            });\n            const monthlyData = Array.from(monthlyMap.entries()).map((param)=>{\n                let [month, data] = param;\n                return {\n                    month,\n                    ...data\n                };\n            }).sort((a, b)=>a.month.localeCompare(b.month));\n            // Calculate level distribution\n            const levelMap = new Map();\n            transactions.forEach((t)=>{\n                const existing = levelMap.get(t.commission_level) || {\n                    amount: 0,\n                    count: 0\n                };\n                levelMap.set(t.commission_level, {\n                    amount: existing.amount + t.commission_amount,\n                    count: existing.count + 1\n                });\n            });\n            const levelDistribution = Array.from(levelMap.entries()).map((param)=>{\n                let [level, data] = param;\n                return {\n                    level,\n                    ...data\n                };\n            }).sort((a, b)=>a.level - b.level);\n            // Calculate type distribution\n            const typeMap = new Map();\n            transactions.forEach((t)=>{\n                const existing = typeMap.get(t.commission_type) || {\n                    amount: 0,\n                    count: 0\n                };\n                typeMap.set(t.commission_type, {\n                    amount: existing.amount + t.commission_amount,\n                    count: existing.count + 1\n                });\n            });\n            const typeDistribution = Array.from(typeMap.entries()).map((param)=>{\n                let [type, data] = param;\n                return {\n                    type,\n                    ...data\n                };\n            });\n            return {\n                transactions,\n                total: countResult.count || 0,\n                monthlyData,\n                levelDistribution,\n                typeDistribution\n            };\n        } catch (error) {\n            console.error(\"Error getting commission analytics:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Get commission transactions with pagination and filters\n   */ static async getCommissionTransactionsWithFilters() {\n        let filters = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {}, page = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 1, limit = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 50;\n        try {\n            const offset = (page - 1) * limit;\n            let query = _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.TABLES.COMMISSION_TRANSACTIONS).select(\"*\");\n            // Apply filters\n            if (filters.status) {\n                query = query.eq(\"status\", filters.status);\n            }\n            if (filters.commissionType) {\n                query = query.eq(\"commission_type\", filters.commissionType);\n            }\n            if (filters.level) {\n                query = query.eq(\"commission_level\", parseInt(filters.level));\n            }\n            if (filters.dateFrom) {\n                query = query.gte(\"created_at\", filters.dateFrom);\n            }\n            if (filters.dateTo) {\n                query = query.lte(\"created_at\", filters.dateTo);\n            }\n            const [dataResult, countResult] = await Promise.all([\n                query.order(\"created_at\", {\n                    ascending: false\n                }).range(offset, offset + limit - 1),\n                query.select(\"*\", {\n                    count: \"exact\",\n                    head: true\n                })\n            ]);\n            if (dataResult.error) {\n                throw new Error(\"Failed to get commission transactions: \".concat(dataResult.error.message));\n            }\n            if (countResult.error) {\n                throw new Error(\"Failed to count commission transactions: \".concat(countResult.error.message));\n            }\n            return {\n                transactions: dataResult.data || [],\n                total: countResult.count || 0\n            };\n        } catch (error) {\n            console.error(\"Error getting commission transactions with filters:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Get commission breakdown by type for a user\n   */ static async getCommissionBreakdown(userId) {\n        try {\n            const response = await fetch(\"/api/commission-breakdown?userId=\".concat(userId));\n            const result = await response.json();\n            if (!response.ok) {\n                throw new Error(result.error || \"Failed to fetch commission breakdown\");\n            }\n            if (!result.success) {\n                throw new Error(result.error || \"API request failed\");\n            }\n            // Map the extended data to the basic breakdown format\n            const data = result.data;\n            const breakdown = {\n                directCommission: data.directCommission || 0,\n                levelCommission: data.levelCommission || 0,\n                rsmBonus: data.rsmBonuses || 0,\n                zmBonus: data.zmBonuses || 0,\n                okdoiHeadCommission: data.okdoiHeadCommission || 0,\n                totalCommissions: data.totalCommissions || 0\n            };\n            return breakdown;\n        } catch (error) {\n            console.error(\"Error getting commission breakdown:\", error);\n            throw error;\n        }\n    }\n    /**\n   * Get extended commission breakdown with all commission types\n   */ static async getExtendedCommissionBreakdown(userId) {\n        try {\n            const response = await fetch(\"/api/commission-breakdown?userId=\".concat(userId));\n            const result = await response.json();\n            if (!response.ok) {\n                throw new Error(result.error || \"Failed to fetch commission breakdown\");\n            }\n            if (!result.success) {\n                throw new Error(result.error || \"API request failed\");\n            }\n            return result.data;\n        } catch (error) {\n            console.error(\"Error getting extended commission breakdown:\", error);\n            throw error;\n        }\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/services/commissionSystem.ts\n"));

/***/ })

});