-- Create function to update order status with tracking information
CREATE OR REPLACE FUNCTION update_order_status(
    p_order_id uuid,
    p_new_status varchar(20),
    p_tracking_number varchar(100) DEFAULT NULL,
    p_tracking_url text DEFAULT NULL,
    p_notes text DEFAULT NULL,
    p_changed_by uuid DEFAULT NULL
)
RETURNS void AS $$
BEGIN
    -- Update the order with new status and tracking information
    UPDATE shop_orders 
    SET 
        status = p_new_status,
        tracking_number = COALESCE(p_tracking_number, tracking_number),
        tracking_url = COALESCE(p_tracking_url, tracking_url),
        seller_notes = CASE 
            WHEN p_notes IS NOT NULL THEN 
                CASE 
                    WHEN seller_notes IS NULL OR seller_notes = '' THEN p_notes
                    ELSE seller_notes || E'\n' || p_notes
                END
            ELSE seller_notes
        END,
        updated_at = now()
    WHERE id = p_order_id;
    
    -- Add entry to status history
    INSERT INTO order_status_history (order_id, status, notes, changed_by)
    VALUES (p_order_id, p_new_status, p_notes, p_changed_by);
    
    -- The trigger will handle updating timestamps and shop stats
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to update product stock when orders are placed
CREATE OR REPLACE FUNCTION update_product_stock(
    p_product_id uuid,
    p_quantity_sold integer
)
RETURNS void AS $$
BEGIN
    -- Decrease stock quantity
    UPDATE shop_products 
    SET 
        stock_quantity = GREATEST(stock_quantity - p_quantity_sold, 0),
        updated_at = now()
    WHERE id = p_product_id;
    
    -- Update status to out_of_stock if stock reaches 0
    UPDATE shop_products 
    SET status = 'out_of_stock'
    WHERE id = p_product_id AND stock_quantity = 0 AND status = 'active';
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
