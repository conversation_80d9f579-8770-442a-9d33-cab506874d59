-- Category-Specific Fields Migration
-- Adds support for storing category-specific field data and draft functionality

-- Add category-specific fields column to ads table
ALTER TABLE ads ADD COLUMN IF NOT EXISTS category_fields JSONB DEFAULT '{}';

-- Add main category column to support the new three-tier system (sell/rent/jobs)
ALTER TABLE ads ADD COLUMN IF NOT EXISTS main_category VARCHAR(20) DEFAULT 'sell';

-- Add additional fields for job postings
ALTER TABLE ads ADD COLUMN IF NOT EXISTS job_type VARCHAR(50);
ALTER TABLE ads ADD COLUMN IF NOT EXISTS salary_range_from DECIMAL(12,2);
ALTER TABLE ads ADD COLUMN IF NOT EXISTS salary_range_to DECIMAL(12,2);
ALTER TABLE ads ADD COLUMN IF NOT EXISTS application_method VARCHAR(20);
ALTER TABLE ads ADD COLUMN IF NOT EXISTS application_deadline DATE;
ALTER TABLE ads ADD COLUMN IF NOT EXISTS employer_name VARCHAR(255);
ALTER TABLE ads ADD COLUMN IF NOT EXISTS employer_website VARCHAR(500);
ALTER TABLE ads ADD COLUMN IF NOT EXISTS employer_logo_url VARCHAR(500);

-- Add additional fields for rental properties
ALTER TABLE ads ADD COLUMN IF NOT EXISTS rental_type VARCHAR(20);
ALTER TABLE ads ADD COLUMN IF NOT EXISTS availability_from DATE;
ALTER TABLE ads ADD COLUMN IF NOT EXISTS availability_to DATE;

-- Create ad_drafts table for saving incomplete ads
CREATE TABLE IF NOT EXISTS ad_drafts (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    draft_data JSONB NOT NULL DEFAULT '{}',
    step_completed INTEGER DEFAULT 1,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_ads_category_fields ON ads USING GIN (category_fields);
CREATE INDEX IF NOT EXISTS idx_ads_main_category ON ads(main_category);
CREATE INDEX IF NOT EXISTS idx_ads_job_type ON ads(job_type);
CREATE INDEX IF NOT EXISTS idx_ads_rental_type ON ads(rental_type);
CREATE INDEX IF NOT EXISTS idx_ad_drafts_user_id ON ad_drafts(user_id);
CREATE INDEX IF NOT EXISTS idx_ad_drafts_updated_at ON ad_drafts(updated_at DESC);

-- Enable Row Level Security for ad_drafts
ALTER TABLE ad_drafts ENABLE ROW LEVEL SECURITY;

-- RLS Policies for ad_drafts
CREATE POLICY "Users can view their own drafts" ON ad_drafts
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can create their own drafts" ON ad_drafts
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own drafts" ON ad_drafts
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own drafts" ON ad_drafts
    FOR DELETE USING (auth.uid() = user_id);

-- Function to update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_ad_draft_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger to automatically update updated_at
CREATE TRIGGER update_ad_drafts_updated_at
    BEFORE UPDATE ON ad_drafts
    FOR EACH ROW
    EXECUTE FUNCTION update_ad_draft_updated_at();

-- Function to clean up old drafts (older than 30 days)
CREATE OR REPLACE FUNCTION cleanup_old_drafts()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM ad_drafts 
    WHERE updated_at < NOW() - INTERVAL '30 days';
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get user's drafts
CREATE OR REPLACE FUNCTION get_user_drafts(user_uuid UUID)
RETURNS TABLE (
    id UUID,
    draft_data JSONB,
    step_completed INTEGER,
    created_at TIMESTAMP WITH TIME ZONE,
    updated_at TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        d.id,
        d.draft_data,
        d.step_completed,
        d.created_at,
        d.updated_at
    FROM ad_drafts d
    WHERE d.user_id = user_uuid
    ORDER BY d.updated_at DESC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to save or update draft
CREATE OR REPLACE FUNCTION save_ad_draft(
    user_uuid UUID,
    draft_uuid UUID DEFAULT NULL,
    data JSONB DEFAULT '{}',
    step INTEGER DEFAULT 1
)
RETURNS UUID AS $$
DECLARE
    result_id UUID;
BEGIN
    IF draft_uuid IS NOT NULL THEN
        -- Update existing draft
        UPDATE ad_drafts 
        SET 
            draft_data = data,
            step_completed = step,
            updated_at = NOW()
        WHERE id = draft_uuid AND user_id = user_uuid
        RETURNING id INTO result_id;
        
        IF result_id IS NULL THEN
            RAISE EXCEPTION 'Draft not found or access denied';
        END IF;
    ELSE
        -- Create new draft
        INSERT INTO ad_drafts (user_id, draft_data, step_completed)
        VALUES (user_uuid, data, step)
        RETURNING id INTO result_id;
    END IF;
    
    RETURN result_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to delete draft
CREATE OR REPLACE FUNCTION delete_ad_draft(user_uuid UUID, draft_uuid UUID)
RETURNS BOOLEAN AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM ad_drafts 
    WHERE id = draft_uuid AND user_id = user_uuid;
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count > 0;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Update the ads table to support the new structure
-- Add check constraints for main_category
ALTER TABLE ads ADD CONSTRAINT check_main_category 
    CHECK (main_category IN ('sell', 'rent', 'jobs'));

-- Add check constraints for job_type
ALTER TABLE ads ADD CONSTRAINT check_job_type 
    CHECK (job_type IS NULL OR job_type IN ('full-time', 'part-time', 'contract', 'internship', 'freelance'));

-- Add check constraints for rental_type
ALTER TABLE ads ADD CONSTRAINT check_rental_type 
    CHECK (rental_type IS NULL OR rental_type IN ('daily', 'monthly', 'yearly'));

-- Add check constraints for application_method
ALTER TABLE ads ADD CONSTRAINT check_application_method 
    CHECK (application_method IS NULL OR application_method IN ('email', 'phone'));

-- Function to search ads with category-specific fields
CREATE OR REPLACE FUNCTION search_ads_with_category_fields(
    search_term TEXT DEFAULT NULL,
    category_filter UUID DEFAULT NULL,
    subcategory_filter UUID DEFAULT NULL,
    main_category_filter VARCHAR(20) DEFAULT NULL,
    location_filter TEXT DEFAULT NULL,
    min_price DECIMAL DEFAULT NULL,
    max_price DECIMAL DEFAULT NULL,
    category_field_filters JSONB DEFAULT '{}'::JSONB,
    limit_count INTEGER DEFAULT 20,
    offset_count INTEGER DEFAULT 0
)
RETURNS TABLE (
    id UUID,
    title VARCHAR,
    description TEXT,
    price DECIMAL,
    main_category VARCHAR,
    category_fields JSONB,
    location VARCHAR,
    created_at TIMESTAMP WITH TIME ZONE,
    user_id UUID,
    category_id UUID,
    subcategory_id UUID,
    status VARCHAR
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        a.id,
        a.title,
        a.description,
        a.price,
        a.main_category,
        a.category_fields,
        a.location,
        a.created_at,
        a.user_id,
        a.category_id,
        a.subcategory_id,
        a.status
    FROM ads a
    WHERE 
        (search_term IS NULL OR 
         a.title ILIKE '%' || search_term || '%' OR 
         a.description ILIKE '%' || search_term || '%')
    AND (category_filter IS NULL OR a.category_id = category_filter)
    AND (subcategory_filter IS NULL OR a.subcategory_id = subcategory_filter)
    AND (main_category_filter IS NULL OR a.main_category = main_category_filter)
    AND (location_filter IS NULL OR a.location ILIKE '%' || location_filter || '%')
    AND (min_price IS NULL OR a.price >= min_price)
    AND (max_price IS NULL OR a.price <= max_price)
    AND (category_field_filters = '{}'::JSONB OR a.category_fields @> category_field_filters)
    AND a.status = 'active'
    ORDER BY a.created_at DESC
    LIMIT limit_count
    OFFSET offset_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
