'use client'

import { useState, useEffect, memo } from 'react'
import Link from 'next/link'
import { ArrowRight } from 'lucide-react'
import AdGrid from '@/components/ads/AdGrid'
import Button from '@/components/ui/Button'
import Card, { CardContent } from '@/components/ui/card'
import { AdWithDetails } from '@/types'
import { AdService } from '@/lib/services/ads'


const FeaturedAds = memo(function FeaturedAds() {
  const [featuredAds, setFeaturedAds] = useState<AdWithDetails[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchFeaturedAds = async () => {
      try {
        setLoading(true)
        const ads = await AdService.getFeaturedAds(8)
        setFeaturedAds(ads)
      } catch (error) {
        console.error('Error fetching featured ads:', error)
        // Fallback to empty array on error
        setFeaturedAds([])
      } finally {
        setLoading(false)
      }
    }

    fetchFeaturedAds()
  }, [])

  if (loading) {
    return (
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
              Featured Listings
            </h2>
            <p className="text-xl text-gray-600">
              Discover the best deals from our community
            </p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {Array.from({ length: 4 }).map((_, index) => (
              <Card key={index} className="animate-pulse">
                <div className="w-full h-48 bg-gray-300 rounded-t-lg"></div>
                <CardContent className="p-4">
                  <div className="h-4 bg-gray-300 rounded w-3/4 mb-2"></div>
                  <div className="h-3 bg-gray-300 rounded w-1/2 mb-4"></div>
                  <div className="h-6 bg-gray-300 rounded w-1/3"></div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>
    )
  }

  return (
    <section className="py-16 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
            Featured Listings
          </h2>
          <p className="text-xl text-gray-600">
            Discover the best deals from our community
          </p>
        </div>

        <AdGrid
          ads={featuredAds}
          loading={loading}
          showFeaturedBadge={true}
          layout="default"
          emptyMessage="No featured ads available at the moment."
        />

        <div className="text-center mt-8">
          <Link href="/ads">
            <Button variant="outline" size="lg">
              View All Listings
              <ArrowRight className="h-4 w-4 ml-2" />
            </Button>
          </Link>
        </div>
      </div>
    </section>
  )
})

export default FeaturedAds
