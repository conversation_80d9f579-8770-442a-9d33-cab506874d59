import { NextRequest, NextResponse } from 'next/server'
import { supabaseAdmin, TABLES } from '@/lib/supabase'

/**
 * Upgrade user to RSM
 * This API route handles the server-side logic that requires admin privileges
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { userId, zonalManagerId, regionName, upgradedBy } = body

    // Validate required fields
    if (!userId) {
      return NextResponse.json(
        { success: false, error: 'User ID is required' },
        { status: 400 }
      )
    }

    if (!zonalManagerId) {
      return NextResponse.json(
        { success: false, error: 'Zonal Manager ID is required' },
        { status: 400 }
      )
    }

    if (!supabaseAdmin) {
      console.error('Supabase admin client not available')
      return NextResponse.json(
        { success: false, error: 'Admin client not configured' },
        { status: 500 }
      )
    }

    // Validate that user exists and is eligible
    const { data: userData, error: userFetchError } = await supabaseAdmin
      .from(TABLES.USERS)
      .select('user_type, referred_by_id')
      .eq('id', userId)
      .single()

    if (userFetchError) {
      console.error('User fetch error:', userFetchError)
      return NextResponse.json(
        { success: false, error: `Failed to get user data: ${userFetchError.message}` },
        { status: 400 }
      )
    }

    if (userData.user_type !== 'user') {
      return NextResponse.json(
        { success: false, error: 'Only regular users can be upgraded to RSM' },
        { status: 400 }
      )
    }

    // Check if user is already an RSM
    const { data: existingRSM, error: rsmCheckError } = await supabaseAdmin
      .from(TABLES.REGIONAL_SALES_MANAGERS)
      .select('id')
      .eq('user_id', userId)
      .eq('is_active', true)
      .single()

    if (rsmCheckError && rsmCheckError.code !== 'PGRST116') { // PGRST116 = no rows returned
      console.error('RSM check error:', rsmCheckError)
      return NextResponse.json(
        { success: false, error: `Failed to check RSM status: ${rsmCheckError.message}` },
        { status: 500 }
      )
    }

    if (existingRSM) {
      return NextResponse.json(
        { success: false, error: 'User is already an RSM' },
        { status: 400 }
      )
    }

    // Validate that the zonal manager exists
    const { data: zmData, error: zmError } = await supabaseAdmin
      .from(TABLES.ZONAL_MANAGERS)
      .select('id, user_id')
      .eq('id', zonalManagerId)
      .single()

    if (zmError) {
      console.error('ZM validation error:', zmError)
      return NextResponse.json(
        { success: false, error: `Failed to validate Zonal Manager: ${zmError.message}` },
        { status: 400 }
      )
    }

    // Check if user is in the ZM's network
    const { data: networkCheck, error: networkError } = await supabaseAdmin
      .from(TABLES.REFERRAL_HIERARCHY)
      .select('user_id')
      .eq('ancestor_id', zmData.user_id)
      .eq('user_id', userId)
      .single()

    if (networkError && networkError.code !== 'PGRST116') {
      console.error('Network check error:', networkError)
      return NextResponse.json(
        { success: false, error: `Failed to verify user is in ZM network: ${networkError.message}` },
        { status: 500 }
      )
    }

    if (!networkCheck) {
      return NextResponse.json(
        { success: false, error: 'User is not in the selected Zonal Manager\'s network' },
        { status: 400 }
      )
    }

    // Start transaction: Update user type and create RSM record
    try {
      // Update user type
      const { error: userError } = await supabaseAdmin
        .from(TABLES.USERS)
        .update({ user_type: 'rsm' })
        .eq('id', userId)

      if (userError) {
        throw new Error(`Failed to update user type: ${userError.message}`)
      }

      // Create RSM record
      const { data: rsmData, error: rsmError } = await supabaseAdmin
        .from(TABLES.REGIONAL_SALES_MANAGERS)
        .insert({
          user_id: userId,
          zonal_manager_id: zonalManagerId,
          region_name: regionName || null,
          upgraded_by: upgradedBy || null,
          is_active: true,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .select()
        .single()

      if (rsmError) {
        // Rollback user type change
        await supabaseAdmin
          .from(TABLES.USERS)
          .update({ user_type: 'user' })
          .eq('id', userId)
        
        throw new Error(`Failed to create RSM record: ${rsmError.message}`)
      }

      return NextResponse.json({
        success: true,
        message: 'User upgraded to RSM successfully',
        data: rsmData
      })

    } catch (transactionError) {
      console.error('Transaction error during RSM upgrade:', transactionError)
      return NextResponse.json(
        { 
          success: false, 
          error: transactionError instanceof Error ? transactionError.message : 'Failed to upgrade user to RSM' 
        },
        { status: 500 }
      )
    }

  } catch (error) {
    console.error('Error in RSM upgrade API:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Internal server error' 
      },
      { status: 500 }
    )
  }
}
