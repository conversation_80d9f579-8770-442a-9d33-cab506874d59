-- Create product reviews table
CREATE TABLE IF NOT EXISTS product_reviews (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    product_id UUID NOT NULL REFERENCES shop_products(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    rating INTEGER NOT NULL CHECK (rating >= 1 AND rating <= 5),
    review_text TEXT,
    is_verified BOOLEAN DEFAULT FALSE,
    helpful_count INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Ensure one review per user per product
    UNIQUE(product_id, user_id)
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_product_reviews_product_id ON product_reviews(product_id);
CREATE INDEX IF NOT EXISTS idx_product_reviews_user_id ON product_reviews(user_id);
CREATE INDEX IF NOT EXISTS idx_product_reviews_rating ON product_reviews(rating);
CREATE INDEX IF NOT EXISTS idx_product_reviews_created_at ON product_reviews(created_at);

-- Create review helpfulness tracking table
CREATE TABLE IF NOT EXISTS product_review_helpfulness (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    review_id UUID NOT NULL REFERENCES product_reviews(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    is_helpful BOOLEAN NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Ensure one vote per user per review
    UNIQUE(review_id, user_id)
);

-- Create index for helpfulness tracking
CREATE INDEX IF NOT EXISTS idx_review_helpfulness_review_id ON product_review_helpfulness(review_id);

-- Add product review statistics to shop_products table
ALTER TABLE shop_products ADD COLUMN IF NOT EXISTS average_rating DECIMAL(3,2) DEFAULT 0.00;
ALTER TABLE shop_products ADD COLUMN IF NOT EXISTS total_reviews INTEGER DEFAULT 0;

-- Create function to update product review statistics
CREATE OR REPLACE FUNCTION update_product_review_stats()
RETURNS TRIGGER AS $$
BEGIN
    -- Update product statistics
    UPDATE shop_products 
    SET 
        average_rating = COALESCE((
            SELECT ROUND(AVG(rating)::numeric, 2)
            FROM product_reviews 
            WHERE product_id = COALESCE(NEW.product_id, OLD.product_id)
        ), 0),
        total_reviews = COALESCE((
            SELECT COUNT(*)
            FROM product_reviews 
            WHERE product_id = COALESCE(NEW.product_id, OLD.product_id)
        ), 0)
    WHERE id = COALESCE(NEW.product_id, OLD.product_id);
    
    -- Update shop statistics based on all product reviews
    UPDATE vendor_shops 
    SET 
        rating = COALESCE((
            SELECT ROUND(AVG(pr.rating)::numeric, 2)
            FROM product_reviews pr
            JOIN shop_products sp ON pr.product_id = sp.id
            WHERE sp.shop_id = (
                SELECT shop_id 
                FROM shop_products 
                WHERE id = COALESCE(NEW.product_id, OLD.product_id)
            )
        ), 0),
        total_reviews = COALESCE((
            SELECT COUNT(*)
            FROM product_reviews pr
            JOIN shop_products sp ON pr.product_id = sp.id
            WHERE sp.shop_id = (
                SELECT shop_id 
                FROM shop_products 
                WHERE id = COALESCE(NEW.product_id, OLD.product_id)
            )
        ), 0)
    WHERE id = (
        SELECT shop_id 
        FROM shop_products 
        WHERE id = COALESCE(NEW.product_id, OLD.product_id)
    );
    
    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- Create triggers for automatic statistics updates
DROP TRIGGER IF EXISTS trigger_update_product_review_stats_insert ON product_reviews;
CREATE TRIGGER trigger_update_product_review_stats_insert
    AFTER INSERT ON product_reviews
    FOR EACH ROW
    EXECUTE FUNCTION update_product_review_stats();

DROP TRIGGER IF EXISTS trigger_update_product_review_stats_update ON product_reviews;
CREATE TRIGGER trigger_update_product_review_stats_update
    AFTER UPDATE ON product_reviews
    FOR EACH ROW
    EXECUTE FUNCTION update_product_review_stats();

DROP TRIGGER IF EXISTS trigger_update_product_review_stats_delete ON product_reviews;
CREATE TRIGGER trigger_update_product_review_stats_delete
    AFTER DELETE ON product_reviews
    FOR EACH ROW
    EXECUTE FUNCTION update_product_review_stats();

-- Create function to update review helpfulness count
CREATE OR REPLACE FUNCTION update_review_helpfulness_count()
RETURNS TRIGGER AS $$
BEGIN
    UPDATE product_reviews 
    SET helpful_count = (
        SELECT COUNT(*)
        FROM product_review_helpfulness 
        WHERE review_id = COALESCE(NEW.review_id, OLD.review_id) 
        AND is_helpful = true
    )
    WHERE id = COALESCE(NEW.review_id, OLD.review_id);
    
    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- Create triggers for helpfulness count updates
DROP TRIGGER IF EXISTS trigger_update_helpfulness_count_insert ON product_review_helpfulness;
CREATE TRIGGER trigger_update_helpfulness_count_insert
    AFTER INSERT ON product_review_helpfulness
    FOR EACH ROW
    EXECUTE FUNCTION update_review_helpfulness_count();

DROP TRIGGER IF EXISTS trigger_update_helpfulness_count_update ON product_review_helpfulness;
CREATE TRIGGER trigger_update_helpfulness_count_update
    AFTER UPDATE ON product_review_helpfulness
    FOR EACH ROW
    EXECUTE FUNCTION update_review_helpfulness_count();

DROP TRIGGER IF EXISTS trigger_update_helpfulness_count_delete ON product_review_helpfulness;
CREATE TRIGGER trigger_update_helpfulness_count_delete
    AFTER DELETE ON product_review_helpfulness
    FOR EACH ROW
    EXECUTE FUNCTION update_review_helpfulness_count();

-- RLS Policies for product_reviews
ALTER TABLE product_reviews ENABLE ROW LEVEL SECURITY;

-- Everyone can read reviews
CREATE POLICY "Reviews are viewable by everyone" ON product_reviews
    FOR SELECT USING (true);

-- Users can create reviews for products
CREATE POLICY "Users can create reviews" ON product_reviews
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Users can update their own reviews
CREATE POLICY "Users can update their own reviews" ON product_reviews
    FOR UPDATE USING (auth.uid() = user_id);

-- Users can delete their own reviews
CREATE POLICY "Users can delete their own reviews" ON product_reviews
    FOR DELETE USING (auth.uid() = user_id);

-- RLS Policies for product_review_helpfulness
ALTER TABLE product_review_helpfulness ENABLE ROW LEVEL SECURITY;

-- Everyone can read helpfulness votes
CREATE POLICY "Review helpfulness is viewable by everyone" ON product_review_helpfulness
    FOR SELECT USING (true);

-- Users can vote on review helpfulness
CREATE POLICY "Users can vote on review helpfulness" ON product_review_helpfulness
    FOR ALL USING (auth.uid() = user_id);
