import { NextRequest, NextResponse } from 'next/server'
import { supabaseAdmin, TABLES } from '@/lib/supabase'

/**
 * Update user password using admin privileges
 * This API route handles server-side password updates that require admin privileges
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { email, newPassword } = body

    // Validate required fields
    if (!email) {
      return NextResponse.json(
        { success: false, error: 'Email is required' },
        { status: 400 }
      )
    }

    if (!newPassword) {
      return NextResponse.json(
        { success: false, error: 'New password is required' },
        { status: 400 }
      )
    }

    if (newPassword.length < 6) {
      return NextResponse.json(
        { success: false, error: 'Password must be at least 6 characters long' },
        { status: 400 }
      )
    }

    if (!supabaseAdmin) {
      console.error('Supabase admin client not available')
      return NextResponse.json(
        { success: false, error: 'Admin client not configured' },
        { status: 500 }
      )
    }



    // First, find the user by email to get their auth ID
    const { data: userData, error: userError } = await supabaseAdmin
      .from(TABLES.USERS)
      .select('id, email, full_name')
      .eq('email', email)
      .single()

    if (userError) {
      console.error('User lookup error:', userError)
      return NextResponse.json(
        { success: false, error: `User not found: ${userError.message}` },
        { status: 404 }
      )
    }

    if (!userData) {
      return NextResponse.json(
        { success: false, error: 'User not found' },
        { status: 404 }
      )
    }

    // Update the user password using the admin API
    const { data: updateData, error: updateError } = await supabaseAdmin.auth.admin.updateUserById(
      userData.id,
      { password: newPassword }
    )

    if (updateError) {
      console.error('Password update error:', updateError)
      return NextResponse.json(
        { success: false, error: `Failed to update password: ${updateError.message}` },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      message: 'Password updated successfully',
      data: {
        id: updateData.user.id,
        email: updateData.user.email,
        full_name: userData.full_name,
        updated_at: updateData.user.updated_at
      }
    })

    // Update the user's password using admin auth API
    const { data: authData, error: authError } = await supabaseAdmin.auth.admin.updateUserById(
      userData.id,
      {
        password: newPassword
      }
    )

    if (authError) {
      console.error('Password update error:', authError)
      return NextResponse.json(
        { 
          success: false, 
          error: `Failed to update password: ${authError.message}` 
        },
        { status: 500 }
      )
    }

    console.log(`Password updated successfully for user: ${email}`)

    return NextResponse.json({
      success: true,
      message: `Password updated successfully for ${email}`,
      data: {
        email: userData.email,
        full_name: userData.full_name,
        updated_at: new Date().toISOString()
      }
    })

  } catch (error) {
    console.error('Error in password update API:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Internal server error' 
      },
      { status: 500 }
    )
  }
}
