-- Subscription System Migration
-- Creates tables for subscription packages, user subscriptions, and ad boosts

-- Create subscription packages table
CREATE TABLE subscription_packages (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    price DECIMAL(12,2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'LKR',
    ad_limit INTEGER NOT NULL,
    boost_limit INTEGER NOT NULL,
    duration_days INTEGER DEFAULT 30,
    is_active BOOLEAN DEFAULT TRUE,
    sort_order INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create user subscriptions table
CREATE TABLE user_subscriptions (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    package_id UUID NOT NULL REFERENCES subscription_packages(id),
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'expired', 'cancelled')),
    ads_used INTEGER DEFAULT 0,
    boosts_used INTEGER DEFAULT 0,
    purchased_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create ad boosts table
CREATE TABLE ad_boosts (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    ad_id UUID NOT NULL REFERENCES ads(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    subscription_id UUID NOT NULL REFERENCES user_subscriptions(id) ON DELETE CASCADE,
    boosted_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add indexes for performance
CREATE INDEX idx_user_subscriptions_user_id ON user_subscriptions(user_id);
CREATE INDEX idx_user_subscriptions_status ON user_subscriptions(status);
CREATE INDEX idx_user_subscriptions_expires_at ON user_subscriptions(expires_at);
CREATE INDEX idx_ad_boosts_ad_id ON ad_boosts(ad_id);
CREATE INDEX idx_ad_boosts_user_id ON ad_boosts(user_id);
CREATE INDEX idx_ad_boosts_expires_at ON ad_boosts(expires_at);
CREATE INDEX idx_ad_boosts_is_active ON ad_boosts(is_active);

-- Add composite indexes
CREATE INDEX idx_user_subscriptions_user_status ON user_subscriptions(user_id, status);
CREATE INDEX idx_ad_boosts_active_expires ON ad_boosts(is_active, expires_at);

-- Insert default subscription packages
INSERT INTO subscription_packages (name, description, price, ad_limit, boost_limit, sort_order) VALUES
('Starter Package', 'Perfect for individuals getting started', 2000.00, 20, 2, 1),
('Business Package', 'Great for small businesses', 5000.00, 75, 10, 2),
('Professional Package', 'Ideal for growing businesses', 10000.00, 250, 25, 3),
('Enterprise Package', 'For large businesses and power users', 15000.00, 2000, 200, 4);

-- Add subscription-related columns to ads table if not exists
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'ads' AND column_name = 'is_boosted') THEN
        ALTER TABLE ads ADD COLUMN is_boosted BOOLEAN DEFAULT FALSE;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'ads' AND column_name = 'boost_expires_at') THEN
        ALTER TABLE ads ADD COLUMN boost_expires_at TIMESTAMP WITH TIME ZONE;
    END IF;
END $$;

-- Create function to update subscription usage
CREATE OR REPLACE FUNCTION update_subscription_usage()
RETURNS TRIGGER AS $$
BEGIN
    -- Update ads_used when a new ad is created
    IF TG_OP = 'INSERT' AND TG_TABLE_NAME = 'ads' THEN
        UPDATE user_subscriptions 
        SET ads_used = ads_used + 1,
            updated_at = NOW()
        WHERE user_id = NEW.user_id 
        AND status = 'active' 
        AND expires_at > NOW()
        ORDER BY expires_at ASC
        LIMIT 1;
    END IF;
    
    -- Update boosts_used when a new boost is created
    IF TG_OP = 'INSERT' AND TG_TABLE_NAME = 'ad_boosts' THEN
        UPDATE user_subscriptions 
        SET boosts_used = boosts_used + 1,
            updated_at = NOW()
        WHERE id = NEW.subscription_id;
    END IF;
    
    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- Create triggers
CREATE TRIGGER trigger_update_ads_usage
    AFTER INSERT ON ads
    FOR EACH ROW
    EXECUTE FUNCTION update_subscription_usage();

CREATE TRIGGER trigger_update_boosts_usage
    AFTER INSERT ON ad_boosts
    FOR EACH ROW
    EXECUTE FUNCTION update_subscription_usage();

-- Create function to expire boosts
CREATE OR REPLACE FUNCTION expire_ad_boosts()
RETURNS void AS $$
BEGIN
    -- Update expired boosts
    UPDATE ad_boosts 
    SET is_active = FALSE,
        updated_at = NOW()
    WHERE is_active = TRUE 
    AND expires_at <= NOW();
    
    -- Update ads table to remove boost status
    UPDATE ads 
    SET is_boosted = FALSE,
        boost_expires_at = NULL,
        updated_at = NOW()
    WHERE id IN (
        SELECT ad_id 
        FROM ad_boosts 
        WHERE is_active = FALSE 
        AND boost_expires_at IS NOT NULL
    );
END;
$$ LANGUAGE plpgsql;

-- Create function to expire subscriptions
CREATE OR REPLACE FUNCTION expire_subscriptions()
RETURNS void AS $$
BEGIN
    UPDATE user_subscriptions 
    SET status = 'expired',
        updated_at = NOW()
    WHERE status = 'active' 
    AND expires_at <= NOW();
END;
$$ LANGUAGE plpgsql;

-- Add updated_at trigger for subscription tables
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_subscription_packages_updated_at
    BEFORE UPDATE ON subscription_packages
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_subscriptions_updated_at
    BEFORE UPDATE ON user_subscriptions
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_ad_boosts_updated_at
    BEFORE UPDATE ON ad_boosts
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();
