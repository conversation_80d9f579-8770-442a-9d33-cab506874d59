import { useState, useCallback, useMemo } from 'react'
import { EnhancedNetworkTreeNode } from '@/components/admin/EnhancedReferralTree'

export interface SearchResult {
  nodeId: string
  node: EnhancedNetworkTreeNode
  matchType: 'name' | 'email'
  matchText: string
  path: string[]
}

export const useTreeSearch = (rootNode: EnhancedNetworkTreeNode | null) => {
  const [searchTerm, setSearchTerm] = useState('')
  const [currentResultIndex, setCurrentResultIndex] = useState(0)

  // Search through the tree and find matching nodes
  const searchResults = useMemo(() => {
    if (!searchTerm.trim() || !rootNode) return []

    const results: SearchResult[] = []
    const searchLower = searchTerm.toLowerCase()

    const searchNode = (node: EnhancedNetworkTreeNode, path: string[] = []) => {
      const currentPath = [...path, node.user.full_name || node.user.email]
      
      // Check if name matches
      const nameMatch = node.user.full_name?.toLowerCase().includes(searchLower)
      if (nameMatch) {
        results.push({
          nodeId: node.user.id,
          node,
          matchType: 'name',
          matchText: node.user.full_name || '',
          path: currentPath
        })
      }

      // Check if email matches
      const emailMatch = node.user.email.toLowerCase().includes(searchLower)
      if (emailMatch && !nameMatch) { // Avoid duplicate results
        results.push({
          nodeId: node.user.id,
          node,
          matchType: 'email',
          matchText: node.user.email,
          path: currentPath
        })
      }

      // Search children
      if (node.children && node.children.length > 0) {
        node.children.forEach(child => searchNode(child, currentPath))
      }
    }

    searchNode(rootNode)
    return results
  }, [searchTerm, rootNode])

  // Create filtered tree that only shows search results and their paths
  const filteredTree = useMemo(() => {
    if (!searchTerm.trim() || !rootNode || searchResults.length === 0) return null

    const resultNodeIds = new Set(searchResults.map(r => r.nodeId))
    
    // Get all ancestor node IDs for search results
    const ancestorIds = new Set<string>()
    
    const findAncestors = (node: EnhancedNetworkTreeNode, targetIds: Set<string>, currentPath: string[] = []): boolean => {
      const newPath = [...currentPath, node.user.id]
      let hasMatchingDescendant = false

      // Check if this node is a search result
      if (targetIds.has(node.user.id)) {
        hasMatchingDescendant = true
        // Add all ancestors to the set
        currentPath.forEach(id => ancestorIds.add(id))
      }

      // Check children
      if (node.children && node.children.length > 0) {
        for (const child of node.children) {
          if (findAncestors(child, targetIds, newPath)) {
            hasMatchingDescendant = true
          }
        }
      }

      if (hasMatchingDescendant) {
        ancestorIds.add(node.user.id)
      }

      return hasMatchingDescendant
    }

    findAncestors(rootNode, resultNodeIds)

    // Filter the tree to only include relevant nodes
    const filterTree = (node: EnhancedNetworkTreeNode): EnhancedNetworkTreeNode | null => {
      const shouldInclude = ancestorIds.has(node.user.id) || resultNodeIds.has(node.user.id)
      
      if (!shouldInclude) return null

      const filteredChildren = node.children
        ?.map(child => filterTree(child))
        .filter((child): child is EnhancedNetworkTreeNode => child !== null) || []

      return {
        ...node,
        children: filteredChildren
      }
    }

    return filterTree(rootNode)
  }, [searchTerm, rootNode, searchResults])

  // Get expanded nodes for search results (to show the path to results)
  const getExpandedNodesForSearch = useCallback(() => {
    if (!searchTerm.trim() || searchResults.length === 0) return new Set<string>()

    const expandedIds = new Set<string>()
    
    // Add all ancestor nodes of search results to expanded set
    const addAncestors = (node: EnhancedNetworkTreeNode, targetIds: Set<string>) => {
      const hasMatchingDescendant = (n: EnhancedNetworkTreeNode): boolean => {
        if (targetIds.has(n.user.id)) return true
        return n.children?.some(child => hasMatchingDescendant(child)) || false
      }

      if (hasMatchingDescendant(node)) {
        expandedIds.add(node.user.id)
        node.children?.forEach(child => addAncestors(child, targetIds))
      }
    }

    if (rootNode) {
      const resultIds = new Set(searchResults.map(r => r.nodeId))
      addAncestors(rootNode, resultIds)
    }

    return expandedIds
  }, [searchTerm, searchResults, rootNode])

  // Navigation functions
  const goToNextResult = useCallback(() => {
    if (searchResults.length === 0) return null
    const nextIndex = (currentResultIndex + 1) % searchResults.length
    setCurrentResultIndex(nextIndex)
    return searchResults[nextIndex]
  }, [searchResults, currentResultIndex])

  const goToPreviousResult = useCallback(() => {
    if (searchResults.length === 0) return null
    const prevIndex = currentResultIndex === 0 ? searchResults.length - 1 : currentResultIndex - 1
    setCurrentResultIndex(prevIndex)
    return searchResults[prevIndex]
  }, [searchResults, currentResultIndex])

  const goToResult = useCallback((index: number) => {
    if (index >= 0 && index < searchResults.length) {
      setCurrentResultIndex(index)
      return searchResults[index]
    }
    return null
  }, [searchResults])

  // Search control functions
  const startSearch = useCallback((term: string) => {
    setSearchTerm(term)
    setCurrentResultIndex(0)
  }, [])

  const clearSearch = useCallback(() => {
    setSearchTerm('')
    setCurrentResultIndex(0)
  }, [])

  // Computed properties
  const isSearchActive = searchTerm.trim().length > 0
  const hasResults = searchResults.length > 0
  const currentResult = hasResults ? searchResults[currentResultIndex] : null

  return {
    searchTerm,
    searchResults,
    filteredTree,
    currentResultIndex,
    currentResult,
    isSearchActive,
    hasResults,
    startSearch,
    clearSearch,
    goToNextResult,
    goToPreviousResult,
    goToResult,
    getExpandedNodesForSearch
  }
}
