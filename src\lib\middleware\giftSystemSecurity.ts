import { NextRequest } from 'next/server'
import { supabase, supabaseAdmin } from '@/lib/supabase'

export interface SecurityContext {
  userId: string
  userType: string
  isAdmin: boolean
  ipAddress: string
  userAgent: string
}

export class GiftSystemSecurity {
  /**
   * Verify admin access for present-related operations
   */
  static async verifyAdminAccess(request: NextRequest): Promise<SecurityContext> {
    const { data: { session }, error } = await supabase.auth.getSession()

    if (error || !session?.user) {
      throw new Error('Authentication required')
    }

    // Get user details
    const { data: user, error: userError } = await supabaseAdmin
      .from('users')
      .select('user_type')
      .eq('id', session.user.id)
      .single()

    if (userError || !user) {
      throw new Error('User not found')
    }

    if (user.user_type !== 'admin') {
      throw new Error('Admin privileges required')
    }

    return {
      userId: session.user.id,
      userType: user.user_type,
      isAdmin: true,
      ipAddress: this.getClientIP(request),
      userAgent: request.headers.get('user-agent') || 'Unknown'
    }
  }

  /**
   * Verify user access for task-related operations
   */
  static async verifyUserAccess(request: NextRequest): Promise<SecurityContext> {
    const { data: { session }, error } = await supabase.auth.getSession()

    if (error || !session?.user) {
      throw new Error('Authentication required')
    }

    // Get user details
    const { data: user, error: userError } = await supabaseAdmin
      .from('users')
      .select('user_type')
      .eq('id', session.user.id)
      .single()

    if (userError || !user) {
      throw new Error('User not found')
    }

    return {
      userId: session.user.id,
      userType: user.user_type || 'user',
      isAdmin: user.user_type === 'admin',
      ipAddress: this.getClientIP(request),
      userAgent: request.headers.get('user-agent') || 'Unknown'
    }
  }

  /**
   * Log security event to audit trail
   */
  static async logSecurityEvent(
    context: SecurityContext,
    action: string,
    entityType: string,
    entityId?: string,
    metadata?: Record<string, any>
  ): Promise<void> {
    try {
      await supabaseAdmin.rpc('log_gift_system_audit', {
        p_user_id: context.userId,
        p_admin_id: context.isAdmin ? context.userId : null,
        p_action: action,
        p_entity_type: entityType,
        p_entity_id: entityId,
        p_metadata: {
          ...metadata,
          ip_address: context.ipAddress,
          user_agent: context.userAgent,
          timestamp: new Date().toISOString()
        },
        p_ip_address: context.ipAddress,
        p_user_agent: context.userAgent
      })
    } catch (error) {
      console.error('Failed to log security event:', error)
      // Don't throw error to avoid breaking the main operation
    }
  }

  /**
   * Verify task completion securely
   */
  static async verifyTaskCompletion(
    userId: string,
    taskId: string
  ): Promise<boolean> {
    try {
      const { data, error } = await supabaseAdmin.rpc('verify_task_completion', {
        p_user_id: userId,
        p_task_id: taskId
      })

      if (error) {
        throw new Error(`Task verification failed: ${error.message}`)
      }

      return data || false
    } catch (error) {
      console.error('Task verification error:', error)
      throw error
    }
  }

  /**
   * Validate present allocation access (admin-only)
   */
  static async validatePresentAccess(context: SecurityContext): Promise<void> {
    if (!context.isAdmin) {
      await this.logSecurityEvent(
        context,
        'unauthorized_present_access_attempt',
        'security_violation',
        undefined,
        { attempted_action: 'present_access', user_type: context.userType }
      )
      throw new Error('Access denied: Present allocations are admin-only')
    }
  }

  /**
   * Extract client IP address
   */
  private static getClientIP(request: NextRequest): string {
    const forwarded = request.headers.get('x-forwarded-for')
    const realIP = request.headers.get('x-real-ip')

    if (forwarded) {
      return forwarded.split(',')[0].trim()
    }

    if (realIP) {
      return realIP
    }

    return 'unknown'
  }

  /**
   * Sanitize sensitive data from logs
   */
  static sanitizeLogData(data: any): any {
    if (!data || typeof data !== 'object') {
      return data
    }

    const sensitiveFields = [
      'password', 'token', 'secret', 'key', 'auth',
      'present_allocation', 'allocation_amount', 'pool_balance'
    ]

    const sanitized = { ...data }

    for (const field of sensitiveFields) {
      if (field in sanitized) {
        sanitized[field] = '[REDACTED]'
      }
    }

    return sanitized
  }
}