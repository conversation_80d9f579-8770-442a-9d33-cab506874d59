-- Fix shop products to be independent from ads system
-- This migration updates the shop_products table structure

-- First, drop the existing shop_products table if it exists
DROP TABLE IF EXISTS shop_products CASCADE;

-- Recreate shop_products table with proper structure (independent from ads)
CREATE TABLE shop_products (
    id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
    shop_id uuid REFERENCES vendor_shops(id) ON DELETE CASCADE NOT NULL,
    category_id uuid REFERENCES shop_categories(id) ON DELETE SET NULL,
    subcategory_id uuid REFERENCES shop_subcategories(id) ON DELETE SET NULL,
    title varchar(200) NOT NULL,
    description text NOT NULL,
    price numeric(12,2) NOT NULL DEFAULT 0,
    currency varchar(3) DEFAULT 'LKR',
    condition varchar(20) DEFAULT 'new' CHECK (condition IN ('new', 'used', 'refurbished')),
    sku varchar(100),
    stock_quantity integer DEFAULT 0,
    min_order_quantity integer DEFAULT 1,
    weight numeric(10,2),
    dimensions jsonb, -- {length, width, height}
    shipping_info jsonb, -- shipping options and costs
    variants jsonb DEFAULT '[]', -- product variants (size, color, etc.)
    is_digital boolean DEFAULT false,
    download_url text, -- for digital products
    contact_phone varchar(20),
    negotiable boolean DEFAULT false,
    status varchar(20) DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'out_of_stock', 'draft')),
    views integer DEFAULT 0,
    average_rating numeric(3,2) DEFAULT 0.00,
    total_reviews integer DEFAULT 0,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now()
);

-- Create product images table (separate from ad images)
CREATE TABLE shop_product_images (
    id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
    product_id uuid REFERENCES shop_products(id) ON DELETE CASCADE NOT NULL,
    image_url text NOT NULL,
    alt_text varchar(255),
    sort_order integer DEFAULT 0,
    is_primary boolean DEFAULT false,
    created_at timestamp with time zone DEFAULT now()
);

-- Create indexes for better performance
CREATE INDEX idx_shop_products_shop_id ON shop_products(shop_id);
CREATE INDEX idx_shop_products_category_id ON shop_products(category_id);
CREATE INDEX idx_shop_products_subcategory_id ON shop_products(subcategory_id);
CREATE INDEX idx_shop_products_status ON shop_products(status);
CREATE INDEX idx_shop_products_created_at ON shop_products(created_at);
CREATE INDEX idx_shop_product_images_product_id ON shop_product_images(product_id);
CREATE INDEX idx_shop_product_images_sort_order ON shop_product_images(sort_order);

-- Enable RLS
ALTER TABLE shop_products ENABLE ROW LEVEL SECURITY;
ALTER TABLE shop_product_images ENABLE ROW LEVEL SECURITY;

-- RLS policies for shop_products
CREATE POLICY "Shop products are viewable by everyone" ON shop_products
    FOR SELECT USING (true);

CREATE POLICY "Shop owners can insert their products" ON shop_products
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM vendor_shops 
            WHERE id = shop_id AND user_id = auth.uid()
        )
    );

CREATE POLICY "Shop owners can update their products" ON shop_products
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM vendor_shops 
            WHERE id = shop_id AND user_id = auth.uid()
        )
    );

CREATE POLICY "Shop owners can delete their products" ON shop_products
    FOR DELETE USING (
        EXISTS (
            SELECT 1 FROM vendor_shops 
            WHERE id = shop_id AND user_id = auth.uid()
        )
    );

-- RLS policies for shop_product_images
CREATE POLICY "Product images are viewable by everyone" ON shop_product_images
    FOR SELECT USING (true);

CREATE POLICY "Shop owners can insert product images" ON shop_product_images
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM shop_products sp
            JOIN vendor_shops vs ON sp.shop_id = vs.id
            WHERE sp.id = product_id AND vs.user_id = auth.uid()
        )
    );

CREATE POLICY "Shop owners can update product images" ON shop_product_images
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM shop_products sp
            JOIN vendor_shops vs ON sp.shop_id = vs.id
            WHERE sp.id = product_id AND vs.user_id = auth.uid()
        )
    );

CREATE POLICY "Shop owners can delete product images" ON shop_product_images
    FOR DELETE USING (
        EXISTS (
            SELECT 1 FROM shop_products sp
            JOIN vendor_shops vs ON sp.shop_id = vs.id
            WHERE sp.id = product_id AND vs.user_id = auth.uid()
        )
    );
