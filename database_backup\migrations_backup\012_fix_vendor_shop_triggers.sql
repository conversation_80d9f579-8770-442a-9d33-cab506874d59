-- Fix missing vendor shop triggers and functions

-- Function to update shop stats
CREATE OR REPLACE FUNCTION update_shop_stats()
RETURNS trigger AS $$
BEGIN
    -- Update total_products count
    UPDATE vendor_shops 
    SET total_products = (
        SELECT COUNT(*) 
        FROM shop_products 
        WHERE shop_id = COALESCE(NEW.shop_id, OLD.shop_id)
    )
    WHERE id = COALESCE(NEW.shop_id, OLD.shop_id);
    
    -- Update rating and review count
    UPDATE vendor_shops 
    SET 
        rating = COALESCE((
            SELECT AVG(rating)::numeric(3,2) 
            FROM shop_reviews 
            WHERE shop_id = COALESCE(NEW.shop_id, OLD.shop_id)
        ), 0.00),
        total_reviews = (
            SELECT COUNT(*) 
            FROM shop_reviews 
            WHERE shop_id = COALESCE(NEW.shop_id, OLD.shop_id)
        )
    WHERE id = COALESCE(NEW.shop_id, OLD.shop_id);
    
    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- Create triggers for updated_at columns
CREATE TRIGGER update_vendor_shops_updated_at
    BEFORE UPDATE ON vendor_shops
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_shop_products_updated_at
    BEFORE UPDATE ON shop_products
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_shop_reviews_updated_at
    BEFORE UPDATE ON shop_reviews
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Create triggers to update shop stats
CREATE TRIGGER update_shop_stats_on_product_change
    AFTER INSERT OR UPDATE OR DELETE ON shop_products
    FOR EACH ROW
    EXECUTE FUNCTION update_shop_stats();

CREATE TRIGGER update_shop_stats_on_review_change
    AFTER INSERT OR UPDATE OR DELETE ON shop_reviews
    FOR EACH ROW
    EXECUTE FUNCTION update_shop_stats();
