-- Fix Referral Hierarchy Placement Function
-- This migration fixes the place_user_in_hierarchy function to properly handle null referral paths
-- and ensures Zonal Managers can have normal users as direct referrals.

-- =====================================================
-- 1. FIX PLACE_USER_IN_HIERARCHY FUNCTION
-- =====================================================

-- Drop and recreate the function with proper null handling
CREATE OR REPLACE FUNCTION public.place_user_in_hierarchy(
    new_user_id uuid, 
    referrer_id uuid
) 
RETURNS void 
LANGUAGE plpgsql 
AS $function$
DECLARE
    placement_info RECORD;
    referrer_level INTEGER;
    new_level INTEGER;
    ancestor_record RECORD;
    parent_path TEXT;
BEGIN
    -- Get referrer's level
    SELECT referral_level INTO referrer_level FROM users WHERE id = referrer_id;
    new_level := referrer_level + 1;
    
    -- Find placement position
    SELECT * INTO placement_info FROM find_next_placement_position(referrer_id);
    
    IF placement_info.placement_parent_id IS NULL THEN
        RAISE EXCEPTION 'No placement position found for user %', new_user_id;
    END IF;
    
    -- Get parent's referral path (handle null values)
    SELECT COALESCE(referral_path, '') INTO parent_path FROM users WHERE id = placement_info.placement_parent_id;
    
    -- Update user's referral information
    UPDATE users SET
        referred_by_id = placement_info.placement_parent_id,
        referral_level = new_level,
        referral_path = parent_path || '/' || new_user_id::TEXT
    WHERE id = new_user_id;
    
    -- Create placement record
    INSERT INTO referral_placements (parent_id, child_id, position, placement_type)
    VALUES (
        placement_info.placement_parent_id, 
        new_user_id, 
        placement_info.placement_position,
        CASE WHEN placement_info.placement_parent_id = referrer_id THEN 'direct' ELSE 'spillover' END
    );
    
    -- Create hierarchy records for all ancestors (only if parent path exists)
    IF parent_path IS NOT NULL AND parent_path != '' THEN
        FOR ancestor_record IN 
            SELECT id, referral_level FROM users 
            WHERE id = ANY(string_to_array(trim(both '/' from parent_path), '/')::UUID[])
        LOOP
            INSERT INTO referral_hierarchy (user_id, ancestor_id, level_difference)
            VALUES (new_user_id, ancestor_record.id, new_level - ancestor_record.referral_level);
        END LOOP;
    END IF;
    
    -- Always create hierarchy record for direct parent
    INSERT INTO referral_hierarchy (user_id, ancestor_id, level_difference)
    VALUES (new_user_id, placement_info.placement_parent_id, 1);
    
    -- Update referral counts
    UPDATE users SET 
        direct_referrals_count = direct_referrals_count + 1
    WHERE id = referrer_id;
    
    -- Update total downline counts for all ancestors
    UPDATE users SET 
        total_downline_count = total_downline_count + 1
    WHERE id IN (
        SELECT ancestor_id FROM referral_hierarchy WHERE user_id = new_user_id
    );
END;
$function$;

-- =====================================================
-- 2. ADD COMMENTS FOR DOCUMENTATION
-- =====================================================

COMMENT ON FUNCTION public.place_user_in_hierarchy(uuid, uuid) IS 
'Places a new user in the referral hierarchy under the specified referrer. Handles automatic placement with spillover logic and supports all user types including Zonal Managers having normal users as direct referrals.';

-- =====================================================
-- 3. VERIFICATION QUERIES (FOR TESTING)
-- =====================================================

-- The following queries can be used to verify the fix works correctly:
-- 
-- 1. Check if Zonal Managers can have normal users as referrals:
-- SELECT rp.*, parent.user_type as parent_type, child.user_type as child_type 
-- FROM referral_placements rp 
-- JOIN users parent ON parent.id = rp.parent_id 
-- JOIN users child ON child.id = rp.child_id 
-- WHERE parent.user_type = 'zonal_manager' AND child.user_type = 'user';
--
-- 2. Check referral counts for Zonal Managers:
-- SELECT full_name, user_type, direct_referrals_count, total_downline_count 
-- FROM users WHERE user_type = 'zonal_manager';
--
-- 3. Verify hierarchy integrity:
-- SELECT u.full_name, u.user_type, u.referral_level, u.referral_path 
-- FROM users u WHERE u.referred_by_id IS NOT NULL 
-- ORDER BY u.referral_level, u.created_at;
