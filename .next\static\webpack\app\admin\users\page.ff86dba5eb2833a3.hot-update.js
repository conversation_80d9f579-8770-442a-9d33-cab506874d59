"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/users/page",{

/***/ "(app-pages-browser)/./src/app/admin/users/page.tsx":
/*!**************************************!*\
  !*** ./src/app/admin/users/page.tsx ***!
  \**************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ AdminUsers; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Award_Ban_Calendar_Mail_MoreVertical_Network_Phone_Search_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Ban,Calendar,Mail,MoreVertical,Network,Phone,Search,Shield,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/more-vertical.js\");\n/* harmony import */ var _barrel_optimize_names_Award_Ban_Calendar_Mail_MoreVertical_Network_Phone_Search_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Ban,Calendar,Mail,MoreVertical,Network,Phone,Search,Shield,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Award_Ban_Calendar_Mail_MoreVertical_Network_Phone_Search_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Ban,Calendar,Mail,MoreVertical,Network,Phone,Search,Shield,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/network.js\");\n/* harmony import */ var _barrel_optimize_names_Award_Ban_Calendar_Mail_MoreVertical_Network_Phone_Search_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Ban,Calendar,Mail,MoreVertical,Network,Phone,Search,Shield,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Award_Ban_Calendar_Mail_MoreVertical_Network_Phone_Search_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Ban,Calendar,Mail,MoreVertical,Network,Phone,Search,Shield,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/ban.js\");\n/* harmony import */ var _barrel_optimize_names_Award_Ban_Calendar_Mail_MoreVertical_Network_Phone_Search_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Ban,Calendar,Mail,MoreVertical,Network,Phone,Search,Shield,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Award_Ban_Calendar_Mail_MoreVertical_Network_Phone_Search_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Ban,Calendar,Mail,MoreVertical,Network,Phone,Search,Shield,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Award_Ban_Calendar_Mail_MoreVertical_Network_Phone_Search_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Ban,Calendar,Mail,MoreVertical,Network,Phone,Search,Shield,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_Award_Ban_Calendar_Mail_MoreVertical_Network_Phone_Search_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Ban,Calendar,Mail,MoreVertical,Network,Phone,Search,Shield,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _barrel_optimize_names_Award_Ban_Calendar_Mail_MoreVertical_Network_Phone_Search_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Ban,Calendar,Mail,MoreVertical,Network,Phone,Search,Shield,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/award.js\");\n/* harmony import */ var _barrel_optimize_names_Award_Ban_Calendar_Mail_MoreVertical_Network_Phone_Search_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Ban,Calendar,Mail,MoreVertical,Network,Phone,Search,Shield,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Award_Ban_Calendar_Mail_MoreVertical_Network_Phone_Search_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Ban,Calendar,Mail,MoreVertical,Network,Phone,Search,Shield,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _components_admin_AdminLayout__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/admin/AdminLayout */ \"(app-pages-browser)/./src/components/admin/AdminLayout.tsx\");\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dynamic */ \"(app-pages-browser)/./node_modules/next/dist/api/app-dynamic.js\");\n/* harmony import */ var _components_ui_ReferralRankBadge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/ReferralRankBadge */ \"(app-pages-browser)/./src/components/ui/ReferralRankBadge.tsx\");\n/* harmony import */ var _lib_services_admin__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/services/admin */ \"(app-pages-browser)/./src/lib/services/admin.ts\");\n/* harmony import */ var _components_admin_ViewUserDataModal__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/admin/ViewUserDataModal */ \"(app-pages-browser)/./src/components/admin/ViewUserDataModal.tsx\");\n/* harmony import */ var _barrel_optimize_names_Key_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Key!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/key.js\");\n/* harmony import */ var _components_ui_ConfirmationDialog__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/ConfirmationDialog */ \"(app-pages-browser)/./src/components/ui/ConfirmationDialog.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n// Lazy load the heavy referral tree component\nconst EnhancedReferralTree = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_src_components_admin_EnhancedReferralTree_tsx-_d5ef1\").then(__webpack_require__.bind(__webpack_require__, /*! @/components/admin/EnhancedReferralTree */ \"(app-pages-browser)/./src/components/admin/EnhancedReferralTree.tsx\")), {\n    loadableGenerated: {\n        modules: [\n            \"app\\\\admin\\\\users\\\\page.tsx -> \" + \"@/components/admin/EnhancedReferralTree\"\n        ]\n    },\n    loading: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-96\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-blue\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                lineNumber: 31,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n            lineNumber: 30,\n            columnNumber: 7\n        }, undefined),\n    ssr: false\n});\n_c = EnhancedReferralTree;\n\n\n\n\n\nfunction UserActions(param) {\n    let { user, onRoleUpdate, onBanUser, onViewTree, onViewUserData, onUpdatePassword } = param;\n    _s();\n    const [showMenu, setShowMenu] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const menuRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Close menu when clicking outside\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleClickOutside = (event)=>{\n            if (menuRef.current && !menuRef.current.contains(event.target)) {\n                setShowMenu(false);\n            }\n        };\n        document.addEventListener(\"mousedown\", handleClickOutside);\n        return ()=>document.removeEventListener(\"mousedown\", handleClickOutside);\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative\",\n        ref: menuRef,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: ()=>setShowMenu(!showMenu),\n                className: \"p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_Ban_Calendar_Mail_MoreVertical_Network_Phone_Search_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    className: \"h-4 w-4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                    lineNumber: 74,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                lineNumber: 70,\n                columnNumber: 7\n            }, this),\n            showMenu && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 z-[9999]\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"py-1\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>{\n                                onViewUserData(user);\n                                setShowMenu(false);\n                            },\n                            className: \"flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_Ban_Calendar_Mail_MoreVertical_Network_Phone_Search_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    className: \"h-4 w-4 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                    lineNumber: 87,\n                                    columnNumber: 15\n                                }, this),\n                                \"View User Data\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                            lineNumber: 80,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>{\n                                onViewTree(user.id);\n                                setShowMenu(false);\n                            },\n                            className: \"flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_Ban_Calendar_Mail_MoreVertical_Network_Phone_Search_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    className: \"h-4 w-4 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                    lineNumber: 97,\n                                    columnNumber: 15\n                                }, this),\n                                \"View Referral Tree\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                            lineNumber: 90,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>{\n                                onUpdatePassword(user);\n                                setShowMenu(false);\n                            },\n                            className: \"flex items-center w-full px-4 py-2 text-sm text-blue-600 hover:bg-blue-50\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Key_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    className: \"h-4 w-4 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                    lineNumber: 107,\n                                    columnNumber: 15\n                                }, this),\n                                \"Update Password\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                            lineNumber: 100,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>{\n                                onRoleUpdate(user.id, user.role === \"admin\" ? \"user\" : \"admin\");\n                                setShowMenu(false);\n                            },\n                            className: \"flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_Ban_Calendar_Mail_MoreVertical_Network_Phone_Search_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    className: \"h-4 w-4 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                    lineNumber: 117,\n                                    columnNumber: 15\n                                }, this),\n                                user.role === \"admin\" ? \"Remove Admin\" : \"Make Admin\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                            lineNumber: 110,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>{\n                                onBanUser(user.id);\n                                setShowMenu(false);\n                            },\n                            className: \"flex items-center w-full px-4 py-2 text-sm text-red-600 hover:bg-red-50\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_Ban_Calendar_Mail_MoreVertical_Network_Phone_Search_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                    className: \"h-4 w-4 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                    lineNumber: 127,\n                                    columnNumber: 15\n                                }, this),\n                                user.banned_until ? \"Unban User\" : \"Ban User\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                            lineNumber: 120,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                    lineNumber: 79,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                lineNumber: 78,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n        lineNumber: 69,\n        columnNumber: 5\n    }, this);\n}\n_s(UserActions, \"IrpJJ9vEf04eEvLSh3rG0JvlrDI=\");\n_c1 = UserActions;\nfunction AdminUsers() {\n    _s1();\n    const [users, setUsers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [searching, setSearching] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [totalUsers, setTotalUsers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [selectedRole, setSelectedRole] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    const [showReferralTree, setShowReferralTree] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedUserTree, setSelectedUserTree] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [treeLoading, setTreeLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedUserInfo, setSelectedUserInfo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showUserDataModal, setShowUserDataModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedUserData, setSelectedUserData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showPasswordModal, setShowPasswordModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedUserForPassword, setSelectedUserForPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [newPassword, setNewPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [updatingPassword, setUpdatingPassword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [giftSystemData, setGiftSystemData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Map());\n    const usersPerPage = 20;\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchUsers();\n    }, [\n        currentPage,\n        selectedRole\n    ]);\n    // Debounced search effect\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setSearching(true);\n        const timeoutId = setTimeout(()=>{\n            if (currentPage === 1) {\n                fetchUsers();\n            } else {\n                setCurrentPage(1) // Reset to first page when searching\n                ;\n            }\n            setSearching(false);\n        }, 500) // 500ms debounce\n        ;\n        return ()=>{\n            clearTimeout(timeoutId);\n            setSearching(false);\n        };\n    }, [\n        searchTerm\n    ]);\n    const fetchGiftSystemData = async (userIds)=>{\n        try {\n            const giftSystemMap = new Map();\n            // Fetch gift system commissions for all users in parallel\n            const promises = userIds.map(async (userId)=>{\n                try {\n                    const response = await fetch(\"/api/commission-breakdown?userId=\".concat(userId, \"&includeGiftSystem=true\"));\n                    if (response.ok) {\n                        const result = await response.json();\n                        if (result.success && result.data.giftSystemCommissions) {\n                            giftSystemMap.set(userId, result.data.giftSystemCommissions.totalGiftSystem);\n                        }\n                    }\n                } catch (error) {\n                    console.error(\"Error fetching gift system data for user \".concat(userId, \":\"), error);\n                }\n            });\n            await Promise.all(promises);\n            setGiftSystemData(giftSystemMap);\n        } catch (error) {\n            console.error(\"Error fetching gift system data:\", error);\n        }\n    };\n    const fetchUsers = async ()=>{\n        try {\n            setLoading(true);\n            const { users: userData, total } = await _lib_services_admin__WEBPACK_IMPORTED_MODULE_5__.AdminService.getAllUsers(currentPage, usersPerPage, searchTerm.trim() || undefined, selectedRole !== \"all\" ? selectedRole : undefined);\n            setUsers(userData);\n            setTotalUsers(total);\n            // Fetch gift system data for the current page of users\n            if (userData.length > 0) {\n                const userIds = userData.map((user)=>user.id);\n                await fetchGiftSystemData(userIds);\n            }\n        } catch (err) {\n            setError(err instanceof Error ? err.message : \"Failed to load users\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleRoleUpdate = async (userId, newRole)=>{\n        try {\n            await _lib_services_admin__WEBPACK_IMPORTED_MODULE_5__.AdminService.updateUserRole(userId, newRole);\n            await fetchUsers() // Refresh the list\n            ;\n        } catch (err) {\n            alert(\"Failed to update user role\");\n        }\n    };\n    const handleBanUser = async (userId)=>{\n        try {\n            const user = users.find((u)=>u.id === userId);\n            const banUntil = (user === null || user === void 0 ? void 0 : user.banned_until) ? undefined : new Date(Date.now() + 30 * 24 * 60 * 60 * 1000);\n            await _lib_services_admin__WEBPACK_IMPORTED_MODULE_5__.AdminService.banUser(userId, banUntil);\n            await fetchUsers() // Refresh the list\n            ;\n        } catch (err) {\n            await (0,_components_ui_ConfirmationDialog__WEBPACK_IMPORTED_MODULE_7__.showAlert)({\n                title: \"Error\",\n                message: \"Failed to ban/unban user\",\n                variant: \"danger\"\n            });\n        }\n    };\n    const handleViewUserData = (user)=>{\n        setSelectedUserData(user);\n        setShowUserDataModal(true);\n    };\n    const handleUpdatePassword = (user)=>{\n        setSelectedUserForPassword(user);\n        setNewPassword(\"\");\n        setShowPasswordModal(true);\n    };\n    const handlePasswordUpdate = async ()=>{\n        if (!selectedUserForPassword || !newPassword) {\n            return;\n        }\n        try {\n            setUpdatingPassword(true);\n            const response = await fetch(\"/api/admin/users/update-password\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    email: selectedUserForPassword.email,\n                    newPassword: newPassword\n                })\n            });\n            const result = await response.json();\n            if (result.success) {\n                await (0,_components_ui_ConfirmationDialog__WEBPACK_IMPORTED_MODULE_7__.showAlert)({\n                    title: \"Success\",\n                    message: \"Password updated successfully for \".concat(selectedUserForPassword.email),\n                    variant: \"success\"\n                });\n                setShowPasswordModal(false);\n                setSelectedUserForPassword(null);\n                setNewPassword(\"\");\n            } else {\n                await (0,_components_ui_ConfirmationDialog__WEBPACK_IMPORTED_MODULE_7__.showAlert)({\n                    title: \"Error\",\n                    message: result.error || \"Failed to update password\",\n                    variant: \"danger\"\n                });\n            }\n        } catch (error) {\n            console.error(\"Error updating password:\", error);\n            await (0,_components_ui_ConfirmationDialog__WEBPACK_IMPORTED_MODULE_7__.showAlert)({\n                title: \"Error\",\n                message: \"Failed to update password\",\n                variant: \"danger\"\n            });\n        } finally{\n            setUpdatingPassword(false);\n        }\n    };\n    const handleViewReferralTree = async (userId)=>{\n        try {\n            const user = users.find((u)=>u.id === userId);\n            if (!user) return;\n            setSelectedUserInfo(user);\n            setShowReferralTree(true);\n            setTreeLoading(true);\n            const response = await fetch(\"/api/admin/network-tree?rootUserId=\".concat(userId, \"&maxDepth=10&lazy=true\"));\n            const result = await response.json();\n            if (!response.ok) {\n                throw new Error(result.error || \"Failed to load referral tree\");\n            }\n            setSelectedUserTree(result.data);\n        } catch (error) {\n            console.error(\"Error loading referral tree:\", error);\n            alert(\"Failed to load referral tree\");\n        } finally{\n            setTreeLoading(false);\n        }\n    };\n    // Users are already filtered server-side, no need for client-side filtering\n    const filteredUsers = users;\n    const totalPages = Math.ceil(totalUsers / usersPerPage);\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_AdminLayout__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-pulse\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-8 bg-gray-300 rounded w-1/4 mb-6\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                        lineNumber: 350,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-xl shadow-sm border border-gray-200\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-6 border-b border-gray-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-10 bg-gray-300 rounded w-1/3\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                    lineNumber: 353,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                lineNumber: 352,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"divide-y divide-gray-200\",\n                                children: Array.from({\n                                    length: 10\n                                }).map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"h-10 w-10 bg-gray-300 rounded-full\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                                    lineNumber: 359,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"h-4 bg-gray-300 rounded w-1/4 mb-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                                            lineNumber: 361,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"h-3 bg-gray-300 rounded w-1/3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                                            lineNumber: 362,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                                    lineNumber: 360,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                            lineNumber: 358,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, i, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                        lineNumber: 357,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                lineNumber: 355,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                        lineNumber: 351,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                lineNumber: 349,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n            lineNumber: 348,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_AdminLayout__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-2xl font-bold text-gray-900\",\n                                    children: \"Users Management\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                    lineNumber: 380,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600\",\n                                    children: \"Manage user accounts and permissions\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                    lineNumber: 381,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                            lineNumber: 379,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2 text-sm text-gray-500\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_Ban_Calendar_Mail_MoreVertical_Network_Phone_Search_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                    lineNumber: 384,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: [\n                                        totalUsers,\n                                        \" total users\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                    lineNumber: 385,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                            lineNumber: 383,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                    lineNumber: 378,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-xl shadow-sm border border-gray-200 p-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col sm:flex-row gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_Ban_Calendar_Mail_MoreVertical_Network_Phone_Search_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                            lineNumber: 394,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"text\",\n                                            placeholder: \"Search users by name or email...\",\n                                            value: searchTerm,\n                                            onChange: (e)=>setSearchTerm(e.target.value),\n                                            className: \"w-full pl-10 pr-10 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-transparent\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                            lineNumber: 395,\n                                            columnNumber: 17\n                                        }, this),\n                                        searching && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute right-3 top-1/2 transform -translate-y-1/2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-primary-blue\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                                lineNumber: 404,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                            lineNumber: 403,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                    lineNumber: 393,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                lineNumber: 392,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"sm:w-48\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                    value: selectedRole,\n                                    onChange: (e)=>setSelectedRole(e.target.value),\n                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-transparent\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"all\",\n                                            children: \"All Roles\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                            lineNumber: 415,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"user\",\n                                            children: \"Users\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                            lineNumber: 416,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"admin\",\n                                            children: \"Admins\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                            lineNumber: 417,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                    lineNumber: 410,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                lineNumber: 409,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                        lineNumber: 391,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                    lineNumber: 390,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-xl shadow-sm border border-gray-200\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"overflow-x-auto\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                            className: \"w-full\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                    className: \"bg-gray-50 border-b border-gray-200\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                children: \"User\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                                lineNumber: 429,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                children: \"Role\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                                lineNumber: 432,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                children: \"Referral Rank\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                                lineNumber: 435,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                children: \"Status\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                                lineNumber: 438,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                children: \"Wallet Balance\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                                lineNumber: 441,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                children: \"Gift System\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                                lineNumber: 444,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                children: \"Joined\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                                lineNumber: 447,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                                                children: \"Actions\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                                lineNumber: 450,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                        lineNumber: 428,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                    lineNumber: 427,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                    className: \"divide-y divide-gray-200\",\n                                    children: filteredUsers.map((user)=>{\n                                        var _user_full_name, _user_email;\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                            className: \"hover:bg-gray-50 relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"px-6 py-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"relative\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"h-10 w-10 rounded-full overflow-hidden\",\n                                                                        children: user.avatar_url ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                            src: user.avatar_url,\n                                                                            alt: \"Profile picture\",\n                                                                            className: \"w-full h-full object-cover object-center\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                                                            lineNumber: 463,\n                                                                            columnNumber: 31\n                                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-full h-full bg-primary-blue/10 flex items-center justify-center\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-sm font-medium text-primary-blue\",\n                                                                                children: ((_user_full_name = user.full_name) === null || _user_full_name === void 0 ? void 0 : _user_full_name.charAt(0)) || ((_user_email = user.email) === null || _user_email === void 0 ? void 0 : _user_email.charAt(0)) || \"U\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                                                                lineNumber: 470,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                                                            lineNumber: 469,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                                                        lineNumber: 461,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"absolute -bottom-1 -right-1\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ReferralRankBadge__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                            userType: user.user_type,\n                                                                            size: \"sm\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                                                            lineNumber: 478,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                                                        lineNumber: 477,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                                                lineNumber: 460,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"ml-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-sm font-medium text-gray-900\",\n                                                                        children: user.full_name || \"No name\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                                                        lineNumber: 485,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-sm text-gray-500 flex items-center\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_Ban_Calendar_Mail_MoreVertical_Network_Phone_Search_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                                className: \"h-3 w-3 mr-1\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                                                                lineNumber: 489,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            user.email\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                                                        lineNumber: 488,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    user.phone && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-sm text-gray-500 flex items-center\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_Ban_Calendar_Mail_MoreVertical_Network_Phone_Search_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                                className: \"h-3 w-3 mr-1\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                                                                lineNumber: 494,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            user.phone\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                                                        lineNumber: 493,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                                                lineNumber: 484,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                                        lineNumber: 459,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                                    lineNumber: 458,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"px-6 py-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium \".concat(user.role === \"admin\" || user.is_super_admin ? \"bg-purple-100 text-purple-800\" : \"bg-gray-100 text-gray-800\"),\n                                                        children: user.is_super_admin ? \"Super Admin\" : user.role || \"User\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                                        lineNumber: 502,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                                    lineNumber: 501,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"px-6 py-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_ReferralRankBadge__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                        userType: user.user_type,\n                                                        showLabel: true,\n                                                        size: \"sm\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                                        lineNumber: 511,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                                    lineNumber: 510,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"px-6 py-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium \".concat(user.banned_until ? \"bg-red-100 text-red-800\" : \"bg-green-100 text-green-800\"),\n                                                        children: user.banned_until ? \"Banned\" : \"Active\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                                        lineNumber: 518,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                                    lineNumber: 517,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"px-6 py-4 text-sm text-gray-900\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium\",\n                                                            children: [\n                                                                \"Rs \",\n                                                                (user.wallet_balance || 0).toLocaleString()\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                                            lineNumber: 528,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                                        lineNumber: 527,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                                    lineNumber: 526,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"px-6 py-4 text-sm text-gray-900\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_Ban_Calendar_Mail_MoreVertical_Network_Phone_Search_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                className: \"h-3 w-3 mr-1 text-pink-500\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                                                lineNumber: 535,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium text-pink-600\",\n                                                                children: [\n                                                                    \"Rs \",\n                                                                    (giftSystemData.get(user.id) || 0).toLocaleString()\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                                                lineNumber: 536,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                                        lineNumber: 534,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                                    lineNumber: 533,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"px-6 py-4 text-sm text-gray-500\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_Ban_Calendar_Mail_MoreVertical_Network_Phone_Search_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                className: \"h-3 w-3 mr-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                                                lineNumber: 543,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            new Date(user.created_at).toLocaleDateString()\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                                        lineNumber: 542,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                                    lineNumber: 541,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                    className: \"px-6 py-4\",\n                                                    children: !user.is_super_admin && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(UserActions, {\n                                                        user: user,\n                                                        onRoleUpdate: handleRoleUpdate,\n                                                        onBanUser: handleBanUser,\n                                                        onViewTree: handleViewReferralTree,\n                                                        onViewUserData: handleViewUserData,\n                                                        onUpdatePassword: handleUpdatePassword\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                                        lineNumber: 549,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                                    lineNumber: 547,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, user.id, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                            lineNumber: 457,\n                                            columnNumber: 19\n                                        }, this);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                    lineNumber: 455,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                            lineNumber: 426,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                        lineNumber: 425,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                    lineNumber: 424,\n                    columnNumber: 9\n                }, this),\n                totalPages > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm text-gray-700\",\n                            children: [\n                                \"Showing \",\n                                (currentPage - 1) * usersPerPage + 1,\n                                \" to \",\n                                Math.min(currentPage * usersPerPage, totalUsers),\n                                \" of \",\n                                totalUsers,\n                                \" users\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                            lineNumber: 569,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setCurrentPage((prev)=>Math.max(prev - 1, 1)),\n                                    disabled: currentPage === 1,\n                                    className: \"px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                    children: \"Previous\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                    lineNumber: 573,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setCurrentPage((prev)=>Math.min(prev + 1, totalPages)),\n                                    disabled: currentPage === totalPages,\n                                    className: \"px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                    children: \"Next\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                    lineNumber: 580,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                            lineNumber: 572,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                    lineNumber: 568,\n                    columnNumber: 11\n                }, this),\n                showReferralTree && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg w-full max-w-6xl h-5/6 flex flex-col\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between p-6 border-b border-gray-200\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_Ban_Calendar_Mail_MoreVertical_Network_Phone_Search_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"h-6 w-6 text-blue-500\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                                lineNumber: 597,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-xl font-semibold text-gray-900\",\n                                                        children: \"Referral Tree\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                                        lineNumber: 599,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    selectedUserInfo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: [\n                                                            selectedUserInfo.full_name || \"N/A\",\n                                                            \" (\",\n                                                            selectedUserInfo.email,\n                                                            \")\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                                        lineNumber: 601,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                                lineNumber: 598,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                        lineNumber: 596,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>{\n                                            setShowReferralTree(false);\n                                            setSelectedUserTree(null);\n                                            setSelectedUserInfo(null);\n                                        },\n                                        className: \"text-gray-400 hover:text-gray-600\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_Ban_Calendar_Mail_MoreVertical_Network_Phone_Search_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                            className: \"h-6 w-6\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                            lineNumber: 615,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                        lineNumber: 607,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                lineNumber: 595,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 overflow-hidden\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(EnhancedReferralTree, {\n                                    rootNode: selectedUserTree,\n                                    loading: treeLoading,\n                                    onNodeClick: (user)=>{\n                                        console.log(\"Node clicked:\", user);\n                                    // You can add more functionality here like showing user details\n                                    },\n                                    className: \"h-full\",\n                                    onRefresh: ()=>handleViewReferralTree((selectedUserInfo === null || selectedUserInfo === void 0 ? void 0 : selectedUserInfo.id) || \"\"),\n                                    isRefreshing: treeLoading\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                    lineNumber: 620,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                lineNumber: 619,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-6 border-t border-gray-200\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-gray-600\",\n                                            children: \"Click on nodes to view details • Expand/collapse using the arrow buttons\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                            lineNumber: 635,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>{\n                                                setShowReferralTree(false);\n                                                setSelectedUserTree(null);\n                                                setSelectedUserInfo(null);\n                                            },\n                                            className: \"px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50\",\n                                            children: \"Close\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                            lineNumber: 638,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                    lineNumber: 634,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                lineNumber: 633,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                        lineNumber: 594,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                    lineNumber: 593,\n                    columnNumber: 11\n                }, this),\n                showUserDataModal && selectedUserData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_admin_ViewUserDataModal__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    isOpen: showUserDataModal,\n                    onClose: ()=>{\n                        setShowUserDataModal(false);\n                        setSelectedUserData(null);\n                    },\n                    user: selectedUserData\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                    lineNumber: 656,\n                    columnNumber: 11\n                }, this),\n                showPasswordModal && selectedUserForPassword && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg p-6 w-full max-w-md mx-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-gray-900\",\n                                        children: \"Update Password\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                        lineNumber: 671,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>{\n                                            setShowPasswordModal(false);\n                                            setSelectedUserForPassword(null);\n                                            setNewPassword(\"\");\n                                        },\n                                        className: \"text-gray-400 hover:text-gray-600\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_Ban_Calendar_Mail_MoreVertical_Network_Phone_Search_Shield_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                            className: \"h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                            lineNumber: 682,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                        lineNumber: 674,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                lineNumber: 670,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600 mb-2\",\n                                        children: [\n                                            \"Update password for: \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium\",\n                                                children: selectedUserForPassword.email\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                                lineNumber: 688,\n                                                columnNumber: 40\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                        lineNumber: 687,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600 mb-4\",\n                                        children: [\n                                            \"User: \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium\",\n                                                children: selectedUserForPassword.full_name || \"N/A\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                                lineNumber: 691,\n                                                columnNumber: 25\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                        lineNumber: 690,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                lineNumber: 686,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                        children: \"New Password\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                        lineNumber: 696,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"password\",\n                                        value: newPassword,\n                                        onChange: (e)=>setNewPassword(e.target.value),\n                                        className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                        placeholder: \"Enter new password (min 6 characters)\",\n                                        minLength: 6\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                        lineNumber: 699,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                lineNumber: 695,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-end space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>{\n                                            setShowPasswordModal(false);\n                                            setSelectedUserForPassword(null);\n                                            setNewPassword(\"\");\n                                        },\n                                        className: \"px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50\",\n                                        disabled: updatingPassword,\n                                        children: \"Cancel\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                        lineNumber: 710,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handlePasswordUpdate,\n                                        disabled: !newPassword || newPassword.length < 6 || updatingPassword,\n                                        className: \"px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center\",\n                                        children: updatingPassword ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                                    lineNumber: 728,\n                                                    columnNumber: 23\n                                                }, this),\n                                                \"Updating...\"\n                                            ]\n                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Key_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"h-4 w-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                                    lineNumber: 733,\n                                                    columnNumber: 23\n                                                }, this),\n                                                \"Update Password\"\n                                            ]\n                                        }, void 0, true)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                        lineNumber: 721,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                                lineNumber: 709,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                        lineNumber: 669,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n                    lineNumber: 668,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n            lineNumber: 376,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\admin\\\\users\\\\page.tsx\",\n        lineNumber: 375,\n        columnNumber: 5\n    }, this);\n}\n_s1(AdminUsers, \"kfxCuFH1WbJhpGrNfRNE+TBXMLk=\");\n_c2 = AdminUsers;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"EnhancedReferralTree\");\n$RefreshReg$(_c1, \"UserActions\");\n$RefreshReg$(_c2, \"AdminUsers\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/admin/users/page.tsx\n"));

/***/ })

});