'use client'

import { useState, useEffect } from 'react'
import { Upload, User, Mail, Phone, MapPin, FileText, Save, Trash2, AlertCircle } from 'lucide-react'
import { useAuth } from '@/contexts/AuthContext'
import { supabase } from '@/lib/supabase'

interface InheritanceData {
  id?: string
  full_name: string
  address: string
  relationship: string
  email: string
  phone: string
  id_document_type: 'national_id' | 'passport' | 'driving_license'
  id_document_front_url?: string
  id_document_back_url?: string
  passport_front_url?: string
  driving_license_front_url?: string
  driving_license_back_url?: string
  notes?: string
  status: 'active' | 'inactive'
}

const RELATIONSHIP_OPTIONS = [
  'Spouse',
  'Child',
  'Parent',
  'Sibling',
  'Grandparent',
  'Grandchild',
  'Other Family Member',
  'Friend',
  'Legal Guardian',
  'Other'
]

export default function InheritanceTab() {
  const { user } = useAuth()
  const [formData, setFormData] = useState<InheritanceData>({
    full_name: '',
    address: '',
    relationship: '',
    email: '',
    phone: '',
    id_document_type: 'national_id',
    notes: '',
    status: 'active'
  })
  const [loading, setLoading] = useState(false)
  const [saving, setSaving] = useState(false)
  const [error, setError] = useState('')
  const [success, setSuccess] = useState('')
  const [uploadingFiles, setUploadingFiles] = useState<Record<string, boolean>>({})
  const [isLocked, setIsLocked] = useState(false)

  useEffect(() => {
    if (user) {
      fetchInheritanceData()
    }
  }, [user])

  const fetchInheritanceData = async () => {
    if (!user) return

    setLoading(true)
    try {
      const { data, error } = await supabase
        .from('user_inheritance')
        .select('*')
        .eq('user_id', user.id)
        .single()

      if (error && error.code !== 'PGRST116') { // PGRST116 = no rows returned
        throw error
      }

      if (data) {
        setFormData(data)
        setIsLocked(true) // Lock the form if data already exists
      }
    } catch (err) {
      console.error('Error fetching inheritance data:', err)
      setError('Failed to load inheritance data')
    } finally {
      setLoading(false)
    }
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))
  }

  const uploadFile = async (file: File, path: string): Promise<string> => {
    const fileExt = file.name.split('.').pop()
    const fileName = `${user!.id}/${path}_${Date.now()}.${fileExt}`
    
    const { data, error } = await supabase.storage
      .from('inheritance-documents')
      .upload(fileName, file)

    if (error) throw error

    const { data: { publicUrl } } = supabase.storage
      .from('inheritance-documents')
      .getPublicUrl(fileName)

    return publicUrl
  }

  const handleFileUpload = async (e: React.ChangeEvent<HTMLInputElement>, fieldName: string) => {
    const file = e.target.files?.[0]
    if (!file || !user) return

    // Validate file type
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'application/pdf']
    if (!allowedTypes.includes(file.type)) {
      setError('Please upload only JPEG, PNG, WebP, or PDF files')
      return
    }

    // Validate file size (5MB max)
    if (file.size > 5 * 1024 * 1024) {
      setError('File size must be less than 5MB')
      return
    }

    setUploadingFiles(prev => ({ ...prev, [fieldName]: true }))
    setError('')

    try {
      const url = await uploadFile(file, fieldName)
      setFormData(prev => ({
        ...prev,
        [fieldName]: url
      }))
      setSuccess('File uploaded successfully')
    } catch (err) {
      console.error('Error uploading file:', err)
      setError('Failed to upload file')
    } finally {
      setUploadingFiles(prev => ({ ...prev, [fieldName]: false }))
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!user) return

    setSaving(true)
    setError('')
    setSuccess('')

    try {
      const dataToSave = {
        ...formData,
        user_id: user.id
      }

      if (formData.id) {
        // Update existing record
        const { error } = await supabase
          .from('user_inheritance')
          .update(dataToSave)
          .eq('id', formData.id)

        if (error) throw error
      } else {
        // Insert new record
        const { data, error } = await supabase
          .from('user_inheritance')
          .insert([dataToSave])
          .select()
          .single()

        if (error) throw error
        setFormData(prev => ({ ...prev, id: data.id }))
      }

      setIsLocked(true) // Lock the form after successful submission
      setSuccess('Inheritance Activated! Your information has been saved successfully.')
    } catch (err) {
      console.error('Error saving inheritance data:', err)
      setError('Failed to save inheritance information')
    } finally {
      setSaving(false)
    }
  }

  const handleDelete = async () => {
    if (!user || !formData.id) return

    if (!confirm('Are you sure you want to delete your inheritance information? This action cannot be undone.')) {
      return
    }

    setSaving(true)
    setError('')

    try {
      const { error } = await supabase
        .from('user_inheritance')
        .delete()
        .eq('id', formData.id)

      if (error) throw error

      // Reset form
      setFormData({
        full_name: '',
        address: '',
        relationship: '',
        email: '',
        phone: '',
        id_document_type: 'national_id',
        notes: '',
        status: 'active'
      })
      setIsLocked(false) // Unlock the form after deletion
      setSuccess('Inheritance information deleted successfully')
    } catch (err) {
      console.error('Error deleting inheritance data:', err)
      setError('Failed to delete inheritance information')
    } finally {
      setSaving(false)
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="max-w-4xl mx-auto">
      <div className="bg-white shadow-sm border border-gray-200 p-6">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h2 className="text-2xl font-bold text-gray-900">Inheritance Information</h2>
            <p className="text-gray-600 mt-1">
              Designate a person who should inherit your account in case of emergency
            </p>
          </div>
          {formData.id && (
            <button
              onClick={handleDelete}
              disabled={saving}
              className="flex items-center space-x-2 px-4 py-2 bg-red-600 text-white hover:bg-red-700 disabled:bg-red-300 transition-colors"
            >
              <Trash2 className="w-4 h-4" />
              <span>Delete</span>
            </button>
          )}
        </div>

        {/* Alert */}
        <div className="mb-6 p-4 bg-amber-50 border border-amber-200 flex items-start space-x-3">
          <AlertCircle className="w-5 h-5 text-amber-600 mt-0.5 flex-shrink-0" />
          <div className="text-sm text-amber-800">
            <p className="font-medium mb-1">Important Information</p>
            <p>
              This information will be used to transfer account ownership in case of emergency. 
              Please ensure all details are accurate and keep your nominee informed about this designation.
            </p>
          </div>
        </div>

        {/* Locked State Message */}
        {isLocked && (
          <div className="mb-6 p-4 bg-blue-50 border border-blue-200">
            <div className="flex items-center space-x-2">
              <AlertCircle className="w-5 h-5 text-blue-600" />
              <p className="text-blue-800 font-medium">Inheritance Activated!</p>
            </div>
            <p className="text-blue-600 text-sm mt-1">
              Your inheritance information has been saved and is now active. To make changes, please delete the current information first.
            </p>
          </div>
        )}

        {/* Success Message */}
        {success && (
          <div className="mb-6 p-4 bg-green-50 border border-green-200">
            <p className="text-green-600 text-sm">{success}</p>
          </div>
        )}

        {/* Error Message */}
        {error && (
          <div className="mb-6 p-4 bg-red-50 border border-red-200">
            <p className="text-red-600 text-sm">{error}</p>
          </div>
        )}

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Personal Information Section */}
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Nominee Personal Information</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Full Name */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Full Name *
                </label>
                <div className="relative">
                  <User className="absolute left-3 top-3 h-5 w-5 text-gray-400" />
                  <input
                    type="text"
                    name="full_name"
                    value={formData.full_name}
                    onChange={handleInputChange}
                    required
                    disabled={isLocked}
                    className={`w-full pl-10 pr-4 py-3 border border-gray-300 focus:border-blue-500 focus:outline-none transition-colors ${isLocked ? 'bg-gray-100 cursor-not-allowed' : ''}`}
                    placeholder="Enter nominee's full name"
                  />
                </div>
              </div>

              {/* Relationship */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Relationship *
                </label>
                <select
                  name="relationship"
                  value={formData.relationship}
                  onChange={handleInputChange}
                  required
                  disabled={isLocked}
                  className={`w-full px-4 py-3 border border-gray-300 focus:border-blue-500 focus:outline-none transition-colors ${isLocked ? 'bg-gray-100 cursor-not-allowed' : ''}`}
                >
                  <option value="">Select relationship</option>
                  {RELATIONSHIP_OPTIONS.map(option => (
                    <option key={option} value={option}>{option}</option>
                  ))}
                </select>
              </div>
            </div>

            {/* Contact Information */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
              {/* Email */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Email Address *
                </label>
                <div className="relative">
                  <Mail className="absolute left-3 top-3 h-5 w-5 text-gray-400" />
                  <input
                    type="email"
                    name="email"
                    value={formData.email}
                    onChange={handleInputChange}
                    required
                    disabled={isLocked}
                    className={`w-full pl-10 pr-4 py-3 border border-gray-300 focus:border-blue-500 focus:outline-none transition-colors ${isLocked ? 'bg-gray-100 cursor-not-allowed' : ''}`}
                    placeholder="Enter nominee's email"
                  />
                </div>
              </div>

              {/* Phone */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Phone Number *
                </label>
                <div className="relative">
                  <Phone className="absolute left-3 top-3 h-5 w-5 text-gray-400" />
                  <input
                    type="tel"
                    name="phone"
                    value={formData.phone}
                    onChange={handleInputChange}
                    required
                    disabled={isLocked}
                    className={`w-full pl-10 pr-4 py-3 border border-gray-300 focus:border-blue-500 focus:outline-none transition-colors ${isLocked ? 'bg-gray-100 cursor-not-allowed' : ''}`}
                    placeholder="Enter nominee's phone number"
                  />
                </div>
              </div>
            </div>

            {/* Address */}
            <div className="mt-6">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Address *
              </label>
              <div className="relative">
                <MapPin className="absolute left-3 top-3 h-5 w-5 text-gray-400" />
                <textarea
                  name="address"
                  value={formData.address}
                  onChange={handleInputChange}
                  required
                  disabled={isLocked}
                  rows={3}
                  className={`w-full pl-10 pr-4 py-3 border border-gray-300 focus:border-blue-500 focus:outline-none transition-colors resize-none ${isLocked ? 'bg-gray-100 cursor-not-allowed' : ''}`}
                  placeholder="Enter nominee's full address"
                />
              </div>
            </div>
          </div>

          {/* Document Upload Section */}
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Identity Documents</h3>

            {/* Document Type Selection */}
            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Primary ID Document Type *
              </label>
              <select
                name="id_document_type"
                value={formData.id_document_type}
                onChange={handleInputChange}
                required
                disabled={isLocked}
                className={`w-full px-4 py-3 border border-gray-300 focus:border-blue-500 focus:outline-none transition-colors ${isLocked ? 'bg-gray-100 cursor-not-allowed' : ''}`}
              >
                <option value="national_id">National ID</option>
                <option value="passport">Passport</option>
                <option value="driving_license">Driving License</option>
              </select>
            </div>

            {/* Document Upload Fields */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* National ID or Driving License - Front */}
              {(formData.id_document_type === 'national_id' || formData.id_document_type === 'driving_license') && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    {formData.id_document_type === 'national_id' ? 'National ID' : 'Driving License'} - Front *
                  </label>
                  <div className="border-2 border-dashed border-gray-300 p-4 text-center hover:border-blue-400 transition-colors">
                    {formData.id_document_front_url ? (
                      <div className="space-y-2">
                        <FileText className="w-8 h-8 text-green-600 mx-auto" />
                        <p className="text-sm text-green-600">Document uploaded</p>
                        <button
                          type="button"
                          onClick={() => window.open(formData.id_document_front_url, '_blank')}
                          className="text-blue-600 hover:text-blue-700 text-sm underline"
                        >
                          View Document
                        </button>
                      </div>
                    ) : (
                      <div className="space-y-2">
                        <Upload className="w-8 h-8 text-gray-400 mx-auto" />
                        <p className="text-sm text-gray-600">Upload front side</p>
                      </div>
                    )}
                    <input
                      type="file"
                      accept="image/*,.pdf"
                      onChange={(e) => handleFileUpload(e, 'id_document_front_url')}
                      className="mt-2 w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
                      disabled={uploadingFiles.id_document_front_url || isLocked}
                    />
                    {uploadingFiles.id_document_front_url && (
                      <p className="text-sm text-blue-600 mt-2">Uploading...</p>
                    )}
                  </div>
                </div>
              )}

              {/* National ID or Driving License - Back */}
              {(formData.id_document_type === 'national_id' || formData.id_document_type === 'driving_license') && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    {formData.id_document_type === 'national_id' ? 'National ID' : 'Driving License'} - Back *
                  </label>
                  <div className="border-2 border-dashed border-gray-300 p-4 text-center hover:border-blue-400 transition-colors">
                    {formData.id_document_back_url ? (
                      <div className="space-y-2">
                        <FileText className="w-8 h-8 text-green-600 mx-auto" />
                        <p className="text-sm text-green-600">Document uploaded</p>
                        <button
                          type="button"
                          onClick={() => window.open(formData.id_document_back_url, '_blank')}
                          className="text-blue-600 hover:text-blue-700 text-sm underline"
                        >
                          View Document
                        </button>
                      </div>
                    ) : (
                      <div className="space-y-2">
                        <Upload className="w-8 h-8 text-gray-400 mx-auto" />
                        <p className="text-sm text-gray-600">Upload back side</p>
                      </div>
                    )}
                    <input
                      type="file"
                      accept="image/*,.pdf"
                      onChange={(e) => handleFileUpload(e, 'id_document_back_url')}
                      className="mt-2 w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
                      disabled={uploadingFiles.id_document_back_url || isLocked}
                    />
                    {uploadingFiles.id_document_back_url && (
                      <p className="text-sm text-blue-600 mt-2">Uploading...</p>
                    )}
                  </div>
                </div>
              )}

              {/* Passport - Front Only */}
              {formData.id_document_type === 'passport' && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Passport - Front *
                  </label>
                  <div className="border-2 border-dashed border-gray-300 p-4 text-center hover:border-blue-400 transition-colors">
                    {formData.passport_front_url ? (
                      <div className="space-y-2">
                        <FileText className="w-8 h-8 text-green-600 mx-auto" />
                        <p className="text-sm text-green-600">Document uploaded</p>
                        <button
                          type="button"
                          onClick={() => window.open(formData.passport_front_url, '_blank')}
                          className="text-blue-600 hover:text-blue-700 text-sm underline"
                        >
                          View Document
                        </button>
                      </div>
                    ) : (
                      <div className="space-y-2">
                        <Upload className="w-8 h-8 text-gray-400 mx-auto" />
                        <p className="text-sm text-gray-600">Upload passport front</p>
                      </div>
                    )}
                    <input
                      type="file"
                      accept="image/*,.pdf"
                      onChange={(e) => handleFileUpload(e, 'passport_front_url')}
                      className="mt-2 w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
                      disabled={uploadingFiles.passport_front_url || isLocked}
                    />
                    {uploadingFiles.passport_front_url && (
                      <p className="text-sm text-blue-600 mt-2">Uploading...</p>
                    )}
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Additional Notes */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Additional Notes (Optional)
            </label>
            <textarea
              name="notes"
              value={formData.notes || ''}
              onChange={handleInputChange}
              rows={4}
              disabled={isLocked}
              className={`w-full px-4 py-3 border border-gray-300 focus:border-blue-500 focus:outline-none transition-colors resize-none ${isLocked ? 'bg-gray-100 cursor-not-allowed' : ''}`}
              placeholder="Any additional information about the nominee or special instructions..."
            />
          </div>

          {/* Submit Button */}
          <div className="flex justify-end">
            <button
              type="submit"
              disabled={saving || isLocked}
              className="flex items-center space-x-2 px-6 py-3 bg-blue-600 text-white hover:bg-blue-700 disabled:bg-blue-300 transition-colors"
            >
              <Save className="w-4 h-4" />
              <span>{saving ? 'Saving...' : isLocked ? 'Information Locked' : 'Save Inheritance Information'}</span>
            </button>
          </div>
        </form>
      </div>
    </div>
  )
}
