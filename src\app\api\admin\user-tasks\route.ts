import { NextRequest, NextResponse } from "next/server"
import { GiftTasksService } from "@/lib/services/giftTasksService"
import { AdminService } from "@/lib/services/admin"

export async function GET(request: NextRequest) {
  try {
    // Check admin authentication
    const isAdmin = await AdminService.isAdminServerSide()
    if (!isAdmin) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const isActive = searchParams.get("is_active")
    const taskType = searchParams.get("task_type")
    const targetUserType = searchParams.get("target_user_type")
    const limit = parseInt(searchParams.get("limit") || "50")
    const offset = parseInt(searchParams.get("offset") || "0")

    const filters: any = { limit, offset }
    
    if (isActive !== null) {
      filters.isActive = isActive === "true"
    }
    
    if (taskType) {
      filters.taskType = taskType
    }
    
    if (targetUserType) {
      filters.targetUserType = targetUserType
    }

    const result = await GiftTasksService.getAllTasks(filters)

    return NextResponse.json({
      success: true,
      data: result
    })
  } catch (error) {
    console.error("GET /api/admin/user-tasks error:", error)
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : "Failed to fetch tasks" 
      },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    // Check admin authentication and get user
    const { cookies } = await import('next/headers')
    const cookieStore = cookies()

    const { createServerClient } = await import('@supabase/ssr')
    const supabaseAuth = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value
          },
        },
      }
    )

    const { data: { user }, error: authError } = await supabaseAuth.auth.getUser()

    if (authError || !user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    // Verify admin status
    const isAdmin = await AdminService.isAdminServerSide()
    if (!isAdmin) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const body = await request.json()

    // Validate required fields (excluding created_by as we'll set it)
    const requiredFields = ["title", "task_type", "target_user_types", "requirements", "reward_amount", "reward_type"]
    for (const field of requiredFields) {
      if (!body[field]) {
        return NextResponse.json(
          { success: false, error: `Missing required field: ${field}` },
          { status: 400 }
        )
      }
    }

    // Set the created_by field to the current admin user
    const taskData = {
      ...body,
      created_by: user.id
    }

    const task = await GiftTasksService.createTask(taskData)

    return NextResponse.json({
      success: true,
      message: "Task created successfully",
      data: task
    })
  } catch (error) {
    console.error("POST /api/admin/user-tasks error:", error)
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : "Failed to create task" 
      },
      { status: 500 }
    )
  }
}
