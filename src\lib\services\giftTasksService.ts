import { supabase, supabaseAdmin } from "@/lib/supabase"

export interface UserTask {
  id: string
  title: string
  description?: string
  task_type: "sales_target" | "referral_target" | "custom"
  target_user_types: string[]
  requirements: Record<string, any>
  reward_amount: number
  reward_type: "normal_present" | "annual_present" | "bonus"
  is_active: boolean
  created_by?: string
  created_at: string
  updated_at: string
  expires_at?: string
}

export interface UserTaskAssignment {
  id: string
  user_id: string
  task_id: string
  status: "assigned" | "in_progress" | "completed" | "expired"
  progress: Record<string, any>
  assigned_at: string
  started_at?: string
  completed_at?: string
  reward_claimed_at?: string
  task?: UserTask
}

export interface GiftTransaction {
  id: string
  transaction_id: string
  user_id: string
  task_id?: string
  present_allocation_id?: string
  gift_type: "task_reward" | "annual_present" | "bonus_gift" | "manual_gift"
  gift_amount: number
  gift_description?: string
  status: "pending" | "processed" | "failed" | "cancelled"
  processed_at?: string
  processed_by?: string
  wallet_transaction_id?: string
  metadata: Record<string, any>
  created_at: string
  updated_at: string
}

export class GiftTasksService {
  /**
   * Create a new user task (admin-only)
   */
  static async createTask(taskData: {
    title: string
    description?: string
    task_type: "sales_target" | "referral_target" | "custom"
    target_user_types: string[]
    requirements: Record<string, any>
    reward_amount: number
    reward_type: "normal_present" | "annual_present" | "bonus"
    expires_at?: string
    created_by: string
  }): Promise<UserTask> {
    try {
      const { data, error } = await supabaseAdmin
        .from("user_tasks")
        .insert({
          title: taskData.title,
          description: taskData.description,
          task_type: taskData.task_type,
          target_user_types: taskData.target_user_types,
          requirements: taskData.requirements,
          reward_amount: taskData.reward_amount,
          reward_type: taskData.reward_type,
          expires_at: taskData.expires_at,
          created_by: taskData.created_by,
          is_active: true
        })
        .select()
        .single()

      if (error) {
        throw new Error(`Failed to create task: ${error.message}`)
      }

      return data
    } catch (error) {
      console.error("GiftTasksService.createTask:", error)
      throw error
    }
  }

  /**
   * Get all tasks with filters (admin-only)
   */
  static async getAllTasks(filters: {
    isActive?: boolean
    taskType?: string
    targetUserType?: string
    limit?: number
    offset?: number
  } = {}): Promise<{
    tasks: UserTask[]
    total: number
  }> {
    try {
      const { isActive, taskType, targetUserType, limit = 50, offset = 0 } = filters

      let query = supabaseAdmin
        .from("user_tasks")
        .select("*", { count: "exact" })
        .order("created_at", { ascending: false })
        .range(offset, offset + limit - 1)

      if (isActive !== undefined) {
        query = query.eq("is_active", isActive)
      }

      if (taskType) {
        query = query.eq("task_type", taskType)
      }

      if (targetUserType) {
        query = query.contains("target_user_types", [targetUserType])
      }

      const { data, error, count } = await query

      if (error) {
        throw new Error(`Failed to get tasks: ${error.message}`)
      }

      return {
        tasks: data || [],
        total: count || 0
      }
    } catch (error) {
      console.error("GiftTasksService.getAllTasks:", error)
      throw error
    }
  }

  /**
   * Update a task (admin-only)
   */
  static async updateTask(
    taskId: string,
    updates: Partial<Omit<UserTask, "id" | "created_at" | "updated_at">>
  ): Promise<UserTask> {
    try {
      const { data, error } = await supabaseAdmin
        .from("user_tasks")
        .update(updates)
        .eq("id", taskId)
        .select()
        .single()

      if (error) {
        throw new Error(`Failed to update task: ${error.message}`)
      }

      return data
    } catch (error) {
      console.error("GiftTasksService.updateTask:", error)
      throw error
    }
  }

  /**
   * Delete a task (admin-only)
   */
  static async deleteTask(taskId: string): Promise<void> {
    try {
      const { error } = await supabaseAdmin
        .from("user_tasks")
        .delete()
        .eq("id", taskId)

      if (error) {
        throw new Error(`Failed to delete task: ${error.message}`)
      }
    } catch (error) {
      console.error("GiftTasksService.deleteTask:", error)
      throw error
    }
  }

  /**
   * Get user's assigned tasks
   */
  static async getUserTasks(
    userId: string,
    status?: string
  ): Promise<UserTaskAssignment[]> {
    try {
      if (!supabaseAdmin) {
        throw new Error("Admin client not available")
      }

      let query = supabaseAdmin
        .from("user_task_assignments")
        .select(`
          *,
          task:user_tasks(*)
        `)
        .eq("user_id", userId)
        .order("assigned_at", { ascending: false })

      if (status) {
        query = query.eq("status", status)
      }

      const { data, error } = await query

      if (error) {
        throw new Error(`Failed to get user tasks: ${error.message}`)
      }

      return data || []
    } catch (error) {
      console.error("GiftTasksService.getUserTasks:", error)
      throw error
    }
  }

  /**
   * Update task progress
   */
  static async updateTaskProgress(
    userId: string,
    taskId: string,
    progress: Record<string, any>,
    status?: "in_progress" | "completed"
  ): Promise<UserTaskAssignment> {
    try {
      const updateData: any = { progress }

      if (status) {
        updateData.status = status
        if (status === "in_progress" && !updateData.started_at) {
          updateData.started_at = new Date().toISOString()
        }
        if (status === "completed") {
          updateData.completed_at = new Date().toISOString()
        }
      }

      const { data, error } = await supabase
        .from("user_task_assignments")
        .update(updateData)
        .eq("user_id", userId)
        .eq("task_id", taskId)
        .select()
        .single()

      if (error) {
        throw new Error(`Failed to update task progress: ${error.message}`)
      }

      return data
    } catch (error) {
      console.error("GiftTasksService.updateTaskProgress:", error)
      throw error
    }
  }

  /**
   * Claim task reward
   */
  static async claimTaskReward(
    userId: string,
    taskId: string
  ): Promise<GiftTransaction> {
    try {
      if (!supabaseAdmin) {
        throw new Error("Admin client not available")
      }

      // Get task assignment
      const { data: assignment, error: assignmentError } = await supabaseAdmin
        .from("user_task_assignments")
        .select(`
          *,
          task:user_tasks(*)
        `)
        .eq("user_id", userId)
        .eq("task_id", taskId)
        .eq("status", "completed")
        .is("reward_claimed_at", null)
        .single()

      if (assignmentError || !assignment) {
        throw new Error("Task not completed or reward already claimed")
      }

      // Create gift transaction
      const transactionId = `TASK_${Date.now()}_${userId.substring(0, 8)}`

      const { data: giftTransaction, error: giftError } = await supabaseAdmin
        .from("gift_transactions")
        .insert({
          transaction_id: transactionId,
          user_id: userId,
          task_id: taskId,
          gift_type: "task_reward",
          gift_amount: assignment.task.reward_amount,
          gift_description: `Task reward: ${assignment.task.title}`,
          status: "pending",
          metadata: {
            task_type: assignment.task.task_type,
            reward_type: assignment.task.reward_type
          }
        })
        .select()
        .single()

      if (giftError) {
        throw new Error(`Failed to create gift transaction: ${giftError.message}`)
      }

      // Update assignment to mark reward as claimed
      const { error: updateError } = await supabaseAdmin
        .from("user_task_assignments")
        .update({ reward_claimed_at: new Date().toISOString() })
        .eq("user_id", userId)
        .eq("task_id", taskId)

      if (updateError) {
        throw new Error(`Failed to update assignment: ${updateError.message}`)
      }

      return giftTransaction
    } catch (error) {
      console.error("GiftTasksService.claimTaskReward:", error)
      throw error
    }
  }

  /**
   * Get task assignments with filters (admin-only)
   */
  static async getTaskAssignments(filters: {
    taskId?: string
    userId?: string
    status?: string
    limit?: number
    offset?: number
  } = {}): Promise<{
    assignments: (UserTaskAssignment & { user: { email: string; full_name?: string } })[]
    total: number
  }> {
    try {
      const { taskId, userId, status, limit = 50, offset = 0 } = filters

      let query = supabaseAdmin
        .from("user_task_assignments")
        .select(`
          *,
          task:user_tasks(*),
          user:users!user_task_assignments_user_id_fkey(email, full_name)
        `, { count: "exact" })
        .order("assigned_at", { ascending: false })
        .range(offset, offset + limit - 1)

      if (taskId) {
        query = query.eq("task_id", taskId)
      }

      if (userId) {
        query = query.eq("user_id", userId)
      }

      if (status) {
        query = query.eq("status", status)
      }

      const { data, error, count } = await query

      if (error) {
        throw new Error(`Failed to get task assignments: ${error.message}`)
      }

      return {
        assignments: data || [],
        total: count || 0
      }
    } catch (error) {
      console.error("GiftTasksService.getTaskAssignments:", error)
      throw error
    }
  }

  /**
   * Get gift transactions with filters
   */
  static async getGiftTransactions(filters: {
    userId?: string
    giftType?: string
    status?: string
    limit?: number
    offset?: number
  } = {}): Promise<{
    transactions: (GiftTransaction & { user?: { email: string; full_name?: string } })[]
    total: number
  }> {
    try {
      const { userId, giftType, status, limit = 50, offset = 0 } = filters

      let query = supabaseAdmin
        .from("gift_transactions")
        .select(`
          *,
          user:users!gift_transactions_user_id_fkey(email, full_name)
        `, { count: "exact" })
        .order("created_at", { ascending: false })
        .range(offset, offset + limit - 1)

      if (userId) {
        query = query.eq("user_id", userId)
      }

      if (giftType) {
        query = query.eq("gift_type", giftType)
      }

      if (status) {
        query = query.eq("status", status)
      }

      const { data, error, count } = await query

      if (error) {
        throw new Error(`Failed to get gift transactions: ${error.message}`)
      }

      return {
        transactions: data || [],
        total: count || 0
      }
    } catch (error) {
      console.error("GiftTasksService.getGiftTransactions:", error)
      throw error
    }
  }

  /**
   * Process gift transaction (admin-only)
   */
  static async processGiftTransaction(
    transactionId: string,
    adminId: string,
    walletTransactionId?: string
  ): Promise<GiftTransaction> {
    try {
      const { data, error } = await supabaseAdmin
        .from("gift_transactions")
        .update({
          status: "processed",
          processed_at: new Date().toISOString(),
          processed_by: adminId,
          wallet_transaction_id: walletTransactionId
        })
        .eq("id", transactionId)
        .select()
        .single()

      if (error) {
        throw new Error(`Failed to process gift transaction: ${error.message}`)
      }

      return data
    } catch (error) {
      console.error("GiftTasksService.processGiftTransaction:", error)
      throw error
    }
  }

  /**
   * Get task statistics (admin-only)
   */
  static async getTaskStatistics(): Promise<{
    totalTasks: number
    activeTasks: number
    totalAssignments: number
    completedAssignments: number
    pendingRewards: number
    totalRewardsDistributed: number
    byTaskType: Record<string, {
      count: number
      assignments: number
      completed: number
    }>
  }> {
    try {
      // Get task stats
      const { data: tasks, error: tasksError } = await supabaseAdmin
        .from("user_tasks")
        .select("task_type, is_active")

      if (tasksError) {
        throw new Error(`Failed to get task stats: ${tasksError.message}`)
      }

      // Get assignment stats
      const { data: assignments, error: assignmentsError } = await supabaseAdmin
        .from("user_task_assignments")
        .select(`
          status,
          task:user_tasks(task_type)
        `)

      if (assignmentsError) {
        throw new Error(`Failed to get assignment stats: ${assignmentsError.message}`)
      }

      // Get gift transaction stats
      const { data: gifts, error: giftsError } = await supabaseAdmin
        .from("gift_transactions")
        .select("status, gift_amount")
        .eq("gift_type", "task_reward")

      if (giftsError) {
        throw new Error(`Failed to get gift stats: ${giftsError.message}`)
      }

      const stats = {
        totalTasks: tasks.length,
        activeTasks: tasks.filter(t => t.is_active).length,
        totalAssignments: assignments.length,
        completedAssignments: assignments.filter(a => a.status === "completed").length,
        pendingRewards: gifts.filter(g => g.status === "pending").length,
        totalRewardsDistributed: gifts
          .filter(g => g.status === "processed")
          .reduce((sum, g) => sum + g.gift_amount, 0),
        byTaskType: {} as Record<string, { count: number; assignments: number; completed: number }>
      }

      // Calculate by task type
      tasks.forEach(task => {
        if (!stats.byTaskType[task.task_type]) {
          stats.byTaskType[task.task_type] = { count: 0, assignments: 0, completed: 0 }
        }
        stats.byTaskType[task.task_type].count++
      })

      assignments.forEach(assignment => {
        const taskType = assignment.task?.task_type
        if (taskType && stats.byTaskType[taskType]) {
          stats.byTaskType[taskType].assignments++
          if (assignment.status === "completed") {
            stats.byTaskType[taskType].completed++
          }
        }
      })

      return stats
    } catch (error) {
      console.error("GiftTasksService.getTaskStatistics:", error)
      throw error
    }
  }
}
