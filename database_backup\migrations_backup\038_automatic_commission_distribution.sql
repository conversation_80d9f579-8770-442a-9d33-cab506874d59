-- Automatic Commission Distribution
-- This migration modifies the commission system to automatically credit wallets
-- instead of creating pending transactions that need manual processing

-- Update the calculate_commission_distribution function to automatically credit wallets
CREATE OR REPLACE FUNCTION calculate_commission_distribution(
    purchaser_id UUID,
    package_id UUID,
    package_amount DECIMAL(12,2)
) RETURNS VOID AS $$
DECLARE
    commission_record RECORD;
    beneficiary_record RECORD;
    commission_amount DECIMAL(12,2);
    transaction_id_val VARCHAR(50);
    okdoi_head_id UUID;
    total_distributed DECIMAL(12,2) := 0;
    remaining_commission DECIMAL(12,2);
    beneficiary_wallet_id UUID;
    wallet_transaction_id UUID;
BEGIN
    -- Generate unique transaction ID
    transaction_id_val := 'COM-' || TO_CHAR(NOW(), 'YYYYMMDD') || '-' || UPPER(SUBSTRING(MD5(RANDOM()::TEXT) FROM 1 FOR 8));

    -- Get OKDOI Head user ID
    SELECT id INTO okdoi_head_id FROM users WHERE user_type = 'okdoi_head' LIMIT 1;

    -- Get commission structures for this package amount
    FOR commission_record IN
        SELECT * FROM commission_structure 
        WHERE package_value <= package_amount 
        AND is_active = true 
        ORDER BY package_value DESC
        LIMIT 1
    LOOP
        -- Process each level (1-10)
        FOR i IN 1..10 LOOP
            -- Get beneficiary at this level
            SELECT * INTO beneficiary_record FROM get_referral_upline(purchaser_id, i);
            
            -- Calculate commission amount for this level
            commission_amount := package_amount * 
                CASE i
                    WHEN 1 THEN commission_record.level_1_rate
                    WHEN 2 THEN commission_record.level_2_rate
                    WHEN 3 THEN commission_record.level_3_rate
                    WHEN 4 THEN commission_record.level_4_rate
                    WHEN 5 THEN commission_record.level_5_rate
                    WHEN 6 THEN commission_record.level_6_rate
                    WHEN 7 THEN commission_record.level_7_rate
                    WHEN 8 THEN commission_record.level_8_rate
                    WHEN 9 THEN commission_record.level_9_rate
                    WHEN 10 THEN commission_record.level_10_rate
                END;

            IF commission_amount > 0 THEN
                IF beneficiary_record.id IS NOT NULL THEN
                    -- Get beneficiary's wallet
                    SELECT id INTO beneficiary_wallet_id 
                    FROM user_wallets 
                    WHERE user_id = beneficiary_record.id;
                    
                    IF beneficiary_wallet_id IS NOT NULL THEN
                        -- Credit commission to wallet immediately
                        SELECT update_wallet_balance(
                            beneficiary_wallet_id,
                            commission_amount,
                            'commission',
                            'Commission - Level ' || i || ' (' || commission_record.commission_type || ')',
                            package_id,
                            'subscription_purchase'
                        ) INTO wallet_transaction_id;
                        
                        -- Create commission transaction record with 'processed' status
                        INSERT INTO commission_transactions (
                            transaction_id, user_id, beneficiary_id, subscription_purchase_id,
                            commission_type, commission_level, package_value, commission_rate,
                            commission_amount, status, wallet_transaction_id
                        ) VALUES (
                            transaction_id_val || '-L' || i, purchaser_id, beneficiary_record.id, package_id,
                            commission_record.commission_type, i, package_amount,
                            CASE i
                                WHEN 1 THEN commission_record.level_1_rate
                                WHEN 2 THEN commission_record.level_2_rate
                                WHEN 3 THEN commission_record.level_3_rate
                                WHEN 4 THEN commission_record.level_4_rate
                                WHEN 5 THEN commission_record.level_5_rate
                                WHEN 6 THEN commission_record.level_6_rate
                                WHEN 7 THEN commission_record.level_7_rate
                                WHEN 8 THEN commission_record.level_8_rate
                                WHEN 9 THEN commission_record.level_9_rate
                                WHEN 10 THEN commission_record.level_10_rate
                            END,
                            commission_amount, 'processed', wallet_transaction_id
                        );
                        
                        -- Update user's total commission earned
                        UPDATE users 
                        SET total_commission_earned = COALESCE(total_commission_earned, 0) + commission_amount,
                            updated_at = NOW()
                        WHERE id = beneficiary_record.id;
                        
                        total_distributed := total_distributed + commission_amount;
                    END IF;
                ELSE
                    -- No beneficiary at this level, goes to OKDOI Head
                    IF okdoi_head_id IS NOT NULL THEN
                        -- Get OKDOI Head's wallet
                        SELECT id INTO beneficiary_wallet_id 
                        FROM user_wallets 
                        WHERE user_id = okdoi_head_id;
                        
                        IF beneficiary_wallet_id IS NOT NULL THEN
                            -- Credit unallocated commission to OKDOI Head wallet
                            SELECT update_wallet_balance(
                                beneficiary_wallet_id,
                                commission_amount,
                                'commission',
                                'Unallocated Commission - Level ' || i || ' (' || commission_record.commission_type || ')',
                                package_id,
                                'subscription_purchase'
                            ) INTO wallet_transaction_id;
                            
                            -- Create commission transaction record
                            INSERT INTO commission_transactions (
                                transaction_id, user_id, beneficiary_id, subscription_purchase_id,
                                commission_type, commission_level, package_value, commission_rate,
                                commission_amount, status, wallet_transaction_id, metadata
                            ) VALUES (
                                transaction_id_val || '-L' || i || '-HEAD', purchaser_id, okdoi_head_id, package_id,
                                commission_record.commission_type || '_unallocated', i, package_amount,
                                CASE i
                                    WHEN 1 THEN commission_record.level_1_rate
                                    WHEN 2 THEN commission_record.level_2_rate
                                    WHEN 3 THEN commission_record.level_3_rate
                                    WHEN 4 THEN commission_record.level_4_rate
                                    WHEN 5 THEN commission_record.level_5_rate
                                    WHEN 6 THEN commission_record.level_6_rate
                                    WHEN 7 THEN commission_record.level_7_rate
                                    WHEN 8 THEN commission_record.level_8_rate
                                    WHEN 9 THEN commission_record.level_9_rate
                                    WHEN 10 THEN commission_record.level_10_rate
                                END,
                                commission_amount, 'processed', wallet_transaction_id,
                                '{"reason": "unallocated_level", "original_level": ' || i || '}'
                            );
                            
                            -- Update OKDOI Head's total commission earned
                            UPDATE users 
                            SET total_commission_earned = COALESCE(total_commission_earned, 0) + commission_amount,
                                updated_at = NOW()
                            WHERE id = okdoi_head_id;
                            
                            total_distributed := total_distributed + commission_amount;
                        END IF;
                    END IF;
                END IF;
            END IF;
        END LOOP;
    END LOOP;
END;
$$ LANGUAGE plpgsql;

-- Add wallet_transaction_id column to commission_transactions if it doesn't exist
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'commission_transactions' 
        AND column_name = 'wallet_transaction_id'
    ) THEN
        ALTER TABLE commission_transactions 
        ADD COLUMN wallet_transaction_id UUID REFERENCES wallet_transactions(id);
    END IF;
END $$;

-- Add comment for documentation
COMMENT ON FUNCTION calculate_commission_distribution(UUID, UUID, DECIMAL) IS 
'Automatically calculates and distributes commissions for subscription purchases. Credits wallets immediately and creates processed commission transaction records.';
