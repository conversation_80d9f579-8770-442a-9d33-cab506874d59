-- Commission Structure Management System
-- This migration creates tables and functions for managing commission structures

-- =====================================================
-- 1. COMMISSION STRUCTURE TABLE
-- =====================================================

CREATE TABLE commission_structure (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    commission_type VARCHAR(50) NOT NULL,
    package_value DECIMAL(12,2) NOT NULL,
    
    -- Commission rates for different levels (as percentages)
    direct_commission_rate DECIMAL(5,4) DEFAULT 0,
    level_commission_rate DECIMAL(5,4) DEFAULT 0,
    voucher_rate DECIMAL(5,4) DEFAULT 0,
    festival_bonus_rate DECIMAL(5,4) DEFAULT 0,
    saving_rate DECIMAL(5,4) DEFAULT 0,
    gift_center_rate DECIMAL(5,4) DEFAULT 0,
    entertainment_rate DECIMAL(5,4) DEFAULT 0,
    medical_rate DECIMAL(5,4) DEFAULT 0,
    education_rate DECIMAL(5,4) DEFAULT 0,
    credit_rate DECIMAL(5,4) DEFAULT 0,
    
    -- ZM specific rates
    zm_bonus_rate DECIMAL(5,4) DEFAULT 0,
    zm_petral_allowance_rate DECIMAL(5,4) DEFAULT 0,
    zm_leasing_facility_rate DECIMAL(5,4) DEFAULT 0,
    zm_phone_bill_rate DECIMAL(5,4) DEFAULT 0,
    
    -- RSM specific rates
    rsm_bonus_rate DECIMAL(5,4) DEFAULT 0,
    rsm_petral_allowance_rate DECIMAL(5,4) DEFAULT 0,
    rsm_leasing_facility_rate DECIMAL(5,4) DEFAULT 0,
    rsm_phone_bill_rate DECIMAL(5,4) DEFAULT 0,
    
    -- Additional fields
    zm_present_leader_rate DECIMAL(5,4) DEFAULT 0,
    rsm_present_leader_rate DECIMAL(5,4) DEFAULT 0,
    annual_present_leader_rate DECIMAL(5,4) DEFAULT 0,
    okdoi_head_rate DECIMAL(5,4) DEFAULT 0,
    
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    UNIQUE(commission_type, package_value)
);

-- =====================================================
-- 2. INSERT DEFAULT COMMISSION STRUCTURE DATA
-- =====================================================

-- Insert commission structure for different package values
INSERT INTO commission_structure (
    commission_type, package_value,
    direct_commission_rate, level_commission_rate, voucher_rate, festival_bonus_rate,
    saving_rate, gift_center_rate, entertainment_rate, medical_rate,
    education_rate, credit_rate, zm_bonus_rate, zm_petral_allowance_rate,
    zm_leasing_facility_rate, zm_phone_bill_rate, rsm_bonus_rate,
    rsm_petral_allowance_rate, rsm_leasing_facility_rate, rsm_phone_bill_rate,
    zm_present_leader_rate, rsm_present_leader_rate, annual_present_leader_rate,
    okdoi_head_rate
) VALUES 
-- Package 2000
('subscription', 2000, 0.10, 0.02, 0.015, 0.02, 0.015, 0.005, 0.003, 0.001, 0.001, 0.002, 0.05, 0.0075, 0.01, 0.0025, 0.05, 0.0075, 0.01, 0.0025, 0.01, 0.01, 0.015, 0.025),

-- Package 5000  
('subscription', 5000, 0.10, 0.02, 0.01, 0.01, 0.01, 0.004, 0.002, 0.001, 0.001, 0.001, 0.05, 0.005, 0.01, 0.001, 0.05, 0.005, 0.01, 0.001, 0.02, 0.013, 0.007, 0.02),

-- Package 10000
('subscription', 10000, 0.10, 0.02, 0.01, 0.01, 0.01, 0.005, 0.002, 0.001, 0.001, 0.001, 0.04, 0.005, 0.01, 0.001, 0.04, 0.005, 0.01, 0.001, 0.02, 0.012, 0.012, 0.02),

-- Package 50000
('subscription', 50000, 0.10, 0.02, 0.01, 0.01, 0.01, 0.005, 0.002, 0.001, 0.001, 0.001, 0.02, 0.005, 0.01, 0.001, 0.02, 0.005, 0.01, 0.001, 0.02, 0.01, 0.009, 0.02);

-- =====================================================
-- 3. COMMISSION CALCULATION FUNCTIONS
-- =====================================================

-- Function to get commission structure by package value
CREATE OR REPLACE FUNCTION get_commission_structure(p_package_value DECIMAL(12,2))
RETURNS commission_structure AS $$
DECLARE
    result commission_structure;
BEGIN
    SELECT * INTO result
    FROM commission_structure
    WHERE package_value = p_package_value
    AND commission_type = 'subscription'
    AND is_active = TRUE
    LIMIT 1;
    
    IF NOT FOUND THEN
        RAISE EXCEPTION 'Commission structure not found for package value %', p_package_value;
    END IF;
    
    RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to calculate commission amounts
CREATE OR REPLACE FUNCTION calculate_commission_amounts(
    p_package_value DECIMAL(12,2),
    p_user_type VARCHAR(20) DEFAULT 'user'
)
RETURNS TABLE (
    commission_type VARCHAR(50),
    amount DECIMAL(12,2)
) AS $$
DECLARE
    structure commission_structure;
BEGIN
    -- Get commission structure
    SELECT * INTO structure FROM get_commission_structure(p_package_value);
    
    -- Return base commissions for all users
    RETURN QUERY SELECT 'direct_commission'::VARCHAR(50), (structure.direct_commission_rate * p_package_value);
    RETURN QUERY SELECT 'level_commission'::VARCHAR(50), (structure.level_commission_rate * p_package_value);
    RETURN QUERY SELECT 'voucher'::VARCHAR(50), (structure.voucher_rate * p_package_value);
    RETURN QUERY SELECT 'festival_bonus'::VARCHAR(50), (structure.festival_bonus_rate * p_package_value);
    RETURN QUERY SELECT 'saving'::VARCHAR(50), (structure.saving_rate * p_package_value);
    RETURN QUERY SELECT 'gift_center'::VARCHAR(50), (structure.gift_center_rate * p_package_value);
    RETURN QUERY SELECT 'entertainment'::VARCHAR(50), (structure.entertainment_rate * p_package_value);
    RETURN QUERY SELECT 'medical'::VARCHAR(50), (structure.medical_rate * p_package_value);
    RETURN QUERY SELECT 'education'::VARCHAR(50), (structure.education_rate * p_package_value);
    RETURN QUERY SELECT 'credit'::VARCHAR(50), (structure.credit_rate * p_package_value);
    
    -- ZM specific commissions
    IF p_user_type = 'zonal_manager' THEN
        RETURN QUERY SELECT 'zm_bonus'::VARCHAR(50), (structure.zm_bonus_rate * p_package_value);
        RETURN QUERY SELECT 'zm_petral_allowance'::VARCHAR(50), (structure.zm_petral_allowance_rate * p_package_value);
        RETURN QUERY SELECT 'zm_leasing_facility'::VARCHAR(50), (structure.zm_leasing_facility_rate * p_package_value);
        RETURN QUERY SELECT 'zm_phone_bill'::VARCHAR(50), (structure.zm_phone_bill_rate * p_package_value);
        RETURN QUERY SELECT 'zm_present_leader'::VARCHAR(50), (structure.zm_present_leader_rate * p_package_value);
    END IF;
    
    -- RSM specific commissions
    IF p_user_type = 'rsm' THEN
        RETURN QUERY SELECT 'rsm_bonus'::VARCHAR(50), (structure.rsm_bonus_rate * p_package_value);
        RETURN QUERY SELECT 'rsm_petral_allowance'::VARCHAR(50), (structure.rsm_petral_allowance_rate * p_package_value);
        RETURN QUERY SELECT 'rsm_leasing_facility'::VARCHAR(50), (structure.rsm_leasing_facility_rate * p_package_value);
        RETURN QUERY SELECT 'rsm_phone_bill'::VARCHAR(50), (structure.rsm_phone_bill_rate * p_package_value);
        RETURN QUERY SELECT 'rsm_present_leader'::VARCHAR(50), (structure.rsm_present_leader_rate * p_package_value);
    END IF;
    
    -- OKDOI Head specific commissions
    IF p_user_type = 'okdoi_head' THEN
        RETURN QUERY SELECT 'okdoi_head_commission'::VARCHAR(50), (structure.okdoi_head_rate * p_package_value);
        RETURN QUERY SELECT 'annual_present_leader'::VARCHAR(50), (structure.annual_present_leader_rate * p_package_value);
    END IF;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- 4. INDEXES AND CONSTRAINTS
-- =====================================================

CREATE INDEX idx_commission_structure_package_value ON commission_structure(package_value);
CREATE INDEX idx_commission_structure_type ON commission_structure(commission_type);
CREATE INDEX idx_commission_structure_active ON commission_structure(is_active);

-- =====================================================
-- 5. RLS POLICIES
-- =====================================================

ALTER TABLE commission_structure ENABLE ROW LEVEL SECURITY;

-- Admins can manage commission structure
CREATE POLICY "Admins can manage commission structure" ON commission_structure
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM users 
            WHERE id = auth.uid() 
            AND (role = 'admin' OR is_super_admin = TRUE)
        )
    );

-- All authenticated users can view active commission structures
CREATE POLICY "Users can view active commission structures" ON commission_structure
    FOR SELECT USING (is_active = TRUE);

-- =====================================================
-- 6. GRANT PERMISSIONS
-- =====================================================

GRANT SELECT ON commission_structure TO authenticated;
GRANT ALL ON commission_structure TO service_role;
GRANT EXECUTE ON FUNCTION get_commission_structure(DECIMAL) TO authenticated;
GRANT EXECUTE ON FUNCTION calculate_commission_amounts(DECIMAL, VARCHAR) TO authenticated;

-- =====================================================
-- 7. COMMENTS
-- =====================================================

COMMENT ON TABLE commission_structure IS 'Stores commission structure rates for different package values and user types';
COMMENT ON FUNCTION get_commission_structure(DECIMAL) IS 'Retrieves commission structure for a specific package value';
COMMENT ON FUNCTION calculate_commission_amounts(DECIMAL, VARCHAR) IS 'Calculates actual commission amounts based on package value and user type';
