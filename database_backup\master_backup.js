const fs = require('fs');
const path = require('path');
const { spawn } = require('child_process');

// Import backup modules
const { backupEdgeFunctions } = require('./backup_edge_functions');
const { backupDatabaseSchema } = require('./backup_database_schema');

async function runScript(scriptPath, description) {
  return new Promise((resolve, reject) => {
    console.log(`\n🔄 Running ${description}...`);
    console.log(`📄 Script: ${scriptPath}`);
    
    const child = spawn('node', [scriptPath], {
      stdio: 'inherit',
      cwd: path.dirname(scriptPath)
    });
    
    child.on('close', (code) => {
      if (code === 0) {
        console.log(`✅ ${description} completed successfully`);
        resolve();
      } else {
        console.error(`❌ ${description} failed with code ${code}`);
        reject(new Error(`${description} failed`));
      }
    });
    
    child.on('error', (error) => {
      console.error(`❌ ${description} error:`, error);
      reject(error);
    });
  });
}

async function createMasterBackup() {
  const startTime = new Date();
  console.log('🚀 STARTING MASTER SUPABASE BACKUP');
  console.log('=' .repeat(80));
  console.log(`⏰ Started at: ${startTime.toISOString()}`);
  console.log('=' .repeat(80));
  
  const masterBackup = {
    timestamp: startTime.toISOString(),
    backup_type: 'MASTER_SUPABASE_BACKUP',
    status: 'IN_PROGRESS',
    components: {
      data_backup: { status: 'pending' },
      complete_backup: { status: 'pending' },
      schema_backup: { status: 'pending' },
      edge_functions: { status: 'pending' },
      migrations: { status: 'pending' }
    },
    files_created: [],
    errors: []
  };
  
  try {
    // 1. Run complete data backup
    console.log('\n📊 PHASE 1: Complete Data Backup');
    console.log('-' .repeat(50));
    try {
      await runScript(path.join(__dirname, 'create_backup.js'), 'Data Backup');
      masterBackup.components.data_backup.status = 'completed';
      masterBackup.components.data_backup.completed_at = new Date().toISOString();
    } catch (error) {
      masterBackup.components.data_backup.status = 'failed';
      masterBackup.components.data_backup.error = error.message;
      masterBackup.errors.push(`Data Backup: ${error.message}`);
    }
    
    // 2. Run complete Supabase backup
    console.log('\n🔧 PHASE 2: Complete Supabase Components Backup');
    console.log('-' .repeat(50));
    try {
      await runScript(path.join(__dirname, 'create_complete_backup.js'), 'Complete Supabase Backup');
      masterBackup.components.complete_backup.status = 'completed';
      masterBackup.components.complete_backup.completed_at = new Date().toISOString();
    } catch (error) {
      masterBackup.components.complete_backup.status = 'failed';
      masterBackup.components.complete_backup.error = error.message;
      masterBackup.errors.push(`Complete Backup: ${error.message}`);
    }
    
    // 3. Run database schema backup
    console.log('\n🗄️  PHASE 3: Database Schema Backup');
    console.log('-' .repeat(50));
    try {
      await backupDatabaseSchema();
      masterBackup.components.schema_backup.status = 'completed';
      masterBackup.components.schema_backup.completed_at = new Date().toISOString();
    } catch (error) {
      masterBackup.components.schema_backup.status = 'failed';
      masterBackup.components.schema_backup.error = error.message;
      masterBackup.errors.push(`Schema Backup: ${error.message}`);
    }
    
    // 4. Run Edge Functions backup
    console.log('\n⚡ PHASE 4: Edge Functions Backup');
    console.log('-' .repeat(50));
    try {
      await backupEdgeFunctions();
      masterBackup.components.edge_functions.status = 'completed';
      masterBackup.components.edge_functions.completed_at = new Date().toISOString();
    } catch (error) {
      masterBackup.components.edge_functions.status = 'failed';
      masterBackup.components.edge_functions.error = error.message;
      masterBackup.errors.push(`Edge Functions: ${error.message}`);
    }
    
    // 5. Copy migrations (already done in schema backup, but ensure it's noted)
    console.log('\n📋 PHASE 5: Migrations Backup');
    console.log('-' .repeat(50));
    try {
      const migrationsDir = path.join(__dirname, '..', 'supabase', 'migrations');
      const backupMigrationsDir = path.join(__dirname, 'migrations_backup');
      
      if (fs.existsSync(migrationsDir)) {
        if (!fs.existsSync(backupMigrationsDir)) {
          fs.mkdirSync(backupMigrationsDir, { recursive: true });
        }
        
        const files = fs.readdirSync(migrationsDir);
        const sqlFiles = files.filter(file => file.endsWith('.sql'));
        
        console.log(`📄 Copying ${sqlFiles.length} migration files...`);
        
        sqlFiles.forEach(file => {
          const sourcePath = path.join(migrationsDir, file);
          const destPath = path.join(backupMigrationsDir, file);
          fs.copyFileSync(sourcePath, destPath);
        });
        
        masterBackup.components.migrations.status = 'completed';
        masterBackup.components.migrations.completed_at = new Date().toISOString();
        masterBackup.components.migrations.files_count = sqlFiles.length;
        console.log(`✅ Migrations backup completed: ${sqlFiles.length} files`);
      } else {
        console.log('⚠️  No migrations directory found');
        masterBackup.components.migrations.status = 'skipped';
        masterBackup.components.migrations.reason = 'No migrations directory found';
      }
    } catch (error) {
      masterBackup.components.migrations.status = 'failed';
      masterBackup.components.migrations.error = error.message;
      masterBackup.errors.push(`Migrations: ${error.message}`);
    }
    
    // 6. Collect all created files
    console.log('\n📁 PHASE 6: Collecting Backup Files');
    console.log('-' .repeat(50));
    
    const backupDir = __dirname;
    const files = fs.readdirSync(backupDir);
    
    // Find all backup files created today
    const today = new Date().toISOString().split('T')[0];
    const backupFiles = files.filter(file => {
      return (
        file.includes('backup') && 
        (file.includes(today.replace(/-/g, '-')) || file.endsWith('.json') || file.endsWith('.sql'))
      );
    });
    
    masterBackup.files_created = backupFiles.map(file => {
      const filePath = path.join(backupDir, file);
      const stats = fs.existsSync(filePath) ? fs.statSync(filePath) : null;
      
      return {
        name: file,
        path: filePath,
        size_bytes: stats ? stats.size : 0,
        size_mb: stats ? (stats.size / (1024 * 1024)).toFixed(2) : '0',
        created_at: stats ? stats.birthtime.toISOString() : null
      };
    });
    
    console.log(`📄 Found ${backupFiles.length} backup files:`);
    masterBackup.files_created.forEach(file => {
      console.log(`  • ${file.name} (${file.size_mb} MB)`);
    });
    
    // Calculate total backup size
    const totalSizeMB = masterBackup.files_created.reduce((sum, file) => sum + parseFloat(file.size_mb), 0);
    
    // Finalize master backup
    const endTime = new Date();
    const durationMs = endTime - startTime;
    const durationMin = (durationMs / 60000).toFixed(2);
    
    masterBackup.status = masterBackup.errors.length > 0 ? 'COMPLETED_WITH_ERRORS' : 'COMPLETED';
    masterBackup.completed_at = endTime.toISOString();
    masterBackup.duration_ms = durationMs;
    masterBackup.duration_minutes = durationMin;
    masterBackup.total_files = masterBackup.files_created.length;
    masterBackup.total_size_mb = totalSizeMB.toFixed(2);
    
    // Save master backup report
    const masterBackupFile = `master_backup_report_${startTime.toISOString().replace(/[:.]/g, '-')}.json`;
    const masterBackupPath = path.join(__dirname, masterBackupFile);
    
    fs.writeFileSync(masterBackupPath, JSON.stringify(masterBackup, null, 2));
    
    // Print final summary
    console.log('\n' + '=' .repeat(80));
    console.log('🎉 MASTER SUPABASE BACKUP COMPLETED!');
    console.log('=' .repeat(80));
    console.log(`⏰ Duration: ${durationMin} minutes`);
    console.log(`📁 Total Files: ${masterBackup.total_files}`);
    console.log(`💾 Total Size: ${masterBackup.total_size_mb} MB`);
    console.log(`📊 Status: ${masterBackup.status}`);
    
    if (masterBackup.errors.length > 0) {
      console.log(`⚠️  Errors: ${masterBackup.errors.length}`);
      masterBackup.errors.forEach(error => {
        console.log(`   • ${error}`);
      });
    }
    
    console.log(`📋 Master Report: ${masterBackupPath}`);
    console.log('=' .repeat(80));
    
    return masterBackup;
    
  } catch (error) {
    console.error('❌ Master backup failed:', error);
    masterBackup.status = 'FAILED';
    masterBackup.error = error.message;
    throw error;
  }
}

// Run master backup
if (require.main === module) {
  createMasterBackup().catch(console.error);
}

module.exports = { createMasterBackup };
