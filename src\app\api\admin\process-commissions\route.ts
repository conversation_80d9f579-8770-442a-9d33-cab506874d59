import { NextRequest, NextResponse } from 'next/server'
import { CommissionSystemService } from '@/lib/services/commissionSystem'
import { cookies } from 'next/headers'
import { createServerClient } from '@supabase/ssr'
import { supabaseAdmin } from '@/lib/supabase'

export async function POST(request: NextRequest) {
  try {
    // Create Supabase client for auth using SSR
    const cookieStore = cookies()
    const supabaseAuth = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value
          },
        },
      }
    )

    // Get the current user
    const { data: { user }, error: authError } = await supabaseAuth.auth.getUser()

    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if user is admin
    const { data: userData, error: userError } = await supabaseAdmin
      .from('users')
      .select('role, is_super_admin')
      .eq('id', user.id)
      .single()

    if (userError || !userData) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 })
    }

    if (!userData.is_super_admin && userData.role !== 'admin') {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 })
    }

    // Process pending commissions
    const result = await CommissionSystemService.processPendingCommissions()

    return NextResponse.json({
      success: true,
      message: 'Commission processing completed',
      data: result
    })

  } catch (error) {
    console.error('POST /api/admin/process-commissions error:', error)
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to process commissions'
      },
      { status: 500 }
    )
  }
}

export async function GET(request: NextRequest) {
  try {
    // Create Supabase client for auth using SSR
    const cookieStore = cookies()
    const supabaseAuth = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value
          },
        },
      }
    )

    // Get the current user
    const { data: { user }, error: authError } = await supabaseAuth.auth.getUser()

    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check if user is admin
    const { data: userData, error: userError } = await supabaseAdmin
      .from('users')
      .select('role, is_super_admin')
      .eq('id', user.id)
      .single()

    if (userError || !userData) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 })
    }

    if (!userData.is_super_admin && userData.role !== 'admin') {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 })
    }

    // Get commission processing status
    const { data: pendingCommissions, error: pendingError } = await supabaseAdmin
      .from('commission_transactions')
      .select('status, COUNT(*) as count, SUM(commission_amount) as total_amount')
      .group('status')

    if (pendingError) {
      throw new Error(`Failed to get commission status: ${pendingError.message}`)
    }

    const { data: recentCommissions, error: recentError } = await supabaseAdmin
      .from('commission_transactions')
      .select(`
        id, status, commission_amount, commission_type, commission_level,
        created_at, processed_at, beneficiary_id
      `)
      .order('created_at', { ascending: false })
      .limit(10)

    if (recentError) {
      throw new Error(`Failed to get recent commissions: ${recentError.message}`)
    }

    return NextResponse.json({
      success: true,
      data: {
        statusSummary: pendingCommissions || [],
        recentCommissions: recentCommissions || []
      }
    })

  } catch (error) {
    console.error('GET /api/admin/process-commissions error:', error)
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get commission status'
      },
      { status: 500 }
    )
  }
}
