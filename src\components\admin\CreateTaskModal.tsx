'use client'

import { useState, useEffect } from 'react'
import { X, Save, Plus, Trash2 } from 'lucide-react'
import Button from '@/components/ui/Button'
import { toast } from 'sonner'

interface CreateTaskModalProps {
  isOpen: boolean
  onClose: () => void
  onTaskCreated: () => void
}

interface TaskFormData {
  title: string
  description: string
  task_type: 'sales_target' | 'referral_target' | 'custom'
  target_user_types: string[]
  requirements: Record<string, any>
  reward_amount: number
  reward_type: 'normal_present' | 'annual_present' | 'bonus'
  expires_at: string
  subscription_package_id?: string
  direct_sales_required?: number
}

interface SubscriptionPackage {
  id: string
  name: string
  price: number
  currency: string
}

const USER_TYPES = [
  { value: 'user', label: 'Regular Users' },
  { value: 'rsm', label: 'Regional Sales Managers' },
  { value: 'zm', label: 'Zonal Managers' },
  { value: 'okdoi_head', label: 'OKDOI Head' }
]

const TASK_TYPES = [
  { value: 'sales_target', label: 'Sales Target' },
  { value: 'referral_target', label: 'Referral Target' },
  { value: 'custom', label: 'Custom Task' }
]

const REWARD_TYPES = [
  { value: 'normal_present', label: 'Normal Present' },
  { value: 'annual_present', label: 'Annual Present' },
  { value: 'bonus', label: 'Bonus' }
]

export default function CreateTaskModal({ isOpen, onClose, onTaskCreated }: CreateTaskModalProps) {
  const [loading, setLoading] = useState(false)
  const [subscriptionPackages, setSubscriptionPackages] = useState<SubscriptionPackage[]>([])
  const [formData, setFormData] = useState<TaskFormData>({
    title: '',
    description: '',
    task_type: 'custom',
    target_user_types: [],
    requirements: {},
    reward_amount: 0,
    reward_type: 'normal_present',
    expires_at: '',
    subscription_package_id: '',
    direct_sales_required: 0
  })

  const [customRequirements, setCustomRequirements] = useState<Array<{ key: string; value: string }>>([
    { key: '', value: '' }
  ])

  // Fetch subscription packages on component mount
  useEffect(() => {
    if (isOpen) {
      fetchSubscriptionPackages()
    }
  }, [isOpen])

  const fetchSubscriptionPackages = async () => {
    try {
      const response = await fetch('/api/admin/subscriptions/packages')
      const data = await response.json()

      if (data.success) {
        setSubscriptionPackages(data.data || [])
      }
    } catch (error) {
      console.error('Error fetching subscription packages:', error)
    }
  }

  if (!isOpen) return null

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)

    try {
      // Validate required fields
      if (!formData.title.trim()) {
        toast.error('Task title is required')
        return
      }

      if (formData.target_user_types.length === 0) {
        toast.error('Please select at least one target user type')
        return
      }

      if (formData.reward_amount <= 0) {
        toast.error('Reward amount must be greater than 0')
        return
      }

      // Build requirements object from custom requirements
      const requirements: Record<string, any> = {}
      customRequirements.forEach(req => {
        if (req.key.trim() && req.value.trim()) {
          requirements[req.key.trim()] = req.value.trim()
        }
      })

      // Get current user ID from session storage or context
      // The API will handle authentication validation
      const taskData = {
        ...formData,
        requirements,
        created_by: 'admin', // Will be set properly by the API
        expires_at: formData.expires_at || undefined
      }

      const response = await fetch('/api/admin/user-tasks', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(taskData)
      })

      const result = await response.json()

      if (result.success) {
        toast.success('Task created successfully!')
        onTaskCreated()
        onClose()
        // Reset form
        setFormData({
          title: '',
          description: '',
          task_type: 'custom',
          target_user_types: [],
          requirements: {},
          reward_amount: 0,
          reward_type: 'normal_present',
          expires_at: '',
          subscription_package_id: '',
          direct_sales_required: 0
        })
        setCustomRequirements([{ key: '', value: '' }])
      } else {
        toast.error(result.error || 'Failed to create task')
      }
    } catch (error) {
      console.error('Error creating task:', error)
      toast.error('Failed to create task')
    } finally {
      setLoading(false)
    }
  }

  const handleUserTypeChange = (userType: string, checked: boolean) => {
    if (checked) {
      setFormData(prev => ({
        ...prev,
        target_user_types: [...prev.target_user_types, userType]
      }))
    } else {
      setFormData(prev => ({
        ...prev,
        target_user_types: prev.target_user_types.filter(type => type !== userType)
      }))
    }
  }

  const addRequirement = () => {
    setCustomRequirements(prev => [...prev, { key: '', value: '' }])
  }

  const removeRequirement = (index: number) => {
    setCustomRequirements(prev => prev.filter((_, i) => i !== index))
  }

  const updateRequirement = (index: number, field: 'key' | 'value', value: string) => {
    setCustomRequirements(prev => prev.map((req, i) => 
      i === index ? { ...req, [field]: value } : req
    ))
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 className="text-xl font-bold text-gray-900">Create New Task</h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 p-2 rounded-lg hover:bg-gray-100"
          >
            <X className="h-5 w-5" />
          </button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {/* Basic Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-900">Basic Information</h3>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Task Title *
              </label>
              <input
                type="text"
                value={formData.title}
                onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-transparent"
                placeholder="Enter task title"
                required
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Description
              </label>
              <textarea
                value={formData.description}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-transparent"
                placeholder="Enter task description"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Task Type *
              </label>
              <select
                value={formData.task_type}
                onChange={(e) => setFormData(prev => ({ ...prev, task_type: e.target.value as any }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-transparent"
                required
              >
                {TASK_TYPES.map(type => (
                  <option key={type.value} value={type.value}>
                    {type.label}
                  </option>
                ))}
              </select>
            </div>
          </div>

          {/* Target Users */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-900">Target Users</h3>
            <div className="space-y-2">
              {USER_TYPES.map(userType => (
                <label key={userType.value} className="flex items-center">
                  <input
                    type="checkbox"
                    checked={formData.target_user_types.includes(userType.value)}
                    onChange={(e) => handleUserTypeChange(userType.value, e.target.checked)}
                    className="h-4 w-4 text-amber-600 focus:ring-amber-500 border-gray-300 rounded"
                  />
                  <span className="ml-2 text-sm text-gray-700">{userType.label}</span>
                </label>
              ))}
            </div>
          </div>

          {/* Requirements */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-900">Requirements</h3>

            {/* Subscription Package Selection */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Required Subscription Package
              </label>
              <select
                value={formData.subscription_package_id || ''}
                onChange={(e) => setFormData(prev => ({ ...prev, subscription_package_id: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-transparent"
              >
                <option value="">Select a subscription package (optional)</option>
                {subscriptionPackages.map(pkg => (
                  <option key={pkg.id} value={pkg.id}>
                    {pkg.name} - {pkg.currency} {pkg.price.toLocaleString()}
                  </option>
                ))}
              </select>
            </div>

            {/* Direct Sales Required */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Direct Sales Required
              </label>
              <input
                type="number"
                value={formData.direct_sales_required || 0}
                onChange={(e) => setFormData(prev => ({ ...prev, direct_sales_required: parseInt(e.target.value) || 0 }))}
                min="0"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-transparent"
                placeholder="Number of direct sales required"
              />
            </div>

            {/* Custom Requirements */}
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <h4 className="text-md font-medium text-gray-700">Custom Requirements</h4>
                <Button
                  type="button"
                  onClick={addRequirement}
                  variant="outline"
                  size="sm"
                  className="flex items-center"
                >
                  <Plus className="h-4 w-4 mr-1" />
                  Add Requirement
                </Button>
              </div>

              {customRequirements.map((req, index) => (
                <div key={index} className="flex items-center space-x-3">
                  <input
                    type="text"
                    value={req.key}
                    onChange={(e) => updateRequirement(index, 'key', e.target.value)}
                    placeholder="Requirement name"
                    className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-transparent"
                  />
                  <input
                    type="text"
                    value={req.value}
                    onChange={(e) => updateRequirement(index, 'value', e.target.value)}
                    placeholder="Requirement value"
                    className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-transparent"
                  />
                  {customRequirements.length > 1 && (
                    <button
                      type="button"
                      onClick={() => removeRequirement(index)}
                      className="text-red-500 hover:text-red-700 p-2"
                    >
                      <Trash2 className="h-4 w-4" />
                    </button>
                  )}
                </div>
              ))}
            </div>
          </div>

          {/* Reward */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-900">Reward</h3>
            
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Reward Amount (Rs) *
                </label>
                <input
                  type="number"
                  value={formData.reward_amount}
                  onChange={(e) => setFormData(prev => ({ ...prev, reward_amount: parseFloat(e.target.value) || 0 }))}
                  min="0"
                  step="0.01"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-transparent"
                  placeholder="0.00"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Reward Type *
                </label>
                <select
                  value={formData.reward_type}
                  onChange={(e) => setFormData(prev => ({ ...prev, reward_type: e.target.value as any }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-transparent"
                  required
                >
                  {REWARD_TYPES.map(type => (
                    <option key={type.value} value={type.value}>
                      {type.label}
                    </option>
                  ))}
                </select>
              </div>
            </div>
          </div>

          {/* Expiry Date */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Expiry Date (Optional)
            </label>
            <input
              type="datetime-local"
              value={formData.expires_at}
              onChange={(e) => setFormData(prev => ({ ...prev, expires_at: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-500 focus:border-transparent"
            />
          </div>

          {/* Actions */}
          <div className="flex items-center justify-end space-x-3 pt-6 border-t border-gray-200">
            <Button
              type="button"
              onClick={onClose}
              variant="outline"
              disabled={loading}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={loading}
              className="bg-amber-600 hover:bg-amber-700 flex items-center"
            >
              {loading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Creating...
                </>
              ) : (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  Create Task
                </>
              )}
            </Button>
          </div>
        </form>
      </div>
    </div>
  )
}
