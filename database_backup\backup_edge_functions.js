const fs = require('fs');
const path = require('path');

async function backupEdgeFunctions() {
  console.log('🔧 Backing up Edge Functions...');
  
  const functionsDir = path.join(__dirname, '..', 'supabase', 'functions');
  const backupDir = path.join(__dirname, 'edge_functions_backup');
  
  // Create backup directory
  if (!fs.existsSync(backupDir)) {
    fs.mkdirSync(backupDir, { recursive: true });
  }
  
  const edgeFunctionsBackup = {
    timestamp: new Date().toISOString(),
    backup_type: 'EDGE_FUNCTIONS',
    functions: {}
  };
  
  if (!fs.existsSync(functionsDir)) {
    console.log('  ⚠️  No Edge Functions directory found');
    edgeFunctionsBackup.functions = { error: 'No functions directory found' };
  } else {
    const functionDirs = fs.readdirSync(functionsDir, { withFileTypes: true })
      .filter(dirent => dirent.isDirectory())
      .map(dirent => dirent.name);
    
    console.log(`  📁 Found ${functionDirs.length} Edge Functions`);
    
    for (const functionName of functionDirs) {
      console.log(`  📄 Backing up function: ${functionName}`);
      
      const functionDir = path.join(functionsDir, functionName);
      const functionBackupDir = path.join(backupDir, functionName);
      
      // Create function backup directory
      if (!fs.existsSync(functionBackupDir)) {
        fs.mkdirSync(functionBackupDir, { recursive: true });
      }
      
      const functionInfo = {
        name: functionName,
        files: {},
        created_at: new Date().toISOString()
      };
      
      // Copy all files in the function directory
      const files = fs.readdirSync(functionDir, { withFileTypes: true });
      
      for (const file of files) {
        if (file.isFile()) {
          const sourceFile = path.join(functionDir, file.name);
          const destFile = path.join(functionBackupDir, file.name);
          
          try {
            const content = fs.readFileSync(sourceFile, 'utf8');
            fs.writeFileSync(destFile, content);
            
            functionInfo.files[file.name] = {
              size: content.length,
              content: content
            };
            
            console.log(`    ✅ ${file.name} (${content.length} bytes)`);
          } catch (err) {
            console.log(`    ❌ Failed to backup ${file.name}: ${err.message}`);
            functionInfo.files[file.name] = { error: err.message };
          }
        }
      }
      
      edgeFunctionsBackup.functions[functionName] = functionInfo;
    }
  }
  
  // Save Edge Functions backup metadata
  const backupMetaFile = path.join(backupDir, 'edge_functions_backup.json');
  fs.writeFileSync(backupMetaFile, JSON.stringify(edgeFunctionsBackup, null, 2));
  
  console.log(`  ✅ Edge Functions backup completed`);
  console.log(`  📁 Backup saved to: ${backupDir}`);
  
  return edgeFunctionsBackup;
}

// Run if called directly
if (require.main === module) {
  backupEdgeFunctions().catch(console.error);
}

module.exports = { backupEdgeFunctions };
