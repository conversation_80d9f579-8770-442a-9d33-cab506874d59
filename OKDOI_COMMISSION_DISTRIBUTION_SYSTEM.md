# OKDOI Commission Distribution System - CORRECTED

## Overview
This document outlines the **CORRECTED** commission distribution system for OKDOI marketplace subscriptions with **absolute amounts** (Rs) based on actual testing and verification.

## Package Structure (Rs 2,000 Example)
- **Total Package Value**: Rs 2,000
- **Network Distribution Budget**: Rs 1,500 (75%)
- **Company Profit**: Rs 500 (25%)

## Commission Categories & Budget Allocation

### Direct Commission
- **Budget**: Rs 200
- **Distribution**: Goes ONLY to the Introducer (person who directly referred the purchaser)
- **Leftover Rule**: If no Introducer exists → Rs 200 goes to company leftover

### Regular Commission Types (10 Types - Rs 860)
**Fixed Rs 86 per level** - Distributed to actual network participants only

| Commission Type | Total Budget | Per Level Amount |
|----------------|--------------|------------------|
| Level Commission | Rs 400 | Rs 40 |
| Voucher | Rs 150 | Rs 15 |
| Festival Bonus | Rs 40 | Rs 4 |
| Saving | Rs 30 | Rs 3 |
| Entertainment | Rs 60 | Rs 6 |
| Medical | Rs 20 | Rs 2 |
| Education | Rs 20 | Rs 2 |
| Credit | Rs 40 | Rs 4 |
| Gift Center | Rs 100 | Rs 10 (Hidden from users) |
| **Total Per Level** | **Rs 860** | **Rs 86** |

### Gift System Commission Types (4 Types - Rs 220)
| Commission Type | Budget Allocation | Distribution Logic |
|----------------|------------------|-------------------|
| Present User | Rs 120 | **Introducer Only** - Person who directly introduced the purchaser gets this |
| Annual Present User | Rs 30 | **Introducer Only** - Person who directly introduced the purchaser gets this |
| Present Leader | Rs 50 | **ZM Only** - ZM gets this from any network purchase |
| Annual Present Leader | Rs 20 | **ZM Only** - ZM gets this from any network purchase |
| **Subtotal** | **Rs 220** |

**CORRECTED Distribution Logic**:
- **Regular Commission Types**: Fixed Rs 86 per level distributed to actual network participants only
- **Gift System User Commissions (Rs 150)**: Go ONLY to the Introducer
- **Gift System Leader Commissions (Rs 70)**: BOTH Present Leader (Rs 50) AND Annual Present Leader (Rs 20) go to ZM
- **Key Correction**: ZM gets BOTH gift system leader commissions regardless of RSM existence

### Special Commission Types

#### Zonal Manager (ZM) Commissions
**Total Budget**: Rs 80 (if ZM exists in network)
- ZM Bonus: Rs 40
- ZM Petral Allowance: Rs 15
- ZM Leasing Facility: Rs 20
- ZM Phone Bill: Rs 5
- **Leftover Rule**: If no ZM in network → Rs 80 goes to company leftover

#### Regional Sales Manager (RSM) Commissions
**Total Budget**: Rs 90 (if RSM exists in network)
- RSM Bonus: Rs 50
- RSM Petral Allowance: Rs 15
- RSM Leasing Facility: Rs 20
- RSM Phone Bill: Rs 5
- **Leftover Rule**: If no RSM in network → Rs 90 goes to company leftover

#### OKDOI Head Universal Commission
- **Budget**: Rs 50
- **Distribution**: Always distributed (universal commission)
- **No Leftover**: Always goes to OKDOI Head regardless of network structure

## Distribution Examples

### Example 1: 4-Level Network (Levels 0,1,2,3) with ZM, no RSM

**Network Structure**:
- Level 0: Purchaser (<EMAIL>)
- Level 1: Introducer (<EMAIL>)
- Level 2: Zonal Manager (<EMAIL>)
- Level 3: OKDOI Head (<EMAIL>)

**Commission Distribution**:
- **Direct Commission**: Rs 200 → Level 1
- **9 Regular Commission Types**: Rs 860 ÷ 4 levels = Rs 215 per level
  - Level 0: Rs 215
  - Level 1: Rs 215 (+ Rs 200 direct = Rs 415 total)
  - Level 2: Rs 215 (+ Rs 80 ZM = Rs 295 total)
  - Level 3: Rs 215
- **Gift System User Commissions**: Rs 150 → Level 1 (direct sale)
- **Gift System Leader Commissions**: Rs 70 → Level 2 (ZM exists)
- **ZM Specific**: Rs 80 → Level 2
- **RSM Specific**: Rs 90 → Leftover (no RSM)
- **OKDOI Head Universal**: Rs 50 → Level 3

**Summary**:
- Total Network Distributed: Rs 1,410
- Company Leftover: Rs 90 (missing RSM)
- Company Profit: Rs 500
- **Company Wallet Total**: Rs 590
- **Grand Total**: Rs 2,000 ✅

### Perfect Balance Example: 10-Level Network with ZM and RSM
**Commission Distribution**:
- **Direct Commission**: Rs 200 → Level 1
- **9 Regular Commission Types**: Rs 860 ÷ 10 levels = Rs 86 per level
- **Gift System User Commissions**: Rs 150 → Level 1 (direct sale)
- **Gift System Leader Commissions**: Rs 70 → ZM/RSM (both exist)
- **ZM Specific**: Rs 80 → ZM level
- **RSM Specific**: Rs 90 → RSM level
- **OKDOI Head Universal**: Rs 50 → OKDOI Head

**Summary**:
- Total Network Distributed: Rs 1,500
- Company Leftover: Rs 0 (no missing commissions)
- Company Profit: Rs 500
- **Company Wallet Total**: Rs 500
- **Grand Total**: Rs 2,000 ✅

## Gift System Integration

### Gift System Commissions
- **Total Budget**: Rs 220 (4 gift system types + Gift Center)
  - **Gift Center**: Rs 100 (distributed to all network levels)
  - **Present User**: Rs 120 (ONLY to Level 1 for direct sales)
  - **Present Leader**: Rs 50 (ONLY to ZM/RSM from entire network)
  - **Annual Present User**: Rs 30 (ONLY to Level 1 for direct sales)
  - **Annual Present Leader**: Rs 20 (ONLY to ZM/RSM from entire network)

**Distribution Logic**:
- **Gift Center**: Distributed proportionally to all network levels (like regular commissions)
- **User Gift Commissions (Rs 150)**: Go to the Introducer (person who directly referred the purchaser)
- **Leader Gift Commissions (Rs 70)**: Present Leader (Rs 50) → ZM, Annual Present Leader (Rs 20) → RSM
- **Leftover**: Leader gift commissions become leftover if no ZM/RSM exists in the network
- **User Visibility**: Hidden from user wallets and dashboards
- **Admin Visibility**: Visible in admin panel for tracking and management
- **Purpose**: Used for admin-created gift tasks and rewards

**Example**: User A introduces User B. When User B purchases Rs 2000 package:
- User A gets Present User (Rs 120) + Annual Present User (Rs 30) = Rs 150
- ZM in network gets Present Leader (Rs 50)
- RSM in network gets Annual Present Leader (Rs 20)

### Implementation - CORRECTED GIFT SYSTEM HANDLING
**⚠️ CRITICAL: Gift System Commissions are EXCLUDED from User Wallets**

#### Gift System Commission Types (NOT Added to User Wallets)
- `present_user` (Rs 120) - ❌ NOT added to user wallet
- `annual_present_user` (Rs 30) - ❌ NOT added to user wallet
- `present_leader` (Rs 50) - ❌ NOT added to user wallet
- `annual_present_leader` (Rs 20) - ❌ NOT added to user wallet

#### Wallet Impact Rules
1. **Commission Calculation**: Gift system commissions are calculated and tracked in commission_transactions
2. **Database Storage**: Stored with metadata `"gift_system": true, "hidden_from_user": true`
3. **Wallet Exclusion**: NOT added to user_wallets balance
4. **Transaction Exclusion**: NOT shown in wallet_transactions table
5. **Admin Visibility**: Only visible in Admin panel user management modal
6. **User Visibility**: Completely hidden from user dashboard and wallet interfaces

#### Corrected Wallet Balances Example (5-Level Network)
- **Kevin (Direct Referrer)**: Rs 286.00 (Rs 436 - Rs 150 gift system)
  - Regular Commissions: Rs 86.00 ✅ Added to wallet
  - Direct Commission: Rs 200.00 ✅ Added to wallet
  - Gift System: Rs 150.00 ❌ Admin panel only
- **ZM**: Rs 166.00 (Rs 236 - Rs 70 gift system)
  - Regular Commissions: Rs 86.00 ✅ Added to wallet
  - ZM Specific: Rs 80.00 ✅ Added to wallet
  - Gift System: Rs 70.00 ❌ Admin panel only

## Leftover Commission Handling

### Sources of Leftover Commissions
1. **Missing Direct Commission**: Rs 200 if no Level 1 introducer
2. **Missing ZM Commissions**: Rs 80 if no ZM in network
3. **Missing RSM Commissions**: Rs 90 if no RSM in network
4. **Remaining Network Budget**: Rs 1,500 - (actual distributions)

### Leftover Destination
- All leftover commissions go to **Company Wallet**
- Combined with company profit (Rs 500) for total company revenue
- Tracked separately for transparency and audit purposes

## Mathematical Validation

### Budget Constraints
- **Network Distribution**: Never exceeds Rs 1,500
- **Company Profit**: Fixed at Rs 500
- **Total Package**: Always equals Rs 2,000
- **Leftover Calculation**: Rs 1,500 - (actual network distributions)

### Validation Formula
```
Network Distributed + Company Leftover + Company Profit = Package Value
Network Distributed ≤ Network Distribution Budget (Rs 1,500)
Company Wallet = Company Leftover + Company Profit
```

## Technical Implementation

### Database Function
- `calculate_commission_distribution_absolute()`: Main distribution function
- Returns commission allocations with beneficiary IDs and amounts
- Handles leftover calculations and company wallet allocations

### Commission Processing
1. **Network Analysis**: Determine available levels and user types
2. **Budget Allocation**: Distribute commissions according to structure
3. **Special Commissions**: Add ZM/RSM/OKDOI Head specific amounts
4. **Leftover Calculation**: Calculate and allocate remaining budget
5. **Wallet Updates**: Credit user wallets and company wallet
6. **Transaction Recording**: Log all commission transactions

### Gift System Processing
- Separate tracking for gift_center commission type
- Hidden from user-facing interfaces
- Admin panel integration for gift management
- Used for task-based reward system

## Security & Compliance

### Data Integrity
- All transactions are logged with full audit trail
- Commission calculations are deterministic and reproducible
- Budget constraints are enforced at database level

### User Privacy
- Gift system allocations are hidden from users
- Only admin panel shows complete commission breakdown
- Users see only their eligible commission types

## CORRECTED REAL EXAMPLE: 5-Level Network Test Case

### Network Structure (<EMAIL> purchase):
- **Level 0**: `<EMAIL>` - sameera banda (Purchaser)
- **Level 1**: `<EMAIL>` - malith
- **Level 2**: `<EMAIL>` - Kevin (Direct Referrer)
- **Level 3**: `<EMAIL>` - OKDOI Default Zonal Manager (ZM)
- **Level 4**: `<EMAIL>` - OKDOI Company Head (OKDOI Head)

### Commission Distribution (CORRECTED):

#### Individual Beneficiary Totals:
- **sameera (Purchaser)**: Rs 86.00 (regular commissions only)
- **malith (Level 1)**: Rs 86.00 (regular commissions only)
- **Kevin (Direct Referrer)**: Rs 436.00
  - Regular Commissions: Rs 86.00
  - Direct Commission: Rs 200.00
  - Gift System User: Rs 150.00 (Present User Rs 120 + Annual Present User Rs 30)
- **ZM (Level 3)**: Rs 236.00
  - Regular Commissions: Rs 86.00
  - ZM Specific: Rs 80.00 (Rs 40 + Rs 15 + Rs 20 + Rs 5)
  - Gift System Leader: Rs 70.00 (Present Leader Rs 50 + Annual Present Leader Rs 20)
- **OKDOI Head (Level 4)**: Rs 136.00
  - Regular Commissions: Rs 86.00 (gets normal commissions because within 10 levels)
  - Universal Commission: Rs 50.00 (always gets this)

#### Financial Summary:
- **Total Network Distributed**: Rs 980.00
- **Network Budget**: Rs 1,500.00
- **Leftover Commissions**: Rs 520.00
  - RSM Specific: Rs 90.00 (no RSM exists)
  - RSM Gift System: Rs 70.00 (Present Leader Rs 50 + Annual Present Leader Rs 20 for RSM)
  - Missing Network Levels: Rs 360.00
- **Company Profit**: Rs 500.00
- **Total Company Wallet**: Rs 1,020.00
- **Grand Total**: Rs 2,000.00 ✅

## Key System Rules (CORRECTED)

1. **Fixed Per-Level Amount**: Regular commissions are Rs 86 per level (Rs 860 ÷ 10 theoretical levels)
2. **ZM Gets Both Leader Gifts**: ZM receives both Present Leader (Rs 50) AND Annual Present Leader (Rs 20)
3. **OKDOI Head Normal Commissions**: OKDOI Head gets regular commissions if within 10 levels of purchaser
4. **RSM Gift System Leftover**: If no RSM exists, both RSM gift system commissions go to leftover
5. **Mathematical Balance**: Always equals Rs 2,000 total
6. **Large Leftover Possible**: Missing network positions create significant leftover amounts

This corrected system reflects the actual tested behavior where ZM gets both gift system leader commissions and OKDOI Head receives normal commissions when within the network hierarchy.
