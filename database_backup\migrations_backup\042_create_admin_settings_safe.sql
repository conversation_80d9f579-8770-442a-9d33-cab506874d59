-- Safe Admin Settings Table Creation
-- This migration creates the admin_settings table and basic policies without conflicts

-- =====================================================
-- 1. CREATE ADMIN SETTINGS TABLE
-- =====================================================

-- Create admin_settings table if it doesn't exist
CREATE TABLE IF NOT EXISTS admin_settings (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    key VARCHAR(100) NOT NULL UNIQUE,
    value JSONB NOT NULL,
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create index for faster key lookups
CREATE INDEX IF NOT EXISTS idx_admin_settings_key ON admin_settings(key);

-- =====================================================
-- 2. INSERT DEFAULT SETTINGS
-- =====================================================

-- Insert default settings with conflict handling
INSERT INTO admin_settings (key, value, description) VALUES
('site_name', '"OKDOI"', 'Name of the website'),
('site_description', '"Premium Marketplace for Everything"', 'Description of the website'),
('admin_email', '"<EMAIL>"', 'Administrator email address'),
('allow_registration', 'true', 'Whether new user registration is allowed'),
('require_email_verification', 'false', 'Whether email verification is required for new users'),
('auto_approve_ads', 'false', 'Whether ads are automatically approved'),
('max_images_per_ad', '10', 'Maximum number of images per ad'),
('ad_expiry_days', '30', 'Number of days before ads expire'),
('enable_notifications', 'true', 'Whether notifications are enabled'),
('maintenance_mode', 'false', 'Whether the site is in maintenance mode'),
('enable_analytics', 'true', 'Whether analytics are enabled'),
('enable_referrals', 'true', 'Whether referral system is enabled'),
('free_ads_limit', '2', 'Number of free ads new users can post without a subscription')
ON CONFLICT (key) DO NOTHING;

-- =====================================================
-- 3. ENABLE RLS AND CREATE SAFE POLICIES
-- =====================================================

-- Enable RLS
ALTER TABLE admin_settings ENABLE ROW LEVEL SECURITY;

-- Drop any existing policies to avoid conflicts
DO $$
BEGIN
    -- Drop policies if they exist
    DROP POLICY IF EXISTS "admin_settings_select_policy" ON admin_settings;
    DROP POLICY IF EXISTS "admin_settings_insert_policy" ON admin_settings;
    DROP POLICY IF EXISTS "admin_settings_update_policy" ON admin_settings;
    DROP POLICY IF EXISTS "admin_settings_delete_policy" ON admin_settings;
    
    -- Also drop any other potential policy names
    DROP POLICY IF EXISTS "Admin users can read admin settings" ON admin_settings;
    DROP POLICY IF EXISTS "Admin users can insert admin settings" ON admin_settings;
    DROP POLICY IF EXISTS "Admin users can update admin settings" ON admin_settings;
    DROP POLICY IF EXISTS "Admin users can delete admin settings" ON admin_settings;
    DROP POLICY IF EXISTS "Enable read access for admin settings" ON admin_settings;
    DROP POLICY IF EXISTS "Enable insert for admin settings" ON admin_settings;
    DROP POLICY IF EXISTS "Enable update for admin settings" ON admin_settings;
    DROP POLICY IF EXISTS "Enable delete for admin settings" ON admin_settings;
EXCEPTION
    WHEN OTHERS THEN
        -- Ignore errors if policies don't exist
        NULL;
END $$;

-- Create simple, safe policies
CREATE POLICY "admin_settings_select_policy" ON admin_settings FOR SELECT USING (true);
CREATE POLICY "admin_settings_insert_policy" ON admin_settings FOR INSERT WITH CHECK (true);
CREATE POLICY "admin_settings_update_policy" ON admin_settings FOR UPDATE USING (true);
CREATE POLICY "admin_settings_delete_policy" ON admin_settings FOR DELETE USING (true);

-- =====================================================
-- 4. CREATE UPDATE TRIGGER
-- =====================================================

-- Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_admin_settings_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Drop existing trigger if it exists
DROP TRIGGER IF EXISTS update_admin_settings_updated_at_trigger ON admin_settings;

-- Create trigger to automatically update updated_at
CREATE TRIGGER update_admin_settings_updated_at_trigger
    BEFORE UPDATE ON admin_settings
    FOR EACH ROW
    EXECUTE FUNCTION update_admin_settings_updated_at();

-- =====================================================
-- 5. ENSURE USERS TABLE HAS BASIC COLUMNS
-- =====================================================

-- Add basic columns to users table if they don't exist
ALTER TABLE users ADD COLUMN IF NOT EXISTS role VARCHAR(20) DEFAULT 'user';
ALTER TABLE users ADD COLUMN IF NOT EXISTS is_super_admin BOOLEAN DEFAULT FALSE;
ALTER TABLE users ADD COLUMN IF NOT EXISTS referral_code VARCHAR(20);

-- Create basic indexes
CREATE INDEX IF NOT EXISTS idx_users_role ON users(role);
CREATE INDEX IF NOT EXISTS idx_users_referral_code ON users(referral_code);

-- =====================================================
-- COMPLETION
-- =====================================================

COMMENT ON TABLE admin_settings IS 'Admin settings table - Safe configuration without conflicts';
