-- Create vendor shops system

-- Vendor shops table
CREATE TABLE vendor_shops (
    id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id uuid REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    name varchar(255) NOT NULL,
    slug varchar(255) UNIQUE NOT NULL,
    description text,
    logo_url text,
    banner_url text,
    contact_email varchar(255),
    contact_phone varchar(20),
    address text,
    website_url text,
    social_links jsonb DEFAULT '{}',
    status varchar(20) DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'rejected', 'suspended')),
    is_featured boolean DEFAULT false,
    rating numeric(3,2) DEFAULT 0.00,
    total_reviews integer DEFAULT 0,
    total_products integer DEFAULT 0,
    total_sales integer DEFAULT 0,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now()
);

-- Products table (independent shop products - NOT linked to ads)
CREATE TABLE shop_products (
    id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
    shop_id uuid REFERENCES vendor_shops(id) ON DELETE CASCADE NOT NULL,
    category_id uuid REFERENCES shop_categories(id) ON DELETE SET NULL,
    subcategory_id uuid REFERENCES shop_subcategories(id) ON DELETE SET NULL,
    title varchar(200) NOT NULL,
    description text NOT NULL,
    price numeric(12,2) NOT NULL DEFAULT 0,
    currency varchar(3) DEFAULT 'LKR',
    condition varchar(20) DEFAULT 'new' CHECK (condition IN ('new', 'used', 'refurbished')),
    sku varchar(100),
    stock_quantity integer DEFAULT 0,
    min_order_quantity integer DEFAULT 1,
    weight numeric(10,2),
    dimensions jsonb, -- {length, width, height}
    shipping_info jsonb, -- shipping options and costs
    variants jsonb DEFAULT '[]', -- product variants (size, color, etc.)
    is_digital boolean DEFAULT false,
    download_url text, -- for digital products
    contact_phone varchar(20),
    negotiable boolean DEFAULT false,
    status varchar(20) DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'out_of_stock', 'draft')),
    views integer DEFAULT 0,
    average_rating numeric(3,2) DEFAULT 0.00,
    total_reviews integer DEFAULT 0,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now()
);

-- Product images table (separate from ad images)
CREATE TABLE shop_product_images (
    id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
    product_id uuid REFERENCES shop_products(id) ON DELETE CASCADE NOT NULL,
    image_url text NOT NULL,
    alt_text varchar(255),
    sort_order integer DEFAULT 0,
    is_primary boolean DEFAULT false,
    created_at timestamp with time zone DEFAULT now()
);

-- Shop reviews table
CREATE TABLE shop_reviews (
    id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
    shop_id uuid REFERENCES vendor_shops(id) ON DELETE CASCADE NOT NULL,
    user_id uuid REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    rating integer CHECK (rating >= 1 AND rating <= 5) NOT NULL,
    review_text text,
    is_verified boolean DEFAULT false,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    UNIQUE(shop_id, user_id)
);

-- Shop followers table
CREATE TABLE shop_followers (
    id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
    shop_id uuid REFERENCES vendor_shops(id) ON DELETE CASCADE NOT NULL,
    user_id uuid REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    created_at timestamp with time zone DEFAULT now(),
    UNIQUE(shop_id, user_id)
);

-- Create indexes for better performance
CREATE INDEX idx_vendor_shops_user_id ON vendor_shops(user_id);
CREATE INDEX idx_vendor_shops_status ON vendor_shops(status);
CREATE INDEX idx_vendor_shops_slug ON vendor_shops(slug);
CREATE INDEX idx_shop_products_shop_id ON shop_products(shop_id);
CREATE INDEX idx_shop_products_ad_id ON shop_products(ad_id);
CREATE INDEX idx_shop_reviews_shop_id ON shop_reviews(shop_id);
CREATE INDEX idx_shop_followers_shop_id ON shop_followers(shop_id);
CREATE INDEX idx_shop_followers_user_id ON shop_followers(user_id);

-- Create triggers for updated_at
CREATE TRIGGER update_vendor_shops_updated_at
    BEFORE UPDATE ON vendor_shops
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_shop_products_updated_at
    BEFORE UPDATE ON shop_products
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_shop_reviews_updated_at
    BEFORE UPDATE ON shop_reviews
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Function to update shop stats
CREATE OR REPLACE FUNCTION update_shop_stats()
RETURNS trigger AS $$
BEGIN
    -- Update total_products count
    UPDATE vendor_shops 
    SET total_products = (
        SELECT COUNT(*) 
        FROM shop_products 
        WHERE shop_id = COALESCE(NEW.shop_id, OLD.shop_id)
    )
    WHERE id = COALESCE(NEW.shop_id, OLD.shop_id);
    
    -- Update rating and review count
    UPDATE vendor_shops 
    SET 
        rating = COALESCE((
            SELECT AVG(rating)::numeric(3,2) 
            FROM shop_reviews 
            WHERE shop_id = COALESCE(NEW.shop_id, OLD.shop_id)
        ), 0.00),
        total_reviews = (
            SELECT COUNT(*) 
            FROM shop_reviews 
            WHERE shop_id = COALESCE(NEW.shop_id, OLD.shop_id)
        )
    WHERE id = COALESCE(NEW.shop_id, OLD.shop_id);
    
    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- Create triggers to update shop stats
CREATE TRIGGER update_shop_stats_on_product_change
    AFTER INSERT OR UPDATE OR DELETE ON shop_products
    FOR EACH ROW
    EXECUTE FUNCTION update_shop_stats();

CREATE TRIGGER update_shop_stats_on_review_change
    AFTER INSERT OR UPDATE OR DELETE ON shop_reviews
    FOR EACH ROW
    EXECUTE FUNCTION update_shop_stats();

-- Enable RLS
ALTER TABLE vendor_shops ENABLE ROW LEVEL SECURITY;
ALTER TABLE shop_products ENABLE ROW LEVEL SECURITY;
ALTER TABLE shop_reviews ENABLE ROW LEVEL SECURITY;
ALTER TABLE shop_followers ENABLE ROW LEVEL SECURITY;

-- RLS Policies for vendor_shops
CREATE POLICY "Approved shops are viewable by everyone" ON vendor_shops
    FOR SELECT USING (status = 'approved');

CREATE POLICY "Users can view their own shops" ON vendor_shops
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can create their own shops" ON vendor_shops
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own shops" ON vendor_shops
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Admins can view all shops" ON vendor_shops
    FOR SELECT USING (is_admin(auth.uid()));

CREATE POLICY "Admins can update all shops" ON vendor_shops
    FOR UPDATE USING (is_admin(auth.uid()));

-- RLS Policies for shop_products
CREATE POLICY "Products from approved shops are viewable by everyone" ON shop_products
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM vendor_shops 
            WHERE id = shop_products.shop_id 
            AND status = 'approved'
        )
    );

CREATE POLICY "Shop owners can manage their products" ON shop_products
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM vendor_shops 
            WHERE id = shop_products.shop_id 
            AND user_id = auth.uid()
        )
    );

CREATE POLICY "Admins can view all shop products" ON shop_products
    FOR SELECT USING (is_admin(auth.uid()));

-- RLS Policies for shop_reviews
CREATE POLICY "Reviews are viewable by everyone" ON shop_reviews
    FOR SELECT USING (true);

CREATE POLICY "Users can create reviews" ON shop_reviews
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own reviews" ON shop_reviews
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own reviews" ON shop_reviews
    FOR DELETE USING (auth.uid() = user_id);

-- RLS Policies for shop_followers
CREATE POLICY "Followers are viewable by everyone" ON shop_followers
    FOR SELECT USING (true);

CREATE POLICY "Users can follow/unfollow shops" ON shop_followers
    FOR ALL USING (auth.uid() = user_id);

-- Update TABLES constant in the application
-- Add these to src/lib/supabase.ts:
-- VENDOR_SHOPS: 'vendor_shops',
-- SHOP_PRODUCTS: 'shop_products', 
-- SHOP_REVIEWS: 'shop_reviews',
-- SHOP_FOLLOWERS: 'shop_followers',
