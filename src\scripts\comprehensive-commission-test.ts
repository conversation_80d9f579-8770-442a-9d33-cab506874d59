#!/usr/bin/env tsx

/**
 * Comprehensive Commission Distribution Test Suite
 * Tests the new commission distribution system according to Commission_Distribution_Explained.md
 * 
 * This script will:
 * 1. Verify OKDOI Head and ZM accounts exist
 * 2. Test commission distribution for different package values
 * 3. Verify leftover commission allocation
 * 4. Test gift system commissions
 * 5. Validate network hierarchy
 */

import { createClient } from '@supabase/supabase-js'

// Initialize Supabase client
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing environment variables')
  console.error('Required: NEXT_PUBLIC_SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY')
  process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
})

interface TestResult {
  test: string
  status: 'PASS' | 'FAIL' | 'SKIP'
  message: string
  details?: any
}

const results: TestResult[] = []

function logTest(test: string, status: 'PASS' | 'FAIL' | 'SKIP', message: string, details?: any) {
  const emoji = status === 'PASS' ? '✅' : status === 'FAIL' ? '❌' : '⏭️'
  console.log(`${emoji} ${test}: ${message}`)
  if (details) {
    console.log('   Details:', JSON.stringify(details, null, 2))
  }
  results.push({ test, status, message, details })
}

async function testAccountSetup() {
  console.log('\n🔍 Testing Account Setup...')
  
  try {
    // Test OKDOI Head account
    const { data: okdoiHead, error: okdoiError } = await supabase
      .from('users')
      .select('id, email, user_type, full_name')
      .eq('email', '<EMAIL>')
      .eq('user_type', 'okdoi_head')
      .single()
    
    if (okdoiError || !okdoiHead) {
      logTest('OKDOI Head Account', 'FAIL', 'OKDOI Head account not found', okdoiError)
      return false
    }
    
    logTest('OKDOI Head Account', 'PASS', `Found: ${okdoiHead.full_name}`, okdoiHead)
    
    // Test ZM account
    const { data: zm, error: zmError } = await supabase
      .from('users')
      .select('id, email, user_type, full_name, referred_by_id')
      .eq('email', '<EMAIL>')
      .eq('user_type', 'zonal_manager')
      .single()
    
    if (zmError || !zm) {
      logTest('Default ZM Account', 'FAIL', 'Default ZM account not found', zmError)
      return false
    }
    
    if (zm.referred_by_id !== okdoiHead.id) {
      logTest('ZM Hierarchy', 'FAIL', 'ZM is not referred by OKDOI Head', { zm_referrer: zm.referred_by_id, okdoi_head: okdoiHead.id })
      return false
    }
    
    logTest('Default ZM Account', 'PASS', `Found: ${zm.full_name}`, zm)
    logTest('ZM Hierarchy', 'PASS', 'ZM correctly referred by OKDOI Head')
    
    return true
  } catch (error) {
    logTest('Account Setup', 'FAIL', 'Error testing account setup', error)
    return false
  }
}

async function testCommissionStructure() {
  console.log('\n🔍 Testing Commission Structure...')
  
  try {
    // Test commission structures exist for all packages
    const { data: structures, error } = await supabase
      .from('commission_structure')
      .select('*')
      .eq('commission_type', 'unified_structure')
      .order('package_value')
    
    if (error || !structures || structures.length === 0) {
      logTest('Commission Structures', 'FAIL', 'No commission structures found', error)
      return false
    }
    
    const expectedPackages = [2000, 5000, 10000, 50000]
    const foundPackages = structures.map(s => s.package_value)
    
    for (const pkg of expectedPackages) {
      if (!foundPackages.includes(pkg)) {
        logTest('Package Coverage', 'FAIL', `Missing commission structure for Rs ${pkg}`)
        return false
      }
    }
    
    logTest('Commission Structures', 'PASS', `Found ${structures.length} commission structures`)
    logTest('Package Coverage', 'PASS', 'All required packages have commission structures')
    
    // Test OKDOI Head rates
    for (const structure of structures) {
      const rateField = `okdoi_head_rate_${structure.package_value}`
      const rate = structure[rateField]
      
      if (rate === null || rate === undefined) {
        logTest('OKDOI Head Rates', 'FAIL', `Missing OKDOI Head rate for Rs ${structure.package_value}`)
        return false
      }
      
      // Verify correct rates according to document
      const expectedRate = structure.package_value === 2000 ? 0.025 : 0.02
      if (Math.abs(rate - expectedRate) > 0.001) {
        logTest('OKDOI Head Rates', 'FAIL', `Incorrect rate for Rs ${structure.package_value}: expected ${expectedRate}, got ${rate}`)
        return false
      }
    }
    
    logTest('OKDOI Head Rates', 'PASS', 'All OKDOI Head rates are correct')
    
    return true
  } catch (error) {
    logTest('Commission Structure', 'FAIL', 'Error testing commission structure', error)
    return false
  }
}

async function testCommissionFunctions() {
  console.log('\n🔍 Testing Commission Functions...')

  try {
    // Test if commission distribution functions exist by checking pg_proc
    const { data: functions, error } = await supabase
      .from('pg_proc')
      .select('proname')
      .eq('proname', 'calculate_commission_distribution')
      .limit(1)

    if (error || !functions || functions.length === 0) {
      logTest('Commission Functions', 'FAIL', 'Main commission function not found', error)
      return false
    }

    logTest('Commission Functions', 'PASS', 'Main commission distribution function exists')

    // Test OKDOI Head rate function
    const { data: rateFunction, error: rateFuncError } = await supabase
      .from('pg_proc')
      .select('proname')
      .eq('proname', 'get_okdoi_head_commission_rate')
      .limit(1)

    if (rateFuncError || !rateFunction || rateFunction.length === 0) {
      logTest('OKDOI Head Rate Function', 'FAIL', 'OKDOI Head rate function not found', rateFuncError)
      return false
    }

    logTest('OKDOI Head Rate Function', 'PASS', 'OKDOI Head rate function exists')

    return true
  } catch (error) {
    logTest('Commission Functions', 'FAIL', 'Error testing commission functions', error)
    return false
  }
}

async function simulateCommissionDistribution() {
  console.log('\n🔍 Simulating Commission Distribution...')
  
  try {
    // Get test users
    const { data: okdoiHead } = await supabase
      .from('users')
      .select('id')
      .eq('user_type', 'okdoi_head')
      .single()
    
    const { data: zm } = await supabase
      .from('users')
      .select('id')
      .eq('email', '<EMAIL>')
      .single()
    
    const { data: testUser } = await supabase
      .from('users')
      .select('id')
      .eq('referred_by_id', zm.id)
      .eq('user_type', 'user')
      .limit(1)
      .single()
    
    if (!okdoiHead || !zm || !testUser) {
      logTest('Test Users', 'FAIL', 'Required test users not found')
      return false
    }
    
    logTest('Test Users', 'PASS', 'Found required test users for simulation')
    
    // Create a test subscription purchase
    const testPackageId = crypto.randomUUID()
    const testAmount = 2000
    
    // Get initial wallet balances
    const { data: initialBalances } = await supabase
      .from('user_wallets')
      .select('user_id, balance')
      .in('user_id', [okdoiHead.id, zm.id, testUser.id])
    
    logTest('Initial Balances', 'PASS', 'Retrieved initial wallet balances', initialBalances)
    
    // Simulate commission distribution
    const { error: commissionError } = await supabase
      .rpc('calculate_commission_distribution', {
        purchaser_id: testUser.id,
        package_id: testPackageId,
        package_amount: testAmount
      })
    
    if (commissionError) {
      logTest('Commission Distribution', 'FAIL', 'Error in commission distribution', commissionError)
      return false
    }
    
    logTest('Commission Distribution', 'PASS', 'Commission distribution executed successfully')
    
    // Check if commissions were created
    const { data: commissions } = await supabase
      .from('commission_transactions')
      .select('*')
      .eq('subscription_purchase_id', testPackageId)
    
    if (!commissions || commissions.length === 0) {
      logTest('Commission Records', 'FAIL', 'No commission transactions created')
      return false
    }
    
    logTest('Commission Records', 'PASS', `Created ${commissions.length} commission transactions`)
    
    // Verify OKDOI Head received commission
    const okdoiCommissions = commissions.filter(c => c.beneficiary_id === okdoiHead.id)
    if (okdoiCommissions.length === 0) {
      logTest('OKDOI Head Commission', 'FAIL', 'OKDOI Head did not receive commission')
      return false
    }
    
    logTest('OKDOI Head Commission', 'PASS', `OKDOI Head received ${okdoiCommissions.length} commissions`)
    
    // Verify purchaser received self-commissions
    const selfCommissions = commissions.filter(c => c.beneficiary_id === testUser.id)
    if (selfCommissions.length === 0) {
      logTest('Self Commission', 'FAIL', 'Purchaser did not receive self-commissions')
      return false
    }
    
    logTest('Self Commission', 'PASS', `Purchaser received ${selfCommissions.length} self-commissions`)
    
    return true
  } catch (error) {
    logTest('Commission Simulation', 'FAIL', 'Error in commission simulation', error)
    return false
  }
}

async function runAllTests() {
  console.log('🚀 Starting Comprehensive Commission Distribution Tests\n')
  
  const testSuite = [
    testAccountSetup,
    testCommissionStructure,
    testCommissionFunctions,
    simulateCommissionDistribution
  ]
  
  let passedTests = 0
  let totalTests = 0
  
  for (const test of testSuite) {
    try {
      const result = await test()
      if (result) passedTests++
    } catch (error) {
      console.error('❌ Test suite error:', error)
    }
  }
  
  // Count individual test results
  totalTests = results.length
  const passed = results.filter(r => r.status === 'PASS').length
  const failed = results.filter(r => r.status === 'FAIL').length
  const skipped = results.filter(r => r.status === 'SKIP').length
  
  console.log('\n📊 Test Results Summary:')
  console.log(`✅ Passed: ${passed}`)
  console.log(`❌ Failed: ${failed}`)
  console.log(`⏭️ Skipped: ${skipped}`)
  console.log(`📈 Success Rate: ${((passed / totalTests) * 100).toFixed(1)}%`)
  
  if (failed > 0) {
    console.log('\n❌ Failed Tests:')
    results.filter(r => r.status === 'FAIL').forEach(r => {
      console.log(`   - ${r.test}: ${r.message}`)
    })
  }
  
  console.log('\n🎯 Commission System Status:', failed === 0 ? '✅ READY FOR PRODUCTION' : '❌ NEEDS ATTENTION')
  
  return failed === 0
}

// Run tests if this script is executed directly
if (require.main === module) {
  runAllTests()
    .then(success => {
      process.exit(success ? 0 : 1)
    })
    .catch(error => {
      console.error('💥 Test execution failed:', error)
      process.exit(1)
    })
}

export { runAllTests }
