const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');

// Load environment variables
require('dotenv').config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase configuration in .env.local');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

// Complete backup structure
const completeBackup = {
  timestamp: new Date().toISOString(),
  supabase_url: supabaseUrl,
  project_ref: supabaseUrl.split('//')[1].split('.')[0],
  backup_type: 'COMPLETE_SUPABASE_BACKUP',
  components: {
    tables: {},
    functions: {},
    storage_buckets: {},
    rls_policies: {},
    triggers: {},
    views: {},
    indexes: {},
    constraints: {},
    sequences: {},
    extensions: {},
    custom_types: {},
    auth_settings: {}
  }
};

// Get all tables with data
async function backupTables() {
  console.log('📊 Backing up all tables and data...');
  
  const tables = [
    'users', 'categories', 'subcategories', 'ads', 'ad_images', 'ad_boosts', 'ad_drafts',
    'user_favorites', 'chat_conversations', 'chat_messages', 'subscription_packages',
    'user_subscriptions', 'boost_packages', 'vendor_shops', 'shop_categories',
    'shop_products', 'shop_product_images', 'product_reviews', 'shop_orders',
    'order_items', 'order_status_history', 'cart_items', 'user_wallets',
    'wallet_transactions', 'deposit_requests', 'withdrawal_requests', 'p2p_transfers',
    'merchant_wallets', 'merchant_wallet_transactions', 'merchant_to_main_transfers',
    'kyc_document_types', 'kyc_submissions', 'kyc_status_history', 'referral_codes',
    'referrals', 'referral_hierarchy', 'referral_placements', 'regional_sales_managers',
    'zonal_managers', 'commission_structure', 'commission_transactions',
    'present_allocations', 'gift_transactions', 'present_pools', 'user_tasks',
    'user_task_assignments', 'gift_system_audit_log', 'user_rewards',
    'shop_reviews', 'shop_followers', 'districts', 'cities', 'admin_settings'
  ];
  
  for (const table of tables) {
    try {
      const { data, error } = await supabase.from(table).select('*');
      
      if (error) {
        console.log(`  ⚠️  ${table}: ${error.message}`);
        completeBackup.components.tables[table] = { error: error.message, count: 0, data: [] };
      } else {
        console.log(`  ✅ ${table}: ${data?.length || 0} records`);
        completeBackup.components.tables[table] = {
          count: data?.length || 0,
          data: data || []
        };
      }
    } catch (err) {
      console.log(`  ❌ ${table}: ${err.message}`);
      completeBackup.components.tables[table] = { error: err.message, count: 0, data: [] };
    }
  }
}

// Get RLS policies
async function backupRLSPolicies() {
  console.log('🔒 Backing up RLS policies...');
  
  try {
    // Query to get all RLS policies
    const { data, error } = await supabase.rpc('get_rls_policies');
    
    if (error) {
      console.log('  Using alternative method for RLS policies...');
      // Alternative: Try to get policies using SQL
      const policyQuery = `
        SELECT 
          schemaname,
          tablename,
          policyname,
          permissive,
          roles,
          cmd,
          qual,
          with_check
        FROM pg_policies 
        WHERE schemaname = 'public'
        ORDER BY tablename, policyname;
      `;
      
      try {
        const { data: policies, error: policyError } = await supabase.rpc('execute_sql', { query: policyQuery });
        completeBackup.components.rls_policies = policies || [];
        console.log(`  ✅ RLS Policies: ${policies?.length || 0} policies`);
      } catch (err) {
        console.log('  ⚠️  Could not backup RLS policies');
        completeBackup.components.rls_policies = { error: 'Could not retrieve RLS policies' };
      }
    } else {
      completeBackup.components.rls_policies = data;
      console.log(`  ✅ RLS Policies: ${data?.length || 0} policies`);
    }
  } catch (err) {
    console.log('  ⚠️  RLS policies backup failed:', err.message);
    completeBackup.components.rls_policies = { error: err.message };
  }
}

// Get storage buckets
async function backupStorageBuckets() {
  console.log('🗄️  Backing up storage buckets...');
  
  try {
    const { data: buckets, error } = await supabase.storage.listBuckets();
    
    if (error) {
      console.log('  ⚠️  Storage buckets error:', error.message);
      completeBackup.components.storage_buckets = { error: error.message };
      return;
    }
    
    completeBackup.components.storage_buckets = {};
    
    for (const bucket of buckets) {
      console.log(`  📁 Processing bucket: ${bucket.name}`);
      
      // Get bucket details
      const bucketInfo = {
        id: bucket.id,
        name: bucket.name,
        public: bucket.public,
        created_at: bucket.created_at,
        updated_at: bucket.updated_at,
        file_size_limit: bucket.file_size_limit,
        allowed_mime_types: bucket.allowed_mime_types,
        files: []
      };
      
      // List files in bucket (first level only to avoid huge backups)
      try {
        const { data: files, error: filesError } = await supabase.storage
          .from(bucket.name)
          .list('', { limit: 100 });
        
        if (!filesError && files) {
          bucketInfo.files = files.map(file => ({
            name: file.name,
            id: file.id,
            updated_at: file.updated_at,
            created_at: file.created_at,
            last_accessed_at: file.last_accessed_at,
            metadata: file.metadata
          }));
          console.log(`    ✅ ${files.length} files listed`);
        } else {
          console.log(`    ⚠️  Could not list files: ${filesError?.message}`);
        }
      } catch (err) {
        console.log(`    ⚠️  File listing error: ${err.message}`);
      }
      
      completeBackup.components.storage_buckets[bucket.name] = bucketInfo;
    }
    
    console.log(`  ✅ Storage Buckets: ${buckets.length} buckets`);
  } catch (err) {
    console.log('  ❌ Storage backup failed:', err.message);
    completeBackup.components.storage_buckets = { error: err.message };
  }
}

// Get database functions
async function backupFunctions() {
  console.log('⚙️  Backing up database functions...');
  
  try {
    const functionQuery = `
      SELECT 
        n.nspname as schema_name,
        p.proname as function_name,
        pg_get_function_result(p.oid) as return_type,
        pg_get_function_arguments(p.oid) as arguments,
        pg_get_functiondef(p.oid) as definition,
        l.lanname as language,
        p.prosecdef as security_definer,
        p.provolatile as volatility
      FROM pg_proc p
      LEFT JOIN pg_namespace n ON p.pronamespace = n.oid
      LEFT JOIN pg_language l ON p.prolang = l.oid
      WHERE n.nspname IN ('public', 'auth')
      AND p.proname NOT LIKE 'pg_%'
      ORDER BY n.nspname, p.proname;
    `;
    
    const { data: functions, error } = await supabase.rpc('execute_sql', { query: functionQuery });
    
    if (error) {
      console.log('  ⚠️  Functions backup error:', error.message);
      completeBackup.components.functions = { error: error.message };
    } else {
      completeBackup.components.functions = functions || [];
      console.log(`  ✅ Database Functions: ${functions?.length || 0} functions`);
    }
  } catch (err) {
    console.log('  ❌ Functions backup failed:', err.message);
    completeBackup.components.functions = { error: err.message };
  }
}

// Get triggers
async function backupTriggers() {
  console.log('🔄 Backing up triggers...');
  
  try {
    const triggerQuery = `
      SELECT 
        t.trigger_name,
        t.event_manipulation,
        t.event_object_schema,
        t.event_object_table,
        t.action_statement,
        t.action_timing,
        t.action_orientation
      FROM information_schema.triggers t
      WHERE t.event_object_schema = 'public'
      ORDER BY t.event_object_table, t.trigger_name;
    `;
    
    const { data: triggers, error } = await supabase.rpc('execute_sql', { query: triggerQuery });
    
    if (error) {
      console.log('  ⚠️  Triggers backup error:', error.message);
      completeBackup.components.triggers = { error: error.message };
    } else {
      completeBackup.components.triggers = triggers || [];
      console.log(`  ✅ Triggers: ${triggers?.length || 0} triggers`);
    }
  } catch (err) {
    console.log('  ❌ Triggers backup failed:', err.message);
    completeBackup.components.triggers = { error: err.message };
  }
}

// Get views
async function backupViews() {
  console.log('👁️  Backing up views...');
  
  try {
    const viewQuery = `
      SELECT 
        table_name as view_name,
        view_definition
      FROM information_schema.views
      WHERE table_schema = 'public'
      ORDER BY table_name;
    `;
    
    const { data: views, error } = await supabase.rpc('execute_sql', { query: viewQuery });
    
    if (error) {
      console.log('  ⚠️  Views backup error:', error.message);
      completeBackup.components.views = { error: error.message };
    } else {
      completeBackup.components.views = views || [];
      console.log(`  ✅ Views: ${views?.length || 0} views`);
    }
  } catch (err) {
    console.log('  ❌ Views backup failed:', err.message);
    completeBackup.components.views = { error: err.message };
  }
}

// Get indexes
async function backupIndexes() {
  console.log('📇 Backing up indexes...');

  try {
    const indexQuery = `
      SELECT
        schemaname,
        tablename,
        indexname,
        indexdef
      FROM pg_indexes
      WHERE schemaname = 'public'
      ORDER BY tablename, indexname;
    `;

    const { data: indexes, error } = await supabase.rpc('execute_sql', { query: indexQuery });

    if (error) {
      console.log('  ⚠️  Indexes backup error:', error.message);
      completeBackup.components.indexes = { error: error.message };
    } else {
      completeBackup.components.indexes = indexes || [];
      console.log(`  ✅ Indexes: ${indexes?.length || 0} indexes`);
    }
  } catch (err) {
    console.log('  ❌ Indexes backup failed:', err.message);
    completeBackup.components.indexes = { error: err.message };
  }
}

// Get constraints
async function backupConstraints() {
  console.log('🔒 Backing up constraints...');

  try {
    const constraintQuery = `
      SELECT
        tc.table_name,
        tc.constraint_name,
        tc.constraint_type,
        kcu.column_name,
        ccu.table_name AS foreign_table_name,
        ccu.column_name AS foreign_column_name,
        rc.update_rule,
        rc.delete_rule
      FROM information_schema.table_constraints tc
      LEFT JOIN information_schema.key_column_usage kcu
        ON tc.constraint_name = kcu.constraint_name
      LEFT JOIN information_schema.constraint_column_usage ccu
        ON ccu.constraint_name = tc.constraint_name
      LEFT JOIN information_schema.referential_constraints rc
        ON tc.constraint_name = rc.constraint_name
      WHERE tc.table_schema = 'public'
      ORDER BY tc.table_name, tc.constraint_type;
    `;

    const { data: constraints, error } = await supabase.rpc('execute_sql', { query: constraintQuery });

    if (error) {
      console.log('  ⚠️  Constraints backup error:', error.message);
      completeBackup.components.constraints = { error: error.message };
    } else {
      completeBackup.components.constraints = constraints || [];
      console.log(`  ✅ Constraints: ${constraints?.length || 0} constraints`);
    }
  } catch (err) {
    console.log('  ❌ Constraints backup failed:', err.message);
    completeBackup.components.constraints = { error: err.message };
  }
}

// Get sequences
async function backupSequences() {
  console.log('🔢 Backing up sequences...');

  try {
    const sequenceQuery = `
      SELECT
        sequence_name,
        data_type,
        start_value,
        minimum_value,
        maximum_value,
        increment,
        cycle_option
      FROM information_schema.sequences
      WHERE sequence_schema = 'public'
      ORDER BY sequence_name;
    `;

    const { data: sequences, error } = await supabase.rpc('execute_sql', { query: sequenceQuery });

    if (error) {
      console.log('  ⚠️  Sequences backup error:', error.message);
      completeBackup.components.sequences = { error: error.message };
    } else {
      completeBackup.components.sequences = sequences || [];
      console.log(`  ✅ Sequences: ${sequences?.length || 0} sequences`);
    }
  } catch (err) {
    console.log('  ❌ Sequences backup failed:', err.message);
    completeBackup.components.sequences = { error: err.message };
  }
}

// Get extensions
async function backupExtensions() {
  console.log('🧩 Backing up extensions...');

  try {
    const extensionQuery = `
      SELECT
        extname as extension_name,
        extversion as version,
        extrelocatable as relocatable
      FROM pg_extension
      ORDER BY extname;
    `;

    const { data: extensions, error } = await supabase.rpc('execute_sql', { query: extensionQuery });

    if (error) {
      console.log('  ⚠️  Extensions backup error:', error.message);
      completeBackup.components.extensions = { error: error.message };
    } else {
      completeBackup.components.extensions = extensions || [];
      console.log(`  ✅ Extensions: ${extensions?.length || 0} extensions`);
    }
  } catch (err) {
    console.log('  ❌ Extensions backup failed:', err.message);
    completeBackup.components.extensions = { error: err.message };
  }
}

// Get custom types
async function backupCustomTypes() {
  console.log('🏷️  Backing up custom types...');

  try {
    const typeQuery = `
      SELECT
        t.typname as type_name,
        t.typtype as type_type,
        n.nspname as schema_name,
        pg_catalog.format_type(t.oid, NULL) AS type_definition
      FROM pg_type t
      LEFT JOIN pg_namespace n ON n.oid = t.typnamespace
      WHERE n.nspname = 'public'
      AND t.typtype IN ('e', 'c', 'd')
      ORDER BY t.typname;
    `;

    const { data: types, error } = await supabase.rpc('execute_sql', { query: typeQuery });

    if (error) {
      console.log('  ⚠️  Custom types backup error:', error.message);
      completeBackup.components.custom_types = { error: error.message };
    } else {
      completeBackup.components.custom_types = types || [];
      console.log(`  ✅ Custom Types: ${types?.length || 0} types`);
    }
  } catch (err) {
    console.log('  ❌ Custom types backup failed:', err.message);
    completeBackup.components.custom_types = { error: err.message };
  }
}

// Main backup function
async function createCompleteBackup() {
  console.log('🚀 Starting COMPLETE Supabase backup...');
  console.log('=' .repeat(60));

  try {
    await backupTables();
    await backupRLSPolicies();
    await backupStorageBuckets();
    await backupFunctions();
    await backupTriggers();
    await backupViews();
    await backupIndexes();
    await backupConstraints();
    await backupSequences();
    await backupExtensions();
    await backupCustomTypes();

    // Save complete backup
    const backupFileName = `complete_supabase_backup_${new Date().toISOString().replace(/[:.]/g, '-')}.json`;
    const backupPath = path.join(__dirname, backupFileName);

    fs.writeFileSync(backupPath, JSON.stringify(completeBackup, null, 2));

    console.log('\n' + '='.repeat(60));
    console.log('🎉 COMPLETE SUPABASE BACKUP FINISHED!');
    console.log('='.repeat(60));
    console.log(`📁 Backup saved to: ${backupPath}`);
    console.log(`📊 Components backed up:`);
    console.log(`   • Tables: ${Object.keys(completeBackup.components.tables).length}`);
    console.log(`   • Storage Buckets: ${Object.keys(completeBackup.components.storage_buckets).length || 'N/A'}`);
    console.log(`   • RLS Policies: ${Array.isArray(completeBackup.components.rls_policies) ? completeBackup.components.rls_policies.length : 'N/A'}`);
    console.log(`   • Functions: ${Array.isArray(completeBackup.components.functions) ? completeBackup.components.functions.length : 'N/A'}`);
    console.log(`   • Triggers: ${Array.isArray(completeBackup.components.triggers) ? completeBackup.components.triggers.length : 'N/A'}`);
    console.log(`   • Views: ${Array.isArray(completeBackup.components.views) ? completeBackup.components.views.length : 'N/A'}`);
    console.log(`   • Indexes: ${Array.isArray(completeBackup.components.indexes) ? completeBackup.components.indexes.length : 'N/A'}`);
    console.log(`   • Constraints: ${Array.isArray(completeBackup.components.constraints) ? completeBackup.components.constraints.length : 'N/A'}`);
    console.log(`   • Sequences: ${Array.isArray(completeBackup.components.sequences) ? completeBackup.components.sequences.length : 'N/A'}`);
    console.log(`   • Extensions: ${Array.isArray(completeBackup.components.extensions) ? completeBackup.components.extensions.length : 'N/A'}`);
    console.log(`   • Custom Types: ${Array.isArray(completeBackup.components.custom_types) ? completeBackup.components.custom_types.length : 'N/A'}`);

    return completeBackup;
  } catch (error) {
    console.error('❌ Complete backup failed:', error);
    throw error;
  }
}

// Run the complete backup
createCompleteBackup().catch(console.error);
