'use client'

import { supabase } from '@/lib/supabase'
import { AdminService } from '@/lib/services/admin'
import { sessionManager } from '@/lib/sessionManager'
import { adminLogger } from '@/lib/adminLogger'

export interface HealthCheckResult {
  component: string
  status: 'pass' | 'fail' | 'warn'
  message: string
  details?: any
  duration?: number
}

export interface AdminPanelHealthReport {
  overall: 'healthy' | 'degraded' | 'unhealthy'
  timestamp: string
  results: HealthCheckResult[]
  summary: {
    passed: number
    failed: number
    warnings: number
    totalDuration: number
  }
}

export class AdminPanelHealthChecker {
  static async runHealthCheck(): Promise<AdminPanelHealthReport> {
    const startTime = Date.now()
    const results: HealthCheckResult[] = []

    adminLogger.info('admin_check', 'Starting admin panel health check')

    // Test 1: Session Management
    try {
      const sessionStart = Date.now()
      const sessionState = sessionManager.getState()
      const sessionDuration = Date.now() - sessionStart

      results.push({
        component: 'Session Manager',
        status: sessionState.session ? 'pass' : 'fail',
        message: sessionState.session 
          ? 'Session manager is working correctly'
          : 'No active session found',
        details: {
          hasSession: !!sessionState.session,
          hasUser: !!sessionState.user,
          isLoading: sessionState.isLoading,
          error: sessionState.error
        },
        duration: sessionDuration
      })
    } catch (error) {
      results.push({
        component: 'Session Manager',
        status: 'fail',
        message: 'Session manager error',
        details: { error: error instanceof Error ? error.message : error }
      })
    }

    // Test 2: Supabase Connection
    try {
      const supabaseStart = Date.now()
      const { data, error } = await supabase.auth.getSession()
      const supabaseDuration = Date.now() - supabaseStart

      results.push({
        component: 'Supabase Connection',
        status: error ? 'fail' : 'pass',
        message: error 
          ? `Supabase connection failed: ${error.message}`
          : 'Supabase connection is healthy',
        details: {
          hasSession: !!data.session,
          error: error?.message
        },
        duration: supabaseDuration
      })
    } catch (error) {
      results.push({
        component: 'Supabase Connection',
        status: 'fail',
        message: 'Supabase connection error',
        details: { error: error instanceof Error ? error.message : error }
      })
    }

    // Test 3: Admin Service
    try {
      const adminStart = Date.now()
      const isAdmin = await AdminService.isAdmin()
      const adminDuration = Date.now() - adminStart

      results.push({
        component: 'Admin Service',
        status: adminDuration > 5000 ? 'warn' : 'pass',
        message: adminDuration > 5000 
          ? `Admin check is slow (${adminDuration}ms)`
          : `Admin check completed successfully (${adminDuration}ms)`,
        details: {
          isAdmin,
          duration: adminDuration,
          cacheSize: AdminService['adminStatusCache']?.size || 0
        },
        duration: adminDuration
      })
    } catch (error) {
      results.push({
        component: 'Admin Service',
        status: 'fail',
        message: 'Admin service error',
        details: { error: error instanceof Error ? error.message : error }
      })
    }

    // Test 4: Database Connectivity
    try {
      const dbStart = Date.now()
      const { data, error } = await supabase
        .from('users')
        .select('id')
        .limit(1)
        .single()
      const dbDuration = Date.now() - dbStart

      results.push({
        component: 'Database Connectivity',
        status: error && error.code !== 'PGRST116' ? 'fail' : 'pass',
        message: error && error.code !== 'PGRST116'
          ? `Database query failed: ${error.message}`
          : `Database connectivity is healthy (${dbDuration}ms)`,
        details: {
          hasData: !!data,
          error: error?.message,
          errorCode: error?.code
        },
        duration: dbDuration
      })
    } catch (error) {
      results.push({
        component: 'Database Connectivity',
        status: 'fail',
        message: 'Database connectivity error',
        details: { error: error instanceof Error ? error.message : error }
      })
    }

    // Test 5: Authentication Flow
    try {
      const authStart = Date.now()
      const { data: { user } } = await supabase.auth.getUser()
      const authDuration = Date.now() - authStart

      results.push({
        component: 'Authentication Flow',
        status: user ? 'pass' : 'fail',
        message: user 
          ? 'Authentication flow is working'
          : 'No authenticated user found',
        details: {
          hasUser: !!user,
          userId: user?.id,
          userEmail: user?.email
        },
        duration: authDuration
      })
    } catch (error) {
      results.push({
        component: 'Authentication Flow',
        status: 'fail',
        message: 'Authentication flow error',
        details: { error: error instanceof Error ? error.message : error }
      })
    }

    // Test 6: Logger Functionality
    try {
      const loggerStart = Date.now()
      const logCount = adminLogger.getLogs().length
      const issueSummary = adminLogger.getIssueSummary()
      const loggerDuration = Date.now() - loggerStart

      results.push({
        component: 'Admin Logger',
        status: issueSummary.totalErrors > 10 ? 'warn' : 'pass',
        message: issueSummary.totalErrors > 10
          ? `High error count detected (${issueSummary.totalErrors} errors)`
          : 'Logger is functioning normally',
        details: {
          totalLogs: logCount,
          totalErrors: issueSummary.totalErrors,
          totalWarnings: issueSummary.totalWarnings,
          isEnabled: adminLogger.isLoggingEnabled()
        },
        duration: loggerDuration
      })
    } catch (error) {
      results.push({
        component: 'Admin Logger',
        status: 'fail',
        message: 'Logger functionality error',
        details: { error: error instanceof Error ? error.message : error }
      })
    }

    // Calculate summary
    const passed = results.filter(r => r.status === 'pass').length
    const failed = results.filter(r => r.status === 'fail').length
    const warnings = results.filter(r => r.status === 'warn').length
    const totalDuration = Date.now() - startTime

    // Determine overall health
    let overall: 'healthy' | 'degraded' | 'unhealthy'
    if (failed > 0) {
      overall = 'unhealthy'
    } else if (warnings > 0) {
      overall = 'degraded'
    } else {
      overall = 'healthy'
    }

    const report: AdminPanelHealthReport = {
      overall,
      timestamp: new Date().toISOString(),
      results,
      summary: {
        passed,
        failed,
        warnings,
        totalDuration
      }
    }

    adminLogger.info('admin_check', 'Health check completed', {
      overall,
      passed,
      failed,
      warnings,
      totalDuration: `${totalDuration}ms`
    })

    return report
  }

  static async runQuickCheck(): Promise<boolean> {
    try {
      // Quick checks for critical functionality
      const { data: { session } } = await supabase.auth.getSession()
      if (!session) return false

      const isAdmin = await AdminService.isAdmin()
      return isAdmin
    } catch (error) {
      adminLogger.error('admin_check', 'Quick health check failed', {
        error: error instanceof Error ? error.message : error
      })
      return false
    }
  }
}

// Export convenience functions
export const runAdminHealthCheck = AdminPanelHealthChecker.runHealthCheck
export const runQuickAdminCheck = AdminPanelHealthChecker.runQuickCheck
