# OKDOI Database Backup System

This directory contains comprehensive database backup and restore tools for your OKDOI Supabase project.

## Files Overview

- `create_backup.js` - Creates complete data backup of all tables
- `create_schema_backup.js` - Creates schema backup and copies migrations
- `restore_backup.js` - Restores data from backup files
- `README.md` - This documentation file

## Prerequisites

Make sure you have:
- Node.js installed
- All npm dependencies installed (`npm install`)
- Proper `.env.local` file with Supa<PERSON> credentials in the root directory

## Creating Backups

### 1. Complete Data Backup

Creates a JSON backup of all table data:

```bash
node create_backup.js
```

This will:
- Export all data from all tables
- Create a timestamped JSON file with complete backup
- Generate a summary file with table counts
- Show progress for each table

### 2. Schema Backup

Creates a backup of database schema and migrations:

```bash
node create_schema_backup.js
```

This will:
- Export table structure information
- Export column definitions
- Export foreign key constraints
- Export indexes
- Copy all migration files to `migrations_backup/`

## Restoring Backups

### Basic Restore

Restore all tables from a backup file:

```bash
node restore_backup.js complete_backup_2025-01-09T12-00-00-000Z.json
```

### Clear Tables Before Restore

Clear existing data before restoring:

```bash
node restore_backup.js complete_backup_2025-01-09T12-00-00-000Z.json --clear-tables
```

### Restore Specific Tables Only

Restore only specific tables:

```bash
node restore_backup.js complete_backup_2025-01-09T12-00-00-000Z.json --tables users,ads,categories
```

## Backup File Structure

### Data Backup JSON Structure
```json
{
  "timestamp": "2025-01-09T12:00:00.000Z",
  "supabase_url": "https://your-project.supabase.co",
  "tables": {
    "users": {
      "table": "users",
      "count": 150,
      "data": [...]
    },
    "ads": {
      "table": "ads", 
      "count": 500,
      "data": [...]
    }
  }
}
```

### Schema Backup JSON Structure
```json
{
  "timestamp": "2025-01-09T12:00:00.000Z",
  "supabase_url": "https://your-project.supabase.co",
  "tables": [...],
  "columns": [...],
  "foreign_keys": [...],
  "indexes": [...]
}
```

## Tables Included in Backup

The backup system covers all OKDOI tables:

**Core Tables:**
- users
- categories
- subcategories
- ads
- ad_images
- ad_views
- favorites

**Communication:**
- chats
- chat_messages

**Subscriptions & Boosts:**
- subscriptions
- user_subscriptions
- boost_purchases

**E-commerce:**
- vendor_shops
- shop_categories
- shop_products
- product_images
- product_reviews
- orders
- order_items

**Financial:**
- wallets
- wallet_transactions
- deposit_requests
- merchant_wallets
- merchant_wallet_transactions

**KYC & Verification:**
- kyc_documents
- kyc_submissions

**Referral System:**
- referral_codes
- referrals
- commission_structures
- commission_transactions

**Administration:**
- admin_settings

## Best Practices

1. **Regular Backups**: Run backups regularly, especially before major updates
2. **Test Restores**: Periodically test restore process on development environment
3. **Multiple Backup Locations**: Store backups in multiple secure locations
4. **Version Control**: Keep migration files in version control
5. **Documentation**: Document any manual schema changes

## Troubleshooting

### Common Issues

1. **Permission Errors**: Ensure service role key has proper permissions
2. **Large Tables**: For very large tables, consider implementing pagination
3. **Foreign Key Constraints**: When restoring, you may need to restore in dependency order
4. **RLS Policies**: Service role bypasses RLS, but be aware of policy implications

### Error Handling

The scripts include comprehensive error handling:
- Individual table failures won't stop the entire process
- Detailed error messages for troubleshooting
- Progress indicators for long-running operations
- Batch processing to handle large datasets

## Security Notes

- Backup files contain sensitive data - store securely
- Service role key has full database access - protect it
- Consider encrypting backup files for additional security
- Regularly rotate access keys

## Support

For issues or questions:
1. Check the console output for detailed error messages
2. Verify Supabase credentials and permissions
3. Ensure all dependencies are installed
4. Check network connectivity to Supabase

---

**Last Updated**: January 2025
**Version**: 1.0.0
