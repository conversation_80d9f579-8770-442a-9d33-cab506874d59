import { supabase } from './supabase'
import { User } from '@/types'
import { AdminSettingsService } from './services/adminSettings'
import { ReferralSystemService } from './services/referralSystem'

export interface AuthUser {
  id: string
  email: string
  full_name?: string
  phone?: string
  location?: string
  avatar_url?: string
}

export class AuthService {
  /**
   * Sign up a new user with OTP verification and referral processing
   */
  static async signUp(email: string, password: string, userData?: Partial<User> & { referralCode?: string }) {
    // Check if email verification is required (with fallback to false if table doesn't exist)
    let requireEmailVerification = false
    try {
      requireEmailVerification = await AdminSettingsService.getSetting('require_email_verification') || false
    } catch (error) {
      // Gracefully handle admin settings access failures
      console.warn('Could not fetch email verification setting, defaulting to false:', error)
      requireEmailVerification = false

      // If it's a 406 error or RLS policy error, it means the table exists but access is restricted
      // This is expected during signup when user is not authenticated yet
      if (error instanceof Error && (error.message.includes('406') || error.message.includes('policy'))) {
        console.info('Admin settings access restricted during signup - using default values')
      }
    }

    // Validate referral code if provided (check both camelCase and snake_case)
    let referrer: User | null = null
    const referralCode = userData?.referralCode || userData?.referral_code
    if (referralCode) {
      try {
        console.log('Validating referral code:', referralCode)
        referrer = await ReferralSystemService.validateReferralCode(referralCode)
        if (!referrer) {
          console.warn('Invalid referral code:', referralCode)
          throw new Error('Invalid referral code')
        }
        console.log('✅ Referral code validated successfully. Referrer:', referrer.email)
      } catch (error) {
        console.warn('Could not validate referral code:', error)
        // Don't fail signup for referral code issues, just log the warning
      }
    }

    const { data, error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: {
          ...userData,
          referral_code: referralCode, // Store referral code in auth metadata (use the validated one)
          full_name: userData?.full_name,
          phone: userData?.phone,
          location: userData?.location,
          gender: userData?.gender,
          religion: userData?.religion,
          referrer_id: referrer?.id,
          referrer_email: referrer?.email // Store referrer info for debugging
        },
        // Don't set emailRedirectTo for OTP flow
      }
    })

    if (error) {
      throw new Error(error.message)
    }

    // Always create user profile immediately (regardless of email verification)
    if (data.user) {
      try {
        // Create profile with referral information if available
        if (referrer) {
          await this.createUserProfileWithReferrer(data.user, userData, referrer)
        } else {
          await this.createBasicUserProfile(data.user, userData)
        }
      } catch (profileError) {
        console.error('Error creating user profile:', profileError)
        // Don't fail the signup, but log the error
      }
    }

    return { data, requireEmailVerification, referrer }
  }

  /**
   * Create user profile with referrer information and hierarchy placement
   */
  private static async createUserProfileWithReferrer(authUser: any, userData?: Partial<User> & { referralCode?: string }, referrer?: User | null) {
    console.log('Creating user profile with referrer for:', authUser.email)

    try {
      // Calculate referral path for the new user
      const referrerPath = referrer?.referral_path || ''
      const newUserPath = referrerPath ? `${referrerPath}/${authUser.id}` : `/${authUser.id}`

      // Use server-side API route for profile creation with referrer data
      const response = await fetch('/api/auth/create-profile', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId: authUser.id,
          email: authUser.email,
          userData: {
            full_name: userData?.full_name,
            phone: userData?.phone,
            location: userData?.location,
            referral_level: referrer ? (referrer.referral_level || 0) + 1 : 0,
            referred_by_id: referrer?.id || null,
            referral_path: newUserPath
          }
        })
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to create user profile')
      }

      // Place user in referral hierarchy immediately after profile creation
      if (referrer) {
        try {
          console.log('🔗 Placing user in referral hierarchy...')
          console.log('New user ID:', authUser.id)
          console.log('Referrer ID:', referrer.id)

          // Check if user is already placed to prevent duplicates
          const { data: existingPlacement } = await supabase
            .from('referral_placements')
            .select('id')
            .eq('child_id', authUser.id)
            .single()

          if (existingPlacement) {
            console.log('✅ User already placed in referral hierarchy')
          } else {
            await ReferralSystemService.placeUserInHierarchy(authUser.id, referrer.id)
            console.log('✅ User placed in referral hierarchy successfully')
          }

          // Verify the placement worked
          const { data: verifyUser } = await supabase
            .from('users')
            .select('referred_by_id, referral_level, direct_referrals_count')
            .eq('id', authUser.id)
            .single()

          console.log('✅ Referral linking verification:', {
            userId: authUser.id,
            referredById: verifyUser?.referred_by_id,
            referralLevel: verifyUser?.referral_level,
            expectedReferrerId: referrer.id
          })

        } catch (referralError) {
          console.error('❌ Failed to place user in referral hierarchy:', referralError)
          // Don't fail the profile creation, but log the error
        }
      } else {
        console.log('ℹ️ No referrer found, user will not be placed in hierarchy')
      }

      console.log('✅ User profile with referrer created successfully')
    } catch (error) {
      console.error('Error creating user profile with referrer:', error)
      throw error
    }
  }

  /**
   * Create basic user profile without complex operations (fast)
   * Uses server-side API route to bypass RLS issues during signup
   */
  private static async createBasicUserProfile(authUser: any, userData?: Partial<User> & { referralCode?: string }) {
    console.log('Creating basic user profile for:', authUser.email)

    const maxRetries = 3
    let lastError: Error | null = null

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        console.log(`Profile creation attempt ${attempt}/${maxRetries}`)

        // Use server-side API route for profile creation
        const response = await fetch('/api/auth/create-profile', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            userId: authUser.id,
            email: authUser.email,
            userData: {
              full_name: userData?.full_name,
              phone: userData?.phone,
              location: userData?.location,
              referral_level: 0,
              referred_by_id: null,
              referral_path: `/${authUser.id}` // Set basic path for user
            }
          })
        })

        if (!response.ok) {
          const errorData = await response.json()
          const errorMessage = errorData.error || 'Failed to create user profile'

          // Don't retry for client errors (4xx)
          if (response.status >= 400 && response.status < 500) {
            throw new Error(errorMessage)
          }

          // Retry for server errors (5xx)
          lastError = new Error(errorMessage)
          console.warn(`Profile creation attempt ${attempt} failed:`, errorMessage)

          if (attempt < maxRetries) {
            // Wait before retry with exponential backoff
            const delay = Math.min(1000 * Math.pow(2, attempt - 1), 5000)
            console.log(`Retrying in ${delay}ms...`)
            await new Promise(resolve => setTimeout(resolve, delay))
            continue
          }
        } else {
          const result = await response.json()
          console.log('✅ Basic user profile created successfully:', result.message)
          return // Success, exit the retry loop
        }
      } catch (error) {
        lastError = error instanceof Error ? error : new Error('Unknown error')
        console.error(`Profile creation attempt ${attempt} failed:`, lastError.message)

        // Don't retry for network errors that are likely permanent
        if (lastError.message.includes('fetch') && attempt === 1) {
          break
        }

        if (attempt < maxRetries) {
          // Wait before retry
          const delay = Math.min(1000 * attempt, 3000)
          await new Promise(resolve => setTimeout(resolve, delay))
        }
      }
    }

    // If we get here, all attempts failed
    console.error('All profile creation attempts failed')
    throw lastError || new Error('Failed to create user profile after multiple attempts')
  }

  /**
   * Schedule referral placement for later processing (non-blocking)
   */
  private static async scheduleReferralPlacement(userId: string, referrerId: string) {
    try {
      // Use a timeout to defer this operation
      setTimeout(async () => {
        try {
          await ReferralSystemService.placeUserInHierarchy(userId, referrerId)
          console.log('Referral placement completed for user:', userId)
        } catch (error) {
          console.error('Deferred referral placement failed:', error)
        }
      }, 2000) // 2 second delay
    } catch (error) {
      console.error('Failed to schedule referral placement:', error)
    }
  }

  /**
   * Create user profile in public.users table (legacy method with full operations)
   */
  private static async createUserProfile(authUser: any, userData?: Partial<User> & { referralCode?: string }, referrer?: User | null) {
    try {
      // Use server-side API route for profile creation with referrer data
      const response = await fetch('/api/auth/create-profile', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId: authUser.id,
          email: authUser.email,
          userData: {
            full_name: userData?.full_name,
            phone: userData?.phone,
            location: userData?.location,
            referral_level: referrer ? (referrer.referral_level || 0) + 1 : 0,
            referred_by_id: referrer?.id || null
          }
        })
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to create user profile')
      }

      // If user has a referrer, place them in hierarchy
      if (referrer) {
        try {
          await ReferralSystemService.placeUserInHierarchy(authUser.id, referrer.id)
        } catch (referralError) {
          console.error('Failed to place user in referral hierarchy:', referralError)
          // Don't fail the profile creation, but log the error
        }
      }
    } catch (error) {
      console.error('Error creating user profile via API:', error)
      throw error
    }
  }

  /**
   * Verify email OTP and process referral placement
   */
  static async verifyEmailOtp(email: string, token: string) {
    const { data, error } = await supabase.auth.verifyOtp({
      email,
      token,
      type: 'signup'
    })

    if (error) {
      throw new Error(error.message)
    }

    // After successful verification, create user profile if it doesn't exist
    if (data.user) {
      try {
        // Check if user profile already exists
        const { data: existingUser } = await supabase
          .from('users')
          .select('id')
          .eq('id', data.user.id)
          .single()

        if (!existingUser) {
          // Get referrer if referral code exists in metadata
          let referrer: User | null = null
          const referralCode = data.user.user_metadata?.referral_code
          if (referralCode) {
            console.log('🔍 Found referral code in metadata during OTP verification:', referralCode)
            try {
              referrer = await ReferralSystemService.validateReferralCode(referralCode)
              console.log('✅ Referrer validated during OTP verification:', referrer?.email)
            } catch (error) {
              console.warn('❌ Failed to validate referral code during OTP verification:', error)
            }
          }

          // Create user profile with referral information
          if (referrer) {
            await this.createUserProfileWithReferrer(data.user, {
              full_name: data.user.user_metadata?.full_name,
              phone: data.user.user_metadata?.phone,
              location: data.user.user_metadata?.location,
              referral_code: referralCode
            }, referrer)
          } else {
            await this.createBasicUserProfile(data.user, {
              full_name: data.user.user_metadata?.full_name,
              phone: data.user.user_metadata?.phone,
              location: data.user.user_metadata?.location,
              referral_code: referralCode
            })
          }
        }
      } catch (profileError) {
        console.error('Error creating user profile after verification:', profileError)
        // Don't fail the verification, but log the error
      }
    }

    return data
  }

  /**
   * Resend email OTP
   */
  static async resendEmailOtp(email: string) {
    const { data, error } = await supabase.auth.resend({
      type: 'signup',
      email
    })

    if (error) {
      throw new Error(error.message)
    }

    return data
  }

  /**
   * Sign in with email and password
   */
  static async signIn(email: string, password: string) {
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password
    })

    if (error) {
      throw new Error(error.message)
    }

    return data
  }

  /**
   * Sign out the current user
   */
  static async signOut() {
    const { error } = await supabase.auth.signOut()
    
    if (error) {
      throw new Error(error.message)
    }
  }

  /**
   * Get the current user session
   */
  static async getSession() {
    const { data: { session }, error } = await supabase.auth.getSession()
    
    if (error) {
      throw new Error(error.message)
    }

    return session
  }

  /**
   * Get the current user with improved error handling
   */
  static async getCurrentUser(): Promise<AuthUser | null> {
    try {
      // First check if we have a valid session
      const { data: { session }, error: sessionError } = await supabase.auth.getSession()

      if (sessionError) {
        console.error('Session error:', sessionError)
        return null
      }

      if (!session?.user) {
        console.log('No active session found')
        return null
      }

      const user = session.user
      console.log('Found active session for user:', user.email)

      // Get additional user data from users table with retry logic
      let retryCount = 0
      const maxRetries = 3

      while (retryCount < maxRetries) {
        try {
          const { data: profile, error: profileError } = await supabase
            .from('users')
            .select('*')
            .eq('id', user.id)
            .single()

          if (profileError) {
            if (profileError.code === 'PGRST116') {
              // User profile doesn't exist, create it
              console.log('User profile not found, creating...')
              const newProfile = {
                id: user.id,
                email: user.email!,
                full_name: user.user_metadata?.full_name || null,
                phone: user.user_metadata?.phone || null,
                location: user.user_metadata?.location || null,
                role: 'user',
                is_super_admin: false
              }

              const { data: createdProfile, error: createError } = await supabase
                .from('users')
                .insert(newProfile)
                .select()
                .single()

              if (createError) {
                console.error('Error creating user profile:', createError)
                return {
                  id: user.id,
                  email: user.email!
                }
              }

              return createdProfile
            } else {
              throw profileError
            }
          }

          return profile
        } catch (error) {
          retryCount++
          console.error(`Error fetching user profile (attempt ${retryCount}):`, error)

          if (retryCount >= maxRetries) {
            console.error('Max retries reached, returning basic user info')
            return {
              id: user.id,
              email: user.email!
            }
          }

          // Wait before retry
          await new Promise(resolve => setTimeout(resolve, 1000 * retryCount))
        }
      }

      return null
    } catch (error) {
      console.error('Error in getCurrentUser:', error)
      return null
    }
  }

  /**
   * Update user profile
   */
  static async updateProfile(userId: string, updates: Partial<User>) {
    const { data, error } = await supabase
      .from('users')
      .update(updates)
      .eq('id', userId)
      .select()
      .single()

    if (error) {
      throw new Error(error.message)
    }

    return data
  }

  /**
   * Reset password
   */
  static async resetPassword(email: string) {
    const { error } = await supabase.auth.resetPasswordForEmail(email, {
      redirectTo: `${window.location.origin}/auth/reset-password`
    })

    if (error) {
      throw new Error(error.message)
    }
  }

  /**
   * Update password
   */
  static async updatePassword(newPassword: string) {
    const { error } = await supabase.auth.updateUser({
      password: newPassword
    })

    if (error) {
      throw new Error(error.message)
    }
  }

  /**
   * Listen to auth state changes with improved handling
   */
  static onAuthStateChange(callback: (event: string, session: any) => void) {
    return supabase.auth.onAuthStateChange(async (event, session) => {
      console.log('Auth state change detected:', event, session?.user?.email)

      // Add a small delay to ensure state consistency
      setTimeout(() => {
        callback(event, session)
      }, 100)
    })
  }

  /**
   * Check if current session is valid
   */
  static async isSessionValid(): Promise<boolean> {
    try {
      const { data: { session }, error } = await supabase.auth.getSession()

      if (error || !session) {
        return false
      }

      // Check if token is expired
      const now = Math.floor(Date.now() / 1000)
      if (session.expires_at && session.expires_at < now) {
        console.log('Session token expired')
        return false
      }

      return true
    } catch (error) {
      console.error('Error checking session validity:', error)
      return false
    }
  }

  /**
   * Refresh session if needed
   */
  static async refreshSession(): Promise<boolean> {
    try {
      const { data, error } = await supabase.auth.refreshSession()

      if (error) {
        console.error('Error refreshing session:', error)
        return false
      }

      return !!data.session
    } catch (error) {
      console.error('Error in refreshSession:', error)
      return false
    }
  }
}
