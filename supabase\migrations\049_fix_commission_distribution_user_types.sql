-- Fix commission distribution to handle user type-specific level limits
-- <PERSON><PERSON> and RS<PERSON> should get commission from entire downline (unlimited levels)
-- Normal users should only get commission up to 10 levels

-- Helper function to get the maximum referral depth for a user
CREATE OR REPLACE FUNCTION get_max_referral_depth(user_id UUID)
RETURNS INTEGER AS $$
DECLARE
    max_depth INTEGER := 0;
    current_depth INTEGER;
BEGIN
    -- Get the maximum depth of referrals under this user
    WITH RECURSIVE referral_depth AS (
        -- Base case: direct referrals (level 1)
        SELECT rh.user_id, 1 as depth
        FROM referral_hierarchy rh
        WHERE rh.ancestor_id = user_id AND rh.level_difference = 1
        
        UNION ALL
        
        -- Recursive case: deeper levels
        SELECT rh.user_id, rd.depth + 1
        FROM referral_hierarchy rh
        JOIN referral_depth rd ON rh.ancestor_id = rd.user_id
        WHERE rh.level_difference = 1
    )
    SELECT COALESCE(MAX(depth), 0) INTO max_depth
    FROM referral_depth;
    
    RETURN max_depth;
END;
$$ LANGUAGE plpgsql;

-- Helper function to determine commission level limit based on user type
CREATE OR REPLACE FUNCTION get_commission_level_limit(beneficiary_user_type TEXT)
RETURNS INTEGER AS $$
BEGIN
    CASE beneficiary_user_type
        WHEN 'zonal_manager' THEN
            RETURN 999; -- Effectively unlimited
        WHEN 'rsm' THEN
            RETURN 999; -- Effectively unlimited
        WHEN 'okdoi_head' THEN
            RETURN 999; -- Effectively unlimited
        ELSE
            RETURN 10; -- Normal users limited to 10 levels
    END CASE;
END;
$$ LANGUAGE plpgsql;

-- Updated commission distribution function with user type-specific level handling
CREATE OR REPLACE FUNCTION calculate_commission_distribution(
    purchaser_id UUID,
    package_id UUID,
    package_amount DECIMAL(12,2)
) RETURNS VOID AS $$
DECLARE
    commission_record RECORD;
    beneficiary_record RECORD;
    commission_amount DECIMAL(12,2);
    transaction_id_val VARCHAR(50);
    okdoi_head_id UUID;
    total_distributed DECIMAL(12,2) := 0;
    remaining_commission DECIMAL(12,2);
    max_levels INTEGER;
    beneficiary_level_limit INTEGER;
    current_level INTEGER;
BEGIN
    -- Generate unique transaction ID
    transaction_id_val := 'COM-' || TO_CHAR(NOW(), 'YYYYMMDD') || '-' || UPPER(SUBSTRING(MD5(RANDOM()::TEXT) FROM 1 FOR 8));

    -- Get OKDOI Head user ID
    SELECT id INTO okdoi_head_id FROM users WHERE user_type = 'okdoi_head' LIMIT 1;

    -- Get the maximum depth of the referral hierarchy from this purchaser
    SELECT get_max_referral_depth(purchaser_id) INTO max_levels;
    
    -- If no upline exists, set max_levels to at least 10 to check for potential beneficiaries
    IF max_levels = 0 THEN
        max_levels := 10;
    END IF;

    -- Get commission structure for this package value
    FOR commission_record IN
        SELECT * FROM commission_structure
        WHERE package_value <= package_amount
        ORDER BY package_value DESC
        LIMIT 1
    LOOP
        -- Distribute commissions up to the maximum available levels
        -- But respect individual user type limits
        FOR i IN 1..GREATEST(max_levels, 50) LOOP -- Check up to 50 levels maximum to prevent infinite loops
            -- Get beneficiary at this level
            SELECT u.* INTO beneficiary_record
            FROM users u
            JOIN referral_hierarchy rh ON rh.ancestor_id = u.id
            WHERE rh.user_id = purchaser_id AND rh.level_difference = i
            LIMIT 1;

            -- If we found a beneficiary, check their level limit
            IF beneficiary_record.id IS NOT NULL THEN
                -- Get the level limit for this beneficiary based on their user type
                SELECT get_commission_level_limit(beneficiary_record.user_type) INTO beneficiary_level_limit;
                
                -- Only process commission if this level is within the beneficiary's limit
                IF i <= beneficiary_level_limit THEN
                    -- Calculate commission amount for this level
                    commission_amount := CASE i
                        WHEN 1 THEN package_amount * commission_record.level_1_rate
                        WHEN 2 THEN package_amount * commission_record.level_2_rate
                        WHEN 3 THEN package_amount * commission_record.level_3_rate
                        WHEN 4 THEN package_amount * commission_record.level_4_rate
                        WHEN 5 THEN package_amount * commission_record.level_5_rate
                        WHEN 6 THEN package_amount * commission_record.level_6_rate
                        WHEN 7 THEN package_amount * commission_record.level_7_rate
                        WHEN 8 THEN package_amount * commission_record.level_8_rate
                        WHEN 9 THEN package_amount * commission_record.level_9_rate
                        WHEN 10 THEN package_amount * commission_record.level_10_rate
                        ELSE package_amount * commission_record.level_10_rate -- Use level 10 rate for levels beyond 10
                    END;

                    IF commission_amount > 0 THEN
                        -- Create commission transaction for actual beneficiary
                        INSERT INTO commission_transactions (
                            transaction_id, user_id, beneficiary_id, subscription_purchase_id,
                            commission_type, commission_level, package_value, commission_rate,
                            commission_amount, status, metadata
                        ) VALUES (
                            transaction_id_val || '-L' || i, purchaser_id, beneficiary_record.id, package_id,
                            commission_record.commission_type, i, package_amount,
                            CASE i
                                WHEN 1 THEN commission_record.level_1_rate
                                WHEN 2 THEN commission_record.level_2_rate
                                WHEN 3 THEN commission_record.level_3_rate
                                WHEN 4 THEN commission_record.level_4_rate
                                WHEN 5 THEN commission_record.level_5_rate
                                WHEN 6 THEN commission_record.level_6_rate
                                WHEN 7 THEN commission_record.level_7_rate
                                WHEN 8 THEN commission_record.level_8_rate
                                WHEN 9 THEN commission_record.level_9_rate
                                WHEN 10 THEN commission_record.level_10_rate
                                ELSE commission_record.level_10_rate
                            END,
                            commission_amount, 'pending',
                            '{"beneficiary_type": "' || beneficiary_record.user_type || '", "level_limit": ' || beneficiary_level_limit || '}'
                        );

                        total_distributed := total_distributed + commission_amount;
                    END IF;
                ELSE
                    -- Beneficiary exists but level exceeds their limit
                    -- This commission goes to OKDOI Head as unallocated
                    commission_amount := CASE i
                        WHEN 1 THEN package_amount * commission_record.level_1_rate
                        WHEN 2 THEN package_amount * commission_record.level_2_rate
                        WHEN 3 THEN package_amount * commission_record.level_3_rate
                        WHEN 4 THEN package_amount * commission_record.level_4_rate
                        WHEN 5 THEN package_amount * commission_record.level_5_rate
                        WHEN 6 THEN package_amount * commission_record.level_6_rate
                        WHEN 7 THEN package_amount * commission_record.level_7_rate
                        WHEN 8 THEN package_amount * commission_record.level_8_rate
                        WHEN 9 THEN package_amount * commission_record.level_9_rate
                        WHEN 10 THEN package_amount * commission_record.level_10_rate
                        ELSE package_amount * commission_record.level_10_rate
                    END;

                    IF commission_amount > 0 AND okdoi_head_id IS NOT NULL THEN
                        INSERT INTO commission_transactions (
                            transaction_id, user_id, beneficiary_id, subscription_purchase_id,
                            commission_type, commission_level, package_value, commission_rate,
                            commission_amount, status, metadata
                        ) VALUES (
                            transaction_id_val || '-L' || i || '-LIMIT', purchaser_id, okdoi_head_id, package_id,
                            commission_record.commission_type || '_level_limit_exceeded', i, package_amount,
                            CASE i
                                WHEN 1 THEN commission_record.level_1_rate
                                WHEN 2 THEN commission_record.level_2_rate
                                WHEN 3 THEN commission_record.level_3_rate
                                WHEN 4 THEN commission_record.level_4_rate
                                WHEN 5 THEN commission_record.level_5_rate
                                WHEN 6 THEN commission_record.level_6_rate
                                WHEN 7 THEN commission_record.level_7_rate
                                WHEN 8 THEN commission_record.level_8_rate
                                WHEN 9 THEN commission_record.level_9_rate
                                WHEN 10 THEN commission_record.level_10_rate
                                ELSE commission_record.level_10_rate
                            END,
                            commission_amount, 'pending',
                            '{"reason": "beneficiary_level_limit_exceeded", "original_level": ' || i || ', "beneficiary_type": "' || beneficiary_record.user_type || '", "level_limit": ' || beneficiary_level_limit || '}'
                        );

                        total_distributed := total_distributed + commission_amount;
                    END IF;
                END IF;
            ELSE
                -- No beneficiary at this level, commission goes to OKDOI Head as unallocated
                commission_amount := CASE i
                    WHEN 1 THEN package_amount * commission_record.level_1_rate
                    WHEN 2 THEN package_amount * commission_record.level_2_rate
                    WHEN 3 THEN package_amount * commission_record.level_3_rate
                    WHEN 4 THEN package_amount * commission_record.level_4_rate
                    WHEN 5 THEN package_amount * commission_record.level_5_rate
                    WHEN 6 THEN package_amount * commission_record.level_6_rate
                    WHEN 7 THEN package_amount * commission_record.level_7_rate
                    WHEN 8 THEN package_amount * commission_record.level_8_rate
                    WHEN 9 THEN package_amount * commission_record.level_9_rate
                    WHEN 10 THEN package_amount * commission_record.level_10_rate
                    ELSE package_amount * commission_record.level_10_rate
                END;

                IF commission_amount > 0 AND okdoi_head_id IS NOT NULL THEN
                    INSERT INTO commission_transactions (
                        transaction_id, user_id, beneficiary_id, subscription_purchase_id,
                        commission_type, commission_level, package_value, commission_rate,
                        commission_amount, status, metadata
                    ) VALUES (
                        transaction_id_val || '-L' || i || '-HEAD', purchaser_id, okdoi_head_id, package_id,
                        commission_record.commission_type || '_unallocated', i, package_amount,
                        CASE i
                            WHEN 1 THEN commission_record.level_1_rate
                            WHEN 2 THEN commission_record.level_2_rate
                            WHEN 3 THEN commission_record.level_3_rate
                            WHEN 4 THEN commission_record.level_4_rate
                            WHEN 5 THEN commission_record.level_5_rate
                            WHEN 6 THEN commission_record.level_6_rate
                            WHEN 7 THEN commission_record.level_7_rate
                            WHEN 8 THEN commission_record.level_8_rate
                            WHEN 9 THEN commission_record.level_9_rate
                            WHEN 10 THEN commission_record.level_10_rate
                            ELSE commission_record.level_10_rate
                        END,
                        commission_amount, 'pending',
                        '{"reason": "unallocated_level", "original_level": ' || i || '}'
                    );

                    total_distributed := total_distributed + commission_amount;
                END IF;
                
                -- If we've gone beyond reasonable depth and found no beneficiaries, break
                IF i > 20 THEN
                    EXIT;
                END IF;
            END IF;
        END LOOP;
    END LOOP;
END;
$$ LANGUAGE plpgsql;
