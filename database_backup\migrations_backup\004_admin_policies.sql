-- Update RLS policies to support admin functionality

-- Drop existing admin policies
DROP POLICY IF EXISTS "Only admins can insert categories" ON categories;
DROP POLICY IF EXISTS "Only admins can update categories" ON categories;
DROP POLICY IF EXISTS "Only admins can insert subcategories" ON subcategories;
DROP POLICY IF EXISTS "Only admins can update subcategories" ON subcategories;

-- Create function to check if user is admin
CREATE OR REPLACE FUNCTION is_admin(user_id uuid)
RETURNS boolean AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM users 
    WHERE id = user_id 
    AND (role = 'admin' OR is_super_admin = true)
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Categories policies with admin support
CREATE POLICY "Only admins can insert categories" ON categories
    FOR INSERT WITH CHECK (is_admin(auth.uid()));

CREATE POLICY "Only admins can update categories" ON categories
    FOR UPDATE USING (is_admin(auth.uid()));

CREATE POLICY "Only admins can delete categories" ON categories
    FOR DELETE USING (is_admin(auth.uid()));

-- Subcategories policies with admin support
CREATE POLICY "Only admins can insert subcategories" ON subcategories
    FOR INSERT WITH CHECK (is_admin(auth.uid()));

CREATE POLICY "Only admins can update subcategories" ON subcategories
    FOR UPDATE USING (is_admin(auth.uid()));

CREATE POLICY "Only admins can delete subcategories" ON subcategories
    FOR DELETE USING (is_admin(auth.uid()));

-- Admin can view all users
CREATE POLICY "Admins can view all users" ON users
    FOR SELECT USING (is_admin(auth.uid()));

-- Admin can update user roles and ban status
CREATE POLICY "Admins can update users" ON users
    FOR UPDATE USING (is_admin(auth.uid()));

-- Admin can view all ads regardless of status
CREATE POLICY "Admins can view all ads" ON ads
    FOR SELECT USING (is_admin(auth.uid()));

-- Admin can update ad status
CREATE POLICY "Admins can update ads" ON ads
    FOR UPDATE USING (is_admin(auth.uid()));

-- Admin can delete ads
CREATE POLICY "Admins can delete ads" ON ads
    FOR DELETE USING (is_admin(auth.uid()));

-- Admin can view all ad images
CREATE POLICY "Admins can view all ad images" ON ad_images
    FOR SELECT USING (is_admin(auth.uid()));

-- Create a default admin user (update with your email)
-- This will be commented out - uncomment and update with your email to create an admin
-- INSERT INTO auth.users (id, email, encrypted_password, email_confirmed_at, created_at, updated_at)
-- VALUES (
--   gen_random_uuid(),
--   '<EMAIL>',
--   crypt('admin123', gen_salt('bf')),
--   now(),
--   now(),
--   now()
-- );

-- Update users table to set admin role for the admin user
-- UPDATE users SET role = 'admin', is_super_admin = true 
-- WHERE email = '<EMAIL>';
