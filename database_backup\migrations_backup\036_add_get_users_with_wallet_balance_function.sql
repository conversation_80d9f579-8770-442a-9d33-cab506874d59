-- Add get_users_with_wallet_balance function for admin user management
-- This function returns users with their wallet balance for the admin panel

CREATE OR REPLACE FUNCTION get_users_with_wallet_balance(
    page_offset INTEGER DEFAULT 0,
    page_limit INTEGER DEFAULT 20
)
RETURNS TABLE (
    id UUID,
    email VARCHAR(255),
    full_name VARCHA<PERSON>(100),
    phone VARCHAR(20),
    location VARCHAR(100),
    avatar_url TEXT,
    is_verified BOOLEAN,
    role VARCHAR(20),
    is_super_admin BOOLEAN,
    banned_until TIMESTAMP WITH TIME ZONE,
    user_type VARCHAR(20),
    referral_code VARCHAR(20),
    referred_by_id UUID,
    referral_level INTEGER,
    referral_path TEXT,
    direct_referrals_count INTEGER,
    total_downline_count INTEGER,
    total_commission_earned DECIMAL(12,2),
    is_referral_active BOOLEAN,
    wallet_balance DECIMAL(12,2),
    created_at TIMESTAMP WITH TIME ZONE,
    updated_at TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        u.id,
        u.email,
        u.full_name,
        u.phone,
        u.location,
        u.avatar_url,
        u.is_verified,
        u.role,
        u.is_super_admin,
        u.banned_until,
        u.user_type,
        u.referral_code,
        u.referred_by_id,
        u.referral_level,
        u.referral_path,
        u.direct_referrals_count,
        u.total_downline_count,
        u.total_commission_earned,
        u.is_referral_active,
        COALESCE(w.balance, 0.00) as wallet_balance,
        u.created_at,
        u.updated_at
    FROM users u
    LEFT JOIN user_wallets w ON u.id = w.user_id
    ORDER BY u.created_at DESC
    LIMIT page_limit
    OFFSET page_offset;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission to authenticated users (admin check will be done in application layer)
GRANT EXECUTE ON FUNCTION get_users_with_wallet_balance(INTEGER, INTEGER) TO authenticated;

-- Add comment for documentation
COMMENT ON FUNCTION get_users_with_wallet_balance(INTEGER, INTEGER) IS 
'Returns users with their wallet balance for admin management. Includes pagination support.';
