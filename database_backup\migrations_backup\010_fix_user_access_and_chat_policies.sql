-- Fix user access and chat system policies
-- This migration fixes the "Anonymous" user display issue and chat message sending errors

-- Create is_admin function (if not exists)
CREATE OR REPLACE FUNCTION is_admin(user_id uuid DEFAULT auth.uid())
RETURNS boolean AS
$$
  SELECT COALESCE(
    (SELECT role = 'admin' OR is_super_admin = true
     FROM users
     WHERE id = user_id),
    false
  )
$$ STABLE LANGUAGE sql SECURITY DEFINER;

-- Drop the restrictive SELECT policy that prevents users from seeing other users' names
DROP POLICY IF EXISTS "Users can view own profile or admins can view all" ON users;

-- Create new RLS policies for users table
-- Allow public read access to basic user info (needed for ads to show user names)
CREATE POLICY "Public can view basic user info" ON users
  FOR SELECT
  USING (true);

-- Users can update their own profile
CREATE POLICY "Users can update own profile" ON users
  FOR UPDATE
  USING (auth.uid() = id);

-- Users can insert their own profile
CREATE POLICY "Users can insert own profile" ON users
  FOR INSERT
  WITH CHECK (auth.uid() = id);

-- <PERSON><PERSON> can do everything
CREATE POLICY "Admins can manage all users" ON users
  FOR ALL
  USING (is_admin())
  WITH CHECK (is_admin());

-- Add missing admin policies for chat system (if not exists)
DO $$
BEGIN
    -- Check and create admin policies for chat_conversations
    IF NOT EXISTS (
        SELECT 1 FROM pg_policies 
        WHERE schemaname = 'public' 
        AND tablename = 'chat_conversations' 
        AND policyname = 'Admins can view all conversations'
    ) THEN
        CREATE POLICY "Admins can view all conversations" ON chat_conversations
            FOR SELECT USING (is_admin(auth.uid()));
    END IF;

    IF NOT EXISTS (
        SELECT 1 FROM pg_policies 
        WHERE schemaname = 'public' 
        AND tablename = 'chat_conversations' 
        AND policyname = 'Admins can update conversations'
    ) THEN
        CREATE POLICY "Admins can update conversations" ON chat_conversations
            FOR UPDATE USING (is_admin(auth.uid()));
    END IF;

    -- Check and create admin policies for chat_messages
    IF NOT EXISTS (
        SELECT 1 FROM pg_policies 
        WHERE schemaname = 'public' 
        AND tablename = 'chat_messages' 
        AND policyname = 'Admins can view all messages'
    ) THEN
        CREATE POLICY "Admins can view all messages" ON chat_messages
            FOR SELECT USING (is_admin(auth.uid()));
    END IF;
END $$;
