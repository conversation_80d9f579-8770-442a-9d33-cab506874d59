-- Optimize signup performance by fixing slow triggers and operations
-- This migration addresses 504 timeout errors during user registration

-- =====================================================
-- 1. DISABLE PROBLEMATIC TRIGGERS TEMPORARILY
-- =====================================================

-- Disable the referral code generation trigger that might be slow
DROP TRIGGER IF EXISTS trigger_auto_generate_referral_code ON users;
DROP TRIGGER IF EXISTS assign_referral_code_trigger ON users;

-- Disable wallet creation trigger temporarily (we'll create wallets on-demand)
DROP TRIGGER IF EXISTS trigger_create_user_wallet ON users;

-- =====================================================
-- 2. CREATE OPTIMIZED REFERRAL CODE GENERATION
-- =====================================================

-- Create a simpler, faster referral code generation function
CREATE OR REPLACE FUNCTION generate_simple_referral_code()
RETURNS TEXT AS $$
DECLARE
    code TEXT;
    counter INTEGER := 0;
BEGIN
    LOOP
        -- Generate a simple 8-character code using timestamp and random
        code := UPPER(SUBSTRING(MD5(EXTRACT(EPOCH FROM NOW())::TEXT || RANDOM()::TEXT), 1, 8));
        
        -- Check if code exists (with timeout protection)
        IF NOT EXISTS (SELECT 1 FROM users WHERE referral_code = code) THEN
            RETURN code;
        END IF;
        
        counter := counter + 1;
        -- Prevent infinite loops
        IF counter > 10 THEN
            -- Fallback to timestamp-based code
            RETURN UPPER(SUBSTRING(MD5(EXTRACT(EPOCH FROM NOW())::TEXT), 1, 8));
        END IF;
    END LOOP;
END;
$$ LANGUAGE plpgsql;

-- Create optimized trigger for referral code generation
CREATE OR REPLACE FUNCTION auto_generate_simple_referral_code()
RETURNS TRIGGER AS $$
BEGIN
    -- Only generate if not provided and avoid complex operations
    IF NEW.referral_code IS NULL OR NEW.referral_code = '' THEN
        NEW.referral_code := generate_simple_referral_code();
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create the optimized trigger
CREATE TRIGGER trigger_auto_generate_simple_referral_code
    BEFORE INSERT ON users
    FOR EACH ROW
    EXECUTE FUNCTION auto_generate_simple_referral_code();

-- =====================================================
-- 3. CREATE ON-DEMAND WALLET CREATION FUNCTION
-- =====================================================

-- Function to create wallet on-demand instead of via trigger
CREATE OR REPLACE FUNCTION ensure_user_wallet(p_user_id UUID)
RETURNS UUID AS $$
DECLARE
    wallet_id UUID;
BEGIN
    -- Check if wallet already exists
    SELECT id INTO wallet_id 
    FROM user_wallets 
    WHERE user_id = p_user_id;
    
    -- Create wallet if it doesn't exist
    IF wallet_id IS NULL THEN
        INSERT INTO user_wallets (user_id, balance, currency)
        VALUES (p_user_id, 0.00, 'LKR')
        RETURNING id INTO wallet_id;
    END IF;
    
    RETURN wallet_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- 4. OPTIMIZE RLS POLICIES FOR FASTER USER CREATION
-- =====================================================

-- Drop complex RLS policies that might cause delays
DROP POLICY IF EXISTS "Users can view own profile or admins can view all" ON users;
DROP POLICY IF EXISTS "Public can view basic user info" ON users;

-- Create simpler, faster RLS policies
CREATE POLICY "Users can view basic user info" ON users
    FOR SELECT USING (true);

CREATE POLICY "Users can insert own profile" ON users
    FOR INSERT WITH CHECK (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON users
    FOR UPDATE USING (auth.uid() = id OR is_admin());

-- =====================================================
-- 5. CREATE BACKGROUND JOB FUNCTION FOR DEFERRED OPERATIONS
-- =====================================================

-- Function to process deferred referral placements
CREATE OR REPLACE FUNCTION process_deferred_referral_placements()
RETURNS void AS $$
DECLARE
    user_record RECORD;
BEGIN
    -- Find users who need referral placement (have referrer but no placement)
    FOR user_record IN 
        SELECT u.id, u.referred_by_id
        FROM users u
        WHERE u.referred_by_id IS NOT NULL
        AND NOT EXISTS (
            SELECT 1 FROM referral_placements rp 
            WHERE rp.user_id = u.id
        )
        LIMIT 10 -- Process in batches
    LOOP
        BEGIN
            -- Place user in hierarchy
            PERFORM place_user_in_hierarchy(user_record.id, user_record.referred_by_id);
        EXCEPTION WHEN OTHERS THEN
            -- Log error but continue processing
            RAISE NOTICE 'Failed to place user % in hierarchy: %', user_record.id, SQLERRM;
        END;
    END LOOP;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- 6. CREATE INDEXES FOR BETTER PERFORMANCE
-- =====================================================

-- Add indexes to improve query performance during signup
CREATE INDEX IF NOT EXISTS idx_users_referral_code_fast ON users(referral_code) WHERE referral_code IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_users_referred_by_id ON users(referred_by_id) WHERE referred_by_id IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_user_wallets_user_id ON user_wallets(user_id);

-- =====================================================
-- 7. ENSURE EXISTING USERS HAVE REFERRAL CODES
-- =====================================================

-- Update users without referral codes (in batches to avoid locks)
DO $$
DECLARE
    batch_size INTEGER := 100;
    updated_count INTEGER;
BEGIN
    LOOP
        UPDATE users 
        SET referral_code = generate_simple_referral_code()
        WHERE id IN (
            SELECT id FROM users 
            WHERE referral_code IS NULL 
            LIMIT batch_size
        );
        
        GET DIAGNOSTICS updated_count = ROW_COUNT;
        
        -- Exit when no more rows to update
        IF updated_count = 0 THEN
            EXIT;
        END IF;
        
        -- Small delay between batches
        PERFORM pg_sleep(0.1);
    END LOOP;
    
    RAISE NOTICE 'Referral code update completed';
END $$;

-- =====================================================
-- 8. CREATE WALLETS FOR EXISTING USERS (ON-DEMAND)
-- =====================================================

-- Don't create all wallets at once, they'll be created on-demand
-- This prevents migration timeouts

COMMENT ON FUNCTION ensure_user_wallet(UUID) IS 'Creates user wallet on-demand to avoid trigger delays during signup';
COMMENT ON FUNCTION process_deferred_referral_placements() IS 'Background job to process referral placements that were deferred during signup';
COMMENT ON FUNCTION generate_simple_referral_code() IS 'Optimized referral code generation with timeout protection';
