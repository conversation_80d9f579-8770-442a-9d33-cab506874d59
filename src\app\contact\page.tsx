'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import { Mail, Phone, MapPin, Clock, Send, MessageCircle, HelpCircle, Shield } from 'lucide-react'
import Header from '@/components/layout/Header'
import Footer from '@/components/layout/Footer'
import { PremiumCard, PremiumButton, PremiumInput, FadeIn, SlideInUp, StaggerContainer, StaggerItem } from '@/components/ui/premium'
import { showAlert } from '@/components/ui/ConfirmationDialog'

export default function ContactPage() {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    subject: '',
    message: ''
  })
  const [isSubmitting, setIsSubmitting] = useState(false)

  const contactInfo = [
    {
      icon: Mail,
      title: 'Email Us',
      details: '<EMAIL>',
      description: 'Send us an email and we\'ll respond within 24 hours'
    },
    {
      icon: Phone,
      title: 'Call Us',
      details: '+94 11 123 4567',
      description: 'Available Monday to Friday, 9 AM to 6 PM'
    },
    {
      icon: MapPin,
      title: 'Visit Us',
      details: 'Colombo, Sri Lanka',
      description: 'Our headquarters in the heart of Colombo'
    },
    {
      icon: Clock,
      title: 'Business Hours',
      details: 'Mon - Fri: 9 AM - 6 PM',
      description: 'Saturday: 9 AM - 2 PM, Sunday: Closed'
    }
  ]

  const supportOptions = [
    {
      icon: MessageCircle,
      title: 'Live Chat',
      description: 'Get instant help from our support team',
      action: 'Start Chat',
      href: '/dashboard/chats'
    },
    {
      icon: HelpCircle,
      title: 'Help Center',
      description: 'Find answers to frequently asked questions',
      action: 'Browse FAQ',
      href: '/help'
    },
    {
      icon: Shield,
      title: 'Report Issue',
      description: 'Report security concerns or technical issues',
      action: 'Report Now',
      href: '/report'
    }
  ]

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({ ...prev, [name]: value }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)
    
    // Simulate form submission
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    // Reset form
    setFormData({ name: '', email: '', subject: '', message: '' })
    setIsSubmitting(false)

    // Show success message
    await showAlert({
      title: 'Message Sent',
      message: 'Thank you for your message! We\'ll get back to you soon.',
      variant: 'success'
    })
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <main className="py-8 pt-28">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Hero Section */}
          <FadeIn>
            <div className="text-center mb-16">
              <motion.h1 
                className="text-4xl lg:text-6xl font-bold text-gray-900 mb-6"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6 }}
              >
                Contact <span className="text-primary-blue">OKDOI</span>
              </motion.h1>
              <motion.p 
                className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.2 }}
              >
                We're here to help! Get in touch with our team for support, 
                partnerships, or any questions about OKDOI.
              </motion.p>
            </div>
          </FadeIn>

          {/* Contact Info Cards */}
          <SlideInUp delay={0.4}>
            <StaggerContainer>
              <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6 mb-16">
                {contactInfo.map((info, index) => (
                  <StaggerItem key={info.title} index={index}>
                    <PremiumCard variant="glass" className="text-center h-full">
                      <motion.div
                        className="inline-flex items-center justify-center w-16 h-16 bg-primary-blue/10 text-primary-blue mb-4 mx-auto"
                        whileHover={{ scale: 1.1, rotate: 5 }}
                        transition={{ duration: 0.2 }}
                      >
                        <info.icon className="h-8 w-8" />
                      </motion.div>
                      <h3 className="text-lg font-bold text-gray-900 mb-2">{info.title}</h3>
                      <p className="text-primary-blue font-semibold mb-2">{info.details}</p>
                      <p className="text-gray-600 text-sm">{info.description}</p>
                    </PremiumCard>
                  </StaggerItem>
                ))}
              </div>
            </StaggerContainer>
          </SlideInUp>

          {/* Main Content */}
          <div className="grid lg:grid-cols-2 gap-12 mb-16">
            {/* Contact Form */}
            <FadeIn>
              <PremiumCard variant="premium">
                <h2 className="text-2xl font-bold text-gray-900 mb-6">Send us a Message</h2>
                <form onSubmit={handleSubmit} className="space-y-6">
                  <div className="grid md:grid-cols-2 gap-4">
                    <PremiumInput
                      label="Your Name"
                      name="name"
                      value={formData.name}
                      onChange={handleInputChange}
                      required
                      placeholder="Enter your full name"
                    />
                    <PremiumInput
                      label="Email Address"
                      name="email"
                      type="email"
                      value={formData.email}
                      onChange={handleInputChange}
                      required
                      placeholder="Enter your email"
                    />
                  </div>
                  
                  <PremiumInput
                    label="Subject"
                    name="subject"
                    value={formData.subject}
                    onChange={handleInputChange}
                    required
                    placeholder="What is this regarding?"
                  />
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Message
                    </label>
                    <motion.textarea
                      name="message"
                      value={formData.message}
                      onChange={handleInputChange}
                      required
                      rows={6}
                      placeholder="Tell us how we can help you..."
                      className="w-full px-4 py-3 border-2 border-gray-200 focus:border-primary-blue focus:ring-4 focus:ring-primary-blue/10 transition-all duration-300 resize-none"
                      whileFocus={{ scale: 1.02 }}
                      transition={{ duration: 0.2 }}
                    />
                  </div>
                  
                  <PremiumButton
                    type="submit"
                    variant="primary"
                    size="lg"
                    disabled={isSubmitting}
                    icon={<Send className="h-5 w-5" />}
                    className="w-full"
                  >
                    {isSubmitting ? 'Sending...' : 'Send Message'}
                  </PremiumButton>
                </form>
              </PremiumCard>
            </FadeIn>

            {/* Support Options */}
            <FadeIn delay={0.2}>
              <div className="space-y-6">
                <h2 className="text-2xl font-bold text-gray-900 mb-6">Other Ways to Get Help</h2>
                
                {supportOptions.map((option, index) => (
                  <motion.div
                    key={option.title}
                    initial={{ opacity: 0, x: 20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.1 * index }}
                  >
                    <PremiumCard variant="glass" className="p-6">
                      <div className="flex items-start space-x-4">
                        <div className="flex-shrink-0">
                          <div className="w-12 h-12 bg-primary-blue/10 text-primary-blue flex items-center justify-center">
                            <option.icon className="h-6 w-6" />
                          </div>
                        </div>
                        <div className="flex-1">
                          <h3 className="text-lg font-semibold text-gray-900 mb-2">{option.title}</h3>
                          <p className="text-gray-600 mb-4">{option.description}</p>
                          <PremiumButton
                            variant="outline"
                            size="sm"
                            onClick={() => window.location.href = option.href}
                          >
                            {option.action}
                          </PremiumButton>
                        </div>
                      </div>
                    </PremiumCard>
                  </motion.div>
                ))}
              </div>
            </FadeIn>
          </div>

          {/* FAQ Section */}
          <FadeIn>
            <PremiumCard variant="premium">
              <div className="text-center mb-8">
                <h2 className="text-2xl font-bold text-gray-900 mb-4">Frequently Asked Questions</h2>
                <p className="text-gray-600">Quick answers to common questions</p>
              </div>
              
              <div className="grid md:grid-cols-2 gap-8">
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-3">How do I post an ad?</h3>
                  <p className="text-gray-600 mb-6">
                    Simply click the "Post Ad" button, choose your category, fill in the details, 
                    and submit. Your ad will be live after admin approval.
                  </p>
                  
                  <h3 className="text-lg font-semibold text-gray-900 mb-3">Is OKDOI free to use?</h3>
                  <p className="text-gray-600">
                    Yes! Basic features are free. We offer premium features like ad boosting 
                    and shop creation for enhanced visibility and sales.
                  </p>
                </div>
                
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-3">How do I create a shop?</h3>
                  <p className="text-gray-600 mb-6">
                    Go to your dashboard, click "Create Shop", provide your business details, 
                    and start adding products. Shop approval takes 24-48 hours.
                  </p>
                  
                  <h3 className="text-lg font-semibold text-gray-900 mb-3">How does the referral system work?</h3>
                  <p className="text-gray-600">
                    Invite friends using your referral link. Earn commissions when they 
                    purchase subscriptions or boost their ads. Check your dashboard for details.
                  </p>
                </div>
              </div>
            </PremiumCard>
          </FadeIn>
        </div>
      </main>
      
      <Footer />
    </div>
  )
}
