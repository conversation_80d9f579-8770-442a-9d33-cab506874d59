"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/admin/unified-commission-structure/route";
exports.ids = ["app/api/admin/unified-commission-structure/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

module.exports = require("https");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("stream");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fadmin%2Funified-commission-structure%2Froute&page=%2Fapi%2Fadmin%2Funified-commission-structure%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Funified-commission-structure%2Froute.ts&appDir=C%3A%5CUsers%5Cdream%5CDesktop%5Cokdoi%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cdream%5CDesktop%5Cokdoi&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fadmin%2Funified-commission-structure%2Froute&page=%2Fapi%2Fadmin%2Funified-commission-structure%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Funified-commission-structure%2Froute.ts&appDir=C%3A%5CUsers%5Cdream%5CDesktop%5Cokdoi%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cdream%5CDesktop%5Cokdoi&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_dream_Desktop_okdoi_src_app_api_admin_unified_commission_structure_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/admin/unified-commission-structure/route.ts */ \"(rsc)/./src/app/api/admin/unified-commission-structure/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/admin/unified-commission-structure/route\",\n        pathname: \"/api/admin/unified-commission-structure\",\n        filename: \"route\",\n        bundlePath: \"app/api/admin/unified-commission-structure/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\app\\\\api\\\\admin\\\\unified-commission-structure\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_dream_Desktop_okdoi_src_app_api_admin_unified_commission_structure_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/admin/unified-commission-structure/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fadmin%2Funified-commission-structure%2Froute&page=%2Fapi%2Fadmin%2Funified-commission-structure%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Funified-commission-structure%2Froute.ts&appDir=C%3A%5CUsers%5Cdream%5CDesktop%5Cokdoi%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cdream%5CDesktop%5Cokdoi&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/admin/unified-commission-structure/route.ts":
/*!*****************************************************************!*\
  !*** ./src/app/api/admin/unified-commission-structure/route.ts ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/supabase */ \"(rsc)/./src/lib/supabase.ts\");\n\n\nasync function GET(request) {\n    try {\n        // Get unified commission structures (one per package)\n        const { data: structures, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabaseAdmin.from(\"commission_structure\").select(\"*\").eq(\"commission_type\", \"unified_structure\").order(\"package_value\", {\n            ascending: true\n        });\n        if (error) {\n            throw new Error(`Failed to fetch commission structures: ${error.message}`);\n        }\n        // Transform the data to match the expected extended schema with absolute amounts\n        const transformedStructures = (structures || []).map((structure)=>({\n                id: structure.id,\n                commission_type: structure.commission_type,\n                package_value: structure.package_value,\n                // Budget configuration\n                network_distribution_budget: structure.network_distribution_budget || 0,\n                company_wallet_amount: structure.company_wallet_amount || 0,\n                // Commission amounts (absolute values)\n                direct_commission_amount: structure.direct_commission_amount || 0,\n                level_commission_amount: structure.level_commission_amount || 0,\n                voucher_amount: structure.voucher_amount || 0,\n                festival_bonus_amount: structure.festival_bonus_amount || 0,\n                saving_amount: structure.saving_amount || 0,\n                gift_center_amount: structure.gift_center_amount || 0,\n                entertainment_amount: structure.entertainment_amount || 0,\n                medical_amount: structure.medical_amount || 0,\n                education_amount: structure.education_amount || 0,\n                credit_amount: structure.credit_amount || 0,\n                // ZM and RSM amounts\n                zm_bonus_amount: structure.zm_bonus_amount || 0,\n                zm_petral_allowance_amount: structure.zm_petral_allowance_amount || 0,\n                zm_leasing_facility_amount: structure.zm_leasing_facility_amount || 0,\n                zm_phone_bill_amount: structure.zm_phone_bill_amount || 0,\n                rsm_bonus_amount: structure.rsm_bonus_amount || 0,\n                rsm_petral_allowance_amount: structure.rsm_petral_allowance_amount || 0,\n                rsm_leasing_facility_amount: structure.rsm_leasing_facility_amount || 0,\n                rsm_phone_bill_amount: structure.rsm_phone_bill_amount || 0,\n                // Gift system amounts\n                present_user_amount: structure.present_user_amount || 0,\n                present_leader_amount: structure.present_leader_amount || 0,\n                annual_present_user_amount: structure.annual_present_user_amount || 0,\n                annual_present_leader_amount: structure.annual_present_leader_amount || 0,\n                // OKDOI Head amounts (package-specific)\n                okdoi_head_amount_2000: structure.okdoi_head_amount_2000 || 0,\n                okdoi_head_amount_5000: structure.okdoi_head_amount_5000 || 0,\n                okdoi_head_amount_10000: structure.okdoi_head_amount_10000 || 0,\n                okdoi_head_amount_50000: structure.okdoi_head_amount_50000 || 0,\n                is_active: structure.is_active,\n                created_at: structure.created_at,\n                updated_at: structure.updated_at\n            }));\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: transformedStructures\n        });\n    } catch (error) {\n        console.error(\"Error fetching unified commission structures:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Failed to fetch commission structures\"\n        }, {\n            status: 500\n        });\n    }\n}\nasync function POST(request) {\n    try {\n        const body = await request.json();\n        // Validate required fields\n        if (!body.package_value) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Package value is required\"\n            }, {\n                status: 400\n            });\n        }\n        // Check if unified structure already exists for this package\n        const { data: existing, error: checkError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabaseAdmin.from(\"commission_structure\").select(\"id\").eq(\"commission_type\", \"unified_structure\").eq(\"package_value\", body.package_value).single();\n        if (existing) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Commission structure already exists for this package\"\n            }, {\n                status: 400\n            });\n        }\n        // Create new unified commission structure with absolute amounts\n        const { data: newStructure, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabaseAdmin.from(\"commission_structure\").insert({\n            commission_type: \"unified_structure\",\n            package_value: body.package_value,\n            // Budget configuration\n            network_distribution_budget: body.network_distribution_budget || 0,\n            company_wallet_amount: body.company_wallet_amount || 0,\n            // Commission amounts\n            direct_commission_amount: body.direct_commission_amount || 0,\n            level_commission_amount: body.level_commission_amount || 0,\n            voucher_amount: body.voucher_amount || 0,\n            festival_bonus_amount: body.festival_bonus_amount || 0,\n            saving_amount: body.saving_amount || 0,\n            gift_center_amount: body.gift_center_amount || 0,\n            entertainment_amount: body.entertainment_amount || 0,\n            medical_amount: body.medical_amount || 0,\n            education_amount: body.education_amount || 0,\n            credit_amount: body.credit_amount || 0,\n            // ZM and RSM amounts\n            zm_bonus_amount: body.zm_bonus_amount || 0,\n            zm_petral_allowance_amount: body.zm_petral_allowance_amount || 0,\n            zm_leasing_facility_amount: body.zm_leasing_facility_amount || 0,\n            zm_phone_bill_amount: body.zm_phone_bill_amount || 0,\n            rsm_bonus_amount: body.rsm_bonus_amount || 0,\n            rsm_petral_allowance_amount: body.rsm_petral_allowance_amount || 0,\n            rsm_leasing_facility_amount: body.rsm_leasing_facility_amount || 0,\n            rsm_phone_bill_amount: body.rsm_phone_bill_amount || 0,\n            // Gift system amounts\n            present_user_amount: body.present_user_amount || 0,\n            present_leader_amount: body.present_leader_amount || 0,\n            annual_present_user_amount: body.annual_present_user_amount || 0,\n            annual_present_leader_amount: body.annual_present_leader_amount || 0,\n            // OKDOI Head amounts\n            okdoi_head_amount_2000: body.okdoi_head_amount_2000 || 0,\n            okdoi_head_amount_5000: body.okdoi_head_amount_5000 || 0,\n            okdoi_head_amount_10000: body.okdoi_head_amount_10000 || 0,\n            okdoi_head_amount_50000: body.okdoi_head_amount_50000 || 0,\n            okdoi_head_rate_2000: body.okdoi_head_rate_2000 || 0.025,\n            okdoi_head_rate_5000: body.okdoi_head_rate_5000 || 0.02,\n            okdoi_head_rate_10000: body.okdoi_head_rate_10000 || 0.02,\n            okdoi_head_rate_50000: body.okdoi_head_rate_50000 || 0.02,\n            // ZM rates\n            zm_bonus_rate: body.zm_bonus_rate || 0.02,\n            zm_petral_allowance_rate: body.zm_petral_allowance_rate || 0.005,\n            zm_leasing_facility_rate: body.zm_leasing_facility_rate || 0.01,\n            zm_phone_bill_rate: body.zm_phone_bill_rate || 0.001,\n            // RSM rates\n            rsm_bonus_rate: body.rsm_bonus_rate || 0.025,\n            rsm_petral_allowance_rate: body.rsm_petral_allowance_rate || 0.005,\n            rsm_leasing_facility_rate: body.rsm_leasing_facility_rate || 0.01,\n            rsm_phone_bill_rate: body.rsm_phone_bill_rate || 0.001,\n            is_active: body.is_active !== undefined ? body.is_active : true\n        }).select().single();\n        if (error) {\n            throw new Error(`Failed to create commission structure: ${error.message}`);\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            message: \"Commission structure created successfully\",\n            data: newStructure\n        });\n    } catch (error) {\n        console.error(\"Error creating commission structure:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Failed to create commission structure\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/admin/unified-commission-structure/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/supabase.ts":
/*!*****************************!*\
  !*** ./src/lib/supabase.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TABLES: () => (/* binding */ TABLES),\n/* harmony export */   supabase: () => (/* binding */ supabase),\n/* harmony export */   supabaseAdmin: () => (/* binding */ supabaseAdmin)\n/* harmony export */ });\n/* harmony import */ var _supabase_ssr__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/ssr */ \"(rsc)/./node_modules/@supabase/ssr/dist/module/index.js\");\n/* harmony import */ var _barrel_optimize_names_createClient_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! __barrel_optimize__?names=createClient!=!@supabase/supabase-js */ \"(rsc)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n\n\n// Environment variables with validation\nconst supabaseUrl = \"https://vnmydqbwjjufnxngpnqo.supabase.co\";\nconst supabaseAnonKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InZubXlkcWJ3amp1Zm54bmdwbnFvIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTYyODkyNjgsImV4cCI6MjA3MTg2NTI2OH0.23oAdwSQ11jasIhrtZf71oeC6ehBsgTda1iCRU8myCo\";\nconst supabaseServiceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;\n// Validate required environment variables\nif (!supabaseUrl) {\n    console.error(\"Missing NEXT_PUBLIC_SUPABASE_URL environment variable\");\n    throw new Error(\"Missing NEXT_PUBLIC_SUPABASE_URL environment variable\");\n}\nif (!supabaseAnonKey) {\n    console.error(\"Missing NEXT_PUBLIC_SUPABASE_ANON_KEY environment variable\");\n    throw new Error(\"Missing NEXT_PUBLIC_SUPABASE_ANON_KEY environment variable\");\n}\nif (!supabaseServiceRoleKey) {\n    console.warn(\"Missing SUPABASE_SERVICE_ROLE_KEY environment variable - admin functions will not work\");\n}\n// Create browser client with error handling\nlet supabase;\ntry {\n    supabase = (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createBrowserClient)(supabaseUrl, supabaseAnonKey, {\n        cookies: {\n            get (name) {\n                if (typeof document !== \"undefined\") {\n                    const value = document.cookie.split(\"; \").find((row)=>row.startsWith(`${name}=`))?.split(\"=\")[1];\n                    return value ? decodeURIComponent(value) : undefined;\n                }\n                return undefined;\n            },\n            set (name, value, options) {\n                if (typeof document !== \"undefined\") {\n                    let cookieString = `${name}=${encodeURIComponent(value)}`;\n                    if (options?.maxAge) cookieString += `; max-age=${options.maxAge}`;\n                    if (options?.path) cookieString += `; path=${options.path}`;\n                    if (options?.domain) cookieString += `; domain=${options.domain}`;\n                    if (options?.secure) cookieString += \"; secure\";\n                    if (options?.httpOnly) cookieString += \"; httponly\";\n                    if (options?.sameSite) cookieString += `; samesite=${options.sameSite}`;\n                    document.cookie = cookieString;\n                }\n            },\n            remove (name, options) {\n                if (typeof document !== \"undefined\") {\n                    let cookieString = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 GMT`;\n                    if (options?.path) cookieString += `; path=${options.path}`;\n                    if (options?.domain) cookieString += `; domain=${options.domain}`;\n                    document.cookie = cookieString;\n                }\n            }\n        }\n    });\n} catch (error) {\n    console.error(\"Failed to create Supabase browser client:\", error);\n    // Fallback to basic client without SSR\n    supabase = (0,_barrel_optimize_names_createClient_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_1__.createClient)(supabaseUrl, supabaseAnonKey);\n}\n// Admin client with service role key for bypassing RLS\n// Note: This will be null on client-side for security reasons\nconst supabaseAdmin = supabaseServiceRoleKey ? (0,_barrel_optimize_names_createClient_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_1__.createClient)(supabaseUrl, supabaseServiceRoleKey, {\n    auth: {\n        autoRefreshToken: false,\n        persistSession: false\n    }\n}) : null;\n\n// Database table names\nconst TABLES = {\n    CATEGORIES: \"categories\",\n    SUBCATEGORIES: \"subcategories\",\n    ADS: \"ads\",\n    USERS: \"users\",\n    AD_IMAGES: \"ad_images\",\n    DISTRICTS: \"districts\",\n    CITIES: \"cities\",\n    USER_FAVORITES: \"user_favorites\",\n    VENDOR_SHOPS: \"vendor_shops\",\n    SHOP_PRODUCTS: \"shop_products\",\n    SHOP_PRODUCT_IMAGES: \"shop_product_images\",\n    SHOP_REVIEWS: \"shop_reviews\",\n    SHOP_FOLLOWERS: \"shop_followers\",\n    SHOP_CATEGORIES: \"shop_categories\",\n    SHOP_SUBCATEGORIES: \"shop_subcategories\",\n    PRODUCT_REVIEWS: \"product_reviews\",\n    CHAT_CONVERSATIONS: \"chat_conversations\",\n    CHAT_MESSAGES: \"chat_messages\",\n    USER_WALLETS: \"user_wallets\",\n    WALLET_TRANSACTIONS: \"wallet_transactions\",\n    P2P_TRANSFERS: \"p2p_transfers\",\n    DEPOSIT_REQUESTS: \"deposit_requests\",\n    WITHDRAWAL_REQUESTS: \"withdrawal_requests\",\n    SUBSCRIPTION_PACKAGES: \"subscription_packages\",\n    USER_SUBSCRIPTIONS: \"user_subscriptions\",\n    AD_BOOSTS: \"ad_boosts\",\n    BOOST_PACKAGES: \"boost_packages\",\n    // Order Management System\n    CART_ITEMS: \"cart_items\",\n    SHOP_ORDERS: \"shop_orders\",\n    ORDER_ITEMS: \"order_items\",\n    ORDER_STATUS_HISTORY: \"order_status_history\",\n    // Merchant Wallet System\n    MERCHANT_WALLETS: \"merchant_wallets\",\n    MERCHANT_WALLET_TRANSACTIONS: \"merchant_wallet_transactions\",\n    MERCHANT_TO_MAIN_TRANSFERS: \"merchant_to_main_transfers\",\n    // Referral & Commission System\n    REFERRAL_HIERARCHY: \"referral_hierarchy\",\n    COMMISSION_STRUCTURE: \"commission_structure\",\n    COMMISSION_TRANSACTIONS: \"commission_transactions\",\n    REFERRAL_PLACEMENTS: \"referral_placements\",\n    // KYC System\n    KYC_SUBMISSIONS: \"kyc_submissions\",\n    KYC_STATUS_HISTORY: \"kyc_status_history\",\n    KYC_DOCUMENT_TYPES: \"kyc_document_types\",\n    ZONAL_MANAGERS: \"zonal_managers\",\n    REGIONAL_SALES_MANAGERS: \"regional_sales_managers\"\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/supabase.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/whatwg-url","vendor-chunks/cookie","vendor-chunks/webidl-conversions"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fadmin%2Funified-commission-structure%2Froute&page=%2Fapi%2Fadmin%2Funified-commission-structure%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Funified-commission-structure%2Froute.ts&appDir=C%3A%5CUsers%5Cdream%5CDesktop%5Cokdoi%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cdream%5CDesktop%5Cokdoi&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();