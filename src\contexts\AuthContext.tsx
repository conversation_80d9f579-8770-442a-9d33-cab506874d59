'use client'

import { createContext, useContext, useEffect, useState, ReactNode } from 'react'
import { AuthService, AuthUser } from '@/lib/auth'

interface AuthContextType {
  user: AuthUser | null
  loading: boolean
  signIn: (email: string, password: string) => Promise<void>
  signUp: (email: string, password: string, userData?: Partial<AuthUser> & { referralCode?: string, gender?: string, religion?: string }) => Promise<{ requireEmailVerification: boolean, referrer?: any }>
  signOut: () => Promise<void>
  updateProfile: (updates: Partial<AuthUser>) => Promise<void>
  verifyEmailOtp: (email: string, token: string) => Promise<void>
  resendEmailOtp: (email: string) => Promise<void>
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function AuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<AuthUser | null>(null)
  const [loading, setLoading] = useState(true)
  const [initialized, setInitialized] = useState(false)

  useEffect(() => {
    let isMounted = true

    // Get initial session with better error handling
    const getInitialSession = async () => {
      try {
        console.log('AuthProvider: Getting initial session...')

        // First check if we have a valid session
        const session = await AuthService.getSession()

        if (session?.user && isMounted) {
          console.log('AuthProvider: Found existing session for user:', session.user.email)
          const currentUser = await AuthService.getCurrentUser()
          if (isMounted) {
            setUser(currentUser)
            console.log('AuthProvider: User set successfully:', currentUser?.email)
          }
        } else {
          console.log('AuthProvider: No existing session found')
          if (isMounted) {
            setUser(null)
          }
        }
      } catch (error) {
        console.error('AuthProvider: Error getting initial session:', error)
        if (isMounted) {
          setUser(null)
        }
      } finally {
        if (isMounted) {
          setLoading(false)
          setInitialized(true)
          console.log('AuthProvider: Initialization complete')
        }
      }
    }

    getInitialSession()

    // Listen for auth changes with improved handling
    const { data: { subscription } } = AuthService.onAuthStateChange(
      async (event, session) => {
        console.log('AuthProvider: Auth state change:', event, session?.user?.email)

        if (!isMounted) return

        try {
          if (event === 'SIGNED_IN' && session?.user) {
            console.log('AuthProvider: User signed in, fetching profile...')
            const currentUser = await AuthService.getCurrentUser()
            if (isMounted) {
              setUser(currentUser)
              console.log('AuthProvider: User profile loaded:', currentUser?.email)
            }
          } else if (event === 'SIGNED_OUT') {
            console.log('AuthProvider: User signed out')
            if (isMounted) {
              setUser(null)
            }
          } else if (event === 'TOKEN_REFRESHED' && session?.user) {
            console.log('AuthProvider: Token refreshed, updating user...')
            const currentUser = await AuthService.getCurrentUser()
            if (isMounted) {
              setUser(currentUser)
            }
          }
        } catch (error) {
          console.error('AuthProvider: Error handling auth state change:', error)
          if (isMounted) {
            setUser(null)
          }
        } finally {
          if (isMounted && !initialized) {
            setLoading(false)
            setInitialized(true)
          }
        }
      }
    )

    return () => {
      isMounted = false
      subscription.unsubscribe()
    }
  }, [])

  const signIn = async (email: string, password: string) => {
    setLoading(true)
    try {
      await AuthService.signIn(email, password)
      // User state will be updated by the auth state change listener
    } catch (error) {
      setLoading(false)
      throw error
    }
  }

  const signUp = async (email: string, password: string, userData?: Partial<AuthUser> & { referralCode?: string, gender?: string, religion?: string }) => {
    setLoading(true)
    try {
      const result = await AuthService.signUp(email, password, userData)
      // Keep loading state active during email verification flow
      // Loading will be managed by the signup form component
      return {
        requireEmailVerification: result.requireEmailVerification,
        referrer: result.referrer
      }
    } catch (error) {
      setLoading(false)
      throw error
    }
  }

  const verifyEmailOtp = async (email: string, token: string) => {
    setLoading(true)
    try {
      await AuthService.verifyEmailOtp(email, token)
      // User state will be updated by the auth state change listener
    } catch (error) {
      setLoading(false)
      throw error
    }
  }

  const resendEmailOtp = async (email: string) => {
    try {
      await AuthService.resendEmailOtp(email)
    } catch (error) {
      throw error
    }
  }

  const signOut = async () => {
    setLoading(true)
    try {
      await AuthService.signOut()
      // User state will be updated by the auth state change listener
    } catch (error) {
      setLoading(false)
      throw error
    }
  }

  const updateProfile = async (updates: Partial<AuthUser>) => {
    if (!user) throw new Error('No user logged in')
    
    try {
      const updatedUser = await AuthService.updateProfile(user.id, updates)
      setUser(updatedUser)
    } catch (error) {
      throw error
    }
  }

  const value = {
    user,
    loading,
    signIn,
    signUp,
    signOut,
    updateProfile,
    verifyEmailOtp,
    resendEmailOtp,
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}
