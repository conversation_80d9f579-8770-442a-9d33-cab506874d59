
-- OKDOI Complete Database Backup
-- Generated: 2025-09-08T19:34:58.178Z
-- Project: vnmydqbwjjufnxngpnqo

-- ============================================================================
-- EXTENSIONS
-- ============================================================================

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- ============================================================================
-- CUSTOM TYPES (if any)
-- ============================================================================

-- Add any custom enum types here
-- Example: CREATE TYPE user_role AS ENUM ('user', 'admin', 'super_admin');

-- ============================================================================
-- TABLES STRUCTURE
-- ============================================================================

-- This section would contain CREATE TABLE statements
-- Note: Use supabase db dump for complete schema

-- ============================================================================
-- RLS POLICIES
-- ============================================================================

-- Enable RLS on all tables
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE ads ENABLE ROW LEVEL SECURITY;
ALTER TABLE categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE subcategories ENABLE ROW LEVEL SECURITY;
ALTER TABLE ad_images ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_favorites ENABLE ROW LEVEL SECURITY;
ALTER TABLE chat_conversations ENABLE ROW LEVEL SECURITY;
ALTER TABLE chat_messages ENABLE ROW LEVEL SECURITY;
ALTER TABLE vendor_shops ENABLE ROW LEVEL SECURITY;
ALTER TABLE shop_products ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_wallets ENABLE ROW LEVEL SECURITY;
ALTER TABLE wallet_transactions ENABLE ROW LEVEL SECURITY;

-- Add RLS policies (these would need to be extracted from your current setup)
-- Example policies:

-- Users can view their own data
CREATE POLICY "Users can view own data" ON users
  FOR SELECT USING (auth.uid() = id);

-- Users can update their own data
CREATE POLICY "Users can update own data" ON users
  FOR UPDATE USING (auth.uid() = id);

-- Public can view active ads
CREATE POLICY "Public can view active ads" ON ads
  FOR SELECT USING (status = 'active');

-- Users can manage their own ads
CREATE POLICY "Users can manage own ads" ON ads
  FOR ALL USING (auth.uid() = user_id);

-- ============================================================================
-- FUNCTIONS
-- ============================================================================

-- Add custom functions here
-- These would need to be extracted from your current database

-- Example function for updating timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- ============================================================================
-- TRIGGERS
-- ============================================================================

-- Add triggers for updated_at columns
-- Example:
-- CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users
--   FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- ============================================================================
-- INDEXES
-- ============================================================================

-- Add performance indexes
-- Examples:
-- CREATE INDEX idx_ads_user_id ON ads(user_id);
-- CREATE INDEX idx_ads_category_id ON ads(category_id);
-- CREATE INDEX idx_ads_status ON ads(status);
-- CREATE INDEX idx_ads_created_at ON ads(created_at);

-- ============================================================================
-- STORAGE BUCKETS
-- ============================================================================

-- Create storage buckets
-- Note: These need to be created via Supabase dashboard or API

-- INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
-- VALUES 
--   ('ad-images', 'ad-images', true, 52428800, ARRAY['image/jpeg', 'image/png', 'image/webp', 'image/gif']),
--   ('kyc-documents', 'kyc-documents', false, 10485760, ARRAY['image/jpeg', 'image/png', 'application/pdf']);

-- ============================================================================
-- STORAGE POLICIES
-- ============================================================================

-- Add storage bucket policies
-- Examples:
-- CREATE POLICY "Public can view ad images" ON storage.objects
--   FOR SELECT USING (bucket_id = 'ad-images');

-- CREATE POLICY "Users can upload ad images" ON storage.objects
--   FOR INSERT WITH CHECK (bucket_id = 'ad-images' AND auth.role() = 'authenticated');

-- ============================================================================
-- INITIAL DATA
-- ============================================================================

-- Insert initial/seed data
-- This would include categories, admin settings, etc.

-- ============================================================================
-- FINAL NOTES
-- ============================================================================

-- This is a template SQL backup script.
-- For a complete backup, you should:
-- 1. Use 'supabase db dump' for complete schema
-- 2. Export data using the JSON backup scripts
-- 3. Manually recreate storage buckets and policies
-- 4. Test the restore process thoroughly

-- End of backup script
