import { useState, useCallback, useEffect } from 'react'
import { EnhancedNetworkTreeNode } from '@/components/admin/EnhancedReferralTree'

export const useTreeNavigation = (rootNode: EnhancedNetworkTreeNode | null) => {
  const [expandedNodes, setExpandedNodes] = useState<Set<string>>(new Set())
  const [selectedNodeId, setSelectedNodeId] = useState<string | null>(null)

  // Auto-expand root node when rootNode changes
  useEffect(() => {
    if (rootNode) {
      setExpandedNodes(new Set([rootNode.user.id]))
      setSelectedNodeId(null)
    }
  }, [rootNode])

  // Toggle expand/collapse for a specific node
  const toggleExpand = useCallback((nodeId: string) => {
    setExpandedNodes(prev => {
      const newExpanded = new Set(prev)
      if (newExpanded.has(nodeId)) {
        newExpanded.delete(nodeId)
      } else {
        newExpanded.add(nodeId)
      }
      return newExpanded
    })
  }, [])

  // Expand a specific node
  const expandNode = useCallback((nodeId: string) => {
    setExpandedNodes(prev => new Set([...prev, nodeId]))
  }, [])

  // Collapse a specific node
  const collapseNode = useCallback((nodeId: string) => {
    setExpandedNodes(prev => {
      const newExpanded = new Set(prev)
      newExpanded.delete(nodeId)
      return newExpanded
    })
  }, [])

  // Expand all nodes in the tree
  const expandAll = useCallback(() => {
    if (!rootNode) return

    const allNodeIds = new Set<string>()
    
    const collectNodeIds = (node: EnhancedNetworkTreeNode) => {
      allNodeIds.add(node.user.id)
      if (node.children && node.children.length > 0) {
        node.children.forEach(collectNodeIds)
      }
    }

    collectNodeIds(rootNode)
    setExpandedNodes(allNodeIds)
  }, [rootNode])

  // Collapse all nodes except root
  const collapseAll = useCallback(() => {
    if (rootNode) {
      setExpandedNodes(new Set([rootNode.user.id]))
    }
  }, [rootNode])

  // Expand to a specific depth level
  const expandToLevel = useCallback((maxLevel: number) => {
    if (!rootNode) return

    const expandedIds = new Set<string>()
    
    const expandToDepth = (node: EnhancedNetworkTreeNode, currentLevel: number = 0) => {
      if (currentLevel <= maxLevel) {
        expandedIds.add(node.user.id)
        if (node.children && node.children.length > 0) {
          node.children.forEach(child => expandToDepth(child, currentLevel + 1))
        }
      }
    }

    expandToDepth(rootNode)
    setExpandedNodes(expandedIds)
  }, [rootNode])

  // Select a specific node
  const selectNode = useCallback((nodeId: string | null) => {
    setSelectedNodeId(nodeId)
  }, [])

  // Expand path to a specific node (useful for search results)
  const expandPathToNode = useCallback((targetNodeId: string) => {
    if (!rootNode) return false

    const pathToNode: string[] = []
    
    const findPath = (node: EnhancedNetworkTreeNode, path: string[] = []): boolean => {
      const currentPath = [...path, node.user.id]
      
      if (node.user.id === targetNodeId) {
        pathToNode.push(...currentPath)
        return true
      }

      if (node.children && node.children.length > 0) {
        for (const child of node.children) {
          if (findPath(child, currentPath)) {
            return true
          }
        }
      }

      return false
    }

    if (findPath(rootNode)) {
      // Expand all nodes in the path except the target node itself
      setExpandedNodes(prev => {
        const newExpanded = new Set(prev)
        pathToNode.slice(0, -1).forEach(nodeId => newExpanded.add(nodeId))
        return newExpanded
      })
      return true
    }

    return false
  }, [rootNode])

  // Get node statistics
  const getNodeStats = useCallback(() => {
    if (!rootNode) return { totalNodes: 0, expandedCount: 0, visibleNodes: 0 }

    let totalNodes = 0
    let visibleNodes = 0

    const countNodes = (node: EnhancedNetworkTreeNode, isVisible: boolean = true) => {
      totalNodes++
      if (isVisible) visibleNodes++

      if (node.children && node.children.length > 0) {
        const isExpanded = expandedNodes.has(node.user.id)
        node.children.forEach(child => 
          countNodes(child, isVisible && isExpanded)
        )
      }
    }

    countNodes(rootNode)

    return {
      totalNodes,
      expandedCount: expandedNodes.size,
      visibleNodes
    }
  }, [rootNode, expandedNodes])

  // Check if a node is expanded
  const isNodeExpanded = useCallback((nodeId: string) => {
    return expandedNodes.has(nodeId)
  }, [expandedNodes])

  // Check if a node is selected
  const isNodeSelected = useCallback((nodeId: string) => {
    return selectedNodeId === nodeId
  }, [selectedNodeId])

  // Get all expanded node IDs as array
  const getExpandedNodeIds = useCallback(() => {
    return Array.from(expandedNodes)
  }, [expandedNodes])

  // Set expanded nodes from array
  const setExpandedNodeIds = useCallback((nodeIds: string[]) => {
    setExpandedNodes(new Set(nodeIds))
  }, [])

  // Find node by ID in the tree
  const findNodeById = useCallback((nodeId: string): EnhancedNetworkTreeNode | null => {
    if (!rootNode) return null

    const findNode = (node: EnhancedNetworkTreeNode): EnhancedNetworkTreeNode | null => {
      if (node.user.id === nodeId) return node

      if (node.children && node.children.length > 0) {
        for (const child of node.children) {
          const found = findNode(child)
          if (found) return found
        }
      }

      return null
    }

    return findNode(rootNode)
  }, [rootNode])

  return {
    expandedNodes,
    selectedNodeId,
    toggleExpand,
    expandNode,
    collapseNode,
    expandAll,
    collapseAll,
    expandToLevel,
    selectNode,
    expandPathToNode,
    getNodeStats,
    isNodeExpanded,
    isNodeSelected,
    getExpandedNodeIds,
    setExpandedNodeIds,
    findNodeById
  }
}
