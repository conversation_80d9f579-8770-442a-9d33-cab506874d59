-- Order Management System Migration
-- Creates comprehensive e-commerce order system for shop products

-- Shopping cart items table
CREATE TABLE cart_items (
    id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id uuid REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    product_id uuid REFERENCES shop_products(id) ON DELETE CASCADE NOT NULL,
    quantity integer NOT NULL DEFAULT 1 CHECK (quantity > 0),
    selected_variant jsonb DEFAULT '{}', -- For product variants (size, color, etc.)
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    UNIQUE(user_id, product_id, selected_variant)
);

-- Shop orders table
CREATE TABLE shop_orders (
    id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
    order_number varchar(50) UNIQUE NOT NULL,
    buyer_id uuid REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    shop_id uuid REFERENCES vendor_shops(id) ON DELETE CASCADE NOT NULL,
    seller_id uuid REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    
    -- Order totals
    subtotal numeric(12,2) NOT NULL DEFAULT 0,
    shipping_cost numeric(12,2) NOT NULL DEFAULT 0,
    tax_amount numeric(12,2) NOT NULL DEFAULT 0,
    discount_amount numeric(12,2) NOT NULL DEFAULT 0,
    total_amount numeric(12,2) NOT NULL DEFAULT 0,
    currency varchar(3) DEFAULT 'LKR',
    
    -- Order status and tracking
    status varchar(20) DEFAULT 'pending' CHECK (status IN ('pending', 'confirmed', 'processing', 'shipped', 'delivered', 'cancelled', 'refunded')),
    payment_status varchar(20) DEFAULT 'pending' CHECK (payment_status IN ('pending', 'paid', 'failed', 'refunded')),
    payment_method varchar(20) DEFAULT 'wallet' CHECK (payment_method IN ('wallet', 'bank_transfer', 'cash_on_delivery')),
    
    -- Shipping information
    shipping_address jsonb NOT NULL, -- {name, phone, address, city, district, postal_code}
    billing_address jsonb, -- Optional separate billing address
    tracking_number varchar(100),
    tracking_url text,
    estimated_delivery_date timestamp with time zone,
    delivered_at timestamp with time zone,
    
    -- Order notes and communication
    buyer_notes text,
    seller_notes text,
    admin_notes text,
    
    -- Timestamps
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    confirmed_at timestamp with time zone,
    shipped_at timestamp with time zone,
    cancelled_at timestamp with time zone,
    
    -- Wallet transaction reference
    wallet_transaction_id uuid REFERENCES wallet_transactions(id)
);

-- Order items table
CREATE TABLE order_items (
    id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
    order_id uuid REFERENCES shop_orders(id) ON DELETE CASCADE NOT NULL,
    product_id uuid REFERENCES shop_products(id) ON DELETE CASCADE NOT NULL,
    
    -- Product snapshot at time of order (in case product details change)
    product_title varchar(200) NOT NULL,
    product_description text,
    product_sku varchar(100),
    selected_variant jsonb DEFAULT '{}',
    
    -- Pricing and quantity
    unit_price numeric(12,2) NOT NULL,
    quantity integer NOT NULL DEFAULT 1 CHECK (quantity > 0),
    total_price numeric(12,2) NOT NULL,
    currency varchar(3) DEFAULT 'LKR',
    
    -- Item status (for partial fulfillment)
    status varchar(20) DEFAULT 'pending' CHECK (status IN ('pending', 'confirmed', 'processing', 'shipped', 'delivered', 'cancelled', 'refunded')),
    
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now()
);

-- Order status history table for tracking changes
CREATE TABLE order_status_history (
    id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
    order_id uuid REFERENCES shop_orders(id) ON DELETE CASCADE NOT NULL,
    status varchar(20) NOT NULL,
    notes text,
    changed_by uuid REFERENCES auth.users(id) ON DELETE SET NULL,
    changed_at timestamp with time zone DEFAULT now()
);

-- Create indexes for better performance
CREATE INDEX idx_cart_items_user ON cart_items(user_id);
CREATE INDEX idx_cart_items_product ON cart_items(product_id);
CREATE INDEX idx_shop_orders_buyer ON shop_orders(buyer_id);
CREATE INDEX idx_shop_orders_seller ON shop_orders(seller_id);
CREATE INDEX idx_shop_orders_shop ON shop_orders(shop_id);
CREATE INDEX idx_shop_orders_status ON shop_orders(status);
CREATE INDEX idx_shop_orders_payment_status ON shop_orders(payment_status);
CREATE INDEX idx_shop_orders_created_at ON shop_orders(created_at);
CREATE INDEX idx_order_items_order ON order_items(order_id);
CREATE INDEX idx_order_items_product ON order_items(product_id);
CREATE INDEX idx_order_status_history_order ON order_status_history(order_id);

-- Generate unique order numbers
CREATE OR REPLACE FUNCTION generate_order_number()
RETURNS varchar(50) AS $$
DECLARE
    new_number varchar(50);
    counter integer := 0;
BEGIN
    LOOP
        new_number := 'ORD-' || TO_CHAR(NOW(), 'YYYYMMDD') || '-' || LPAD((EXTRACT(EPOCH FROM NOW())::bigint % 100000)::text, 5, '0');
        
        -- Check if this number already exists
        IF NOT EXISTS (SELECT 1 FROM shop_orders WHERE order_number = new_number) THEN
            RETURN new_number;
        END IF;
        
        counter := counter + 1;
        IF counter > 100 THEN
            -- Fallback with random component
            new_number := 'ORD-' || TO_CHAR(NOW(), 'YYYYMMDD') || '-' || LPAD(FLOOR(RANDOM() * 99999)::text, 5, '0');
            EXIT;
        END IF;
    END LOOP;
    
    RETURN new_number;
END;
$$ LANGUAGE plpgsql;

-- Trigger to auto-generate order numbers
CREATE OR REPLACE FUNCTION set_order_number()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.order_number IS NULL OR NEW.order_number = '' THEN
        NEW.order_number := generate_order_number();
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_set_order_number
    BEFORE INSERT ON shop_orders
    FOR EACH ROW
    EXECUTE FUNCTION set_order_number();

-- Function to update order totals when items change
CREATE OR REPLACE FUNCTION update_order_totals()
RETURNS TRIGGER AS $$
BEGIN
    -- Update the order totals based on order items
    UPDATE shop_orders 
    SET 
        subtotal = (
            SELECT COALESCE(SUM(total_price), 0) 
            FROM order_items 
            WHERE order_id = COALESCE(NEW.order_id, OLD.order_id)
        ),
        total_amount = (
            SELECT COALESCE(SUM(total_price), 0) + shipping_cost + tax_amount - discount_amount
            FROM order_items 
            WHERE order_id = COALESCE(NEW.order_id, OLD.order_id)
        ),
        updated_at = now()
    WHERE id = COALESCE(NEW.order_id, OLD.order_id);
    
    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_order_totals
    AFTER INSERT OR UPDATE OR DELETE ON order_items
    FOR EACH ROW
    EXECUTE FUNCTION update_order_totals();

-- Function to track order status changes
CREATE OR REPLACE FUNCTION track_order_status_change()
RETURNS TRIGGER AS $$
BEGIN
    -- Only track if status actually changed
    IF OLD.status IS DISTINCT FROM NEW.status THEN
        INSERT INTO order_status_history (order_id, status, notes, changed_by)
        VALUES (NEW.id, NEW.status, 'Status changed from ' || COALESCE(OLD.status, 'null') || ' to ' || NEW.status, NEW.seller_id);
        
        -- Update specific timestamp fields based on status
        CASE NEW.status
            WHEN 'confirmed' THEN
                NEW.confirmed_at := now();
            WHEN 'shipped' THEN
                NEW.shipped_at := now();
            WHEN 'cancelled' THEN
                NEW.cancelled_at := now();
            WHEN 'delivered' THEN
                NEW.delivered_at := now();
            ELSE
                -- No specific timestamp update needed
        END CASE;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_track_order_status_change
    BEFORE UPDATE ON shop_orders
    FOR EACH ROW
    EXECUTE FUNCTION track_order_status_change();

-- Function to update shop sales statistics
CREATE OR REPLACE FUNCTION update_shop_sales_stats()
RETURNS TRIGGER AS $$
BEGIN
    -- Update shop total_sales when order is completed
    IF NEW.status = 'delivered' AND (OLD.status IS NULL OR OLD.status != 'delivered') THEN
        UPDATE vendor_shops 
        SET 
            total_sales = total_sales + 1,
            updated_at = now()
        WHERE id = NEW.shop_id;
    ELSIF OLD.status = 'delivered' AND NEW.status != 'delivered' THEN
        -- Decrease if order was delivered but now changed to different status
        UPDATE vendor_shops 
        SET 
            total_sales = GREATEST(total_sales - 1, 0),
            updated_at = now()
        WHERE id = NEW.shop_id;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_shop_sales_stats
    AFTER UPDATE ON shop_orders
    FOR EACH ROW
    EXECUTE FUNCTION update_shop_sales_stats();

-- Add new table references to existing tables list
-- Note: This will be handled in the application code by updating TABLES constant
