-- Create shop categories system (separate from classified ad categories)

-- Shop categories table
CREATE TABLE shop_categories (
    id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
    name varchar(100) NOT NULL,
    slug varchar(100) UNIQUE NOT NULL,
    description text,
    icon varchar(50),
    image_url text,
    sort_order integer DEFAULT 0,
    is_active boolean DEFAULT true,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now()
);

-- Shop subcategories table
CREATE TABLE shop_subcategories (
    id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
    category_id uuid REFERENCES shop_categories(id) ON DELETE CASCADE NOT NULL,
    name varchar(100) NOT NULL,
    slug varchar(100) NOT NULL,
    description text,
    sort_order integer DEFAULT 0,
    is_active boolean DEFAULT true,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    UNIQUE(category_id, slug)
);

-- Add category reference to shop_products table
ALTER TABLE shop_products ADD COLUMN category_id uuid REFERENCES shop_categories(id);
ALTER TABLE shop_products ADD COLUMN subcategory_id uuid REFERENCES shop_subcategories(id);

-- Create indexes for better performance
CREATE INDEX idx_shop_categories_slug ON shop_categories(slug);
CREATE INDEX idx_shop_categories_active ON shop_categories(is_active);
CREATE INDEX idx_shop_subcategories_category ON shop_subcategories(category_id);
CREATE INDEX idx_shop_subcategories_slug ON shop_subcategories(category_id, slug);
CREATE INDEX idx_shop_products_category ON shop_products(category_id);
CREATE INDEX idx_shop_products_subcategory ON shop_products(subcategory_id);

-- Insert popular e-commerce categories
INSERT INTO shop_categories (name, slug, description, icon, sort_order) VALUES
('Electronics', 'electronics', 'Smartphones, laptops, gadgets and electronic accessories', 'Smartphone', 1),
('Fashion & Clothing', 'fashion-clothing', 'Clothing, shoes, accessories and fashion items', 'Shirt', 2),
('Home & Garden', 'home-garden', 'Furniture, home decor, kitchen items and garden supplies', 'Home', 3),
('Health & Beauty', 'health-beauty', 'Skincare, makeup, health supplements and beauty products', 'Heart', 4),
('Sports & Fitness', 'sports-fitness', 'Sports equipment, fitness gear and outdoor activities', 'Dumbbell', 5),
('Books & Media', 'books-media', 'Books, movies, music and educational materials', 'Book', 6),
('Toys & Games', 'toys-games', 'Children toys, board games and gaming accessories', 'Gamepad2', 7),
('Automotive', 'automotive', 'Car parts, accessories and automotive supplies', 'Car', 8),
('Food & Beverages', 'food-beverages', 'Gourmet food, beverages and specialty items', 'Coffee', 9),
('Jewelry & Watches', 'jewelry-watches', 'Jewelry, watches and precious accessories', 'Watch', 10),
('Pet Supplies', 'pet-supplies', 'Pet food, toys, accessories and care products', 'Heart', 11),
('Office & Business', 'office-business', 'Office supplies, business equipment and stationery', 'Briefcase', 12);

-- Insert subcategories for Electronics
INSERT INTO shop_subcategories (category_id, name, slug, sort_order) VALUES
((SELECT id FROM shop_categories WHERE slug = 'electronics'), 'Smartphones', 'smartphones', 1),
((SELECT id FROM shop_categories WHERE slug = 'electronics'), 'Laptops & Computers', 'laptops-computers', 2),
((SELECT id FROM shop_categories WHERE slug = 'electronics'), 'Tablets', 'tablets', 3),
((SELECT id FROM shop_categories WHERE slug = 'electronics'), 'Audio & Headphones', 'audio-headphones', 4),
((SELECT id FROM shop_categories WHERE slug = 'electronics'), 'Cameras', 'cameras', 5),
((SELECT id FROM shop_categories WHERE slug = 'electronics'), 'Gaming', 'gaming', 6),
((SELECT id FROM shop_categories WHERE slug = 'electronics'), 'Smart Home', 'smart-home', 7),
((SELECT id FROM shop_categories WHERE slug = 'electronics'), 'Accessories', 'accessories', 8);

-- Insert subcategories for Fashion & Clothing
INSERT INTO shop_subcategories (category_id, name, slug, sort_order) VALUES
((SELECT id FROM shop_categories WHERE slug = 'fashion-clothing'), 'Men''s Clothing', 'mens-clothing', 1),
((SELECT id FROM shop_categories WHERE slug = 'fashion-clothing'), 'Women''s Clothing', 'womens-clothing', 2),
((SELECT id FROM shop_categories WHERE slug = 'fashion-clothing'), 'Kids'' Clothing', 'kids-clothing', 3),
((SELECT id FROM shop_categories WHERE slug = 'fashion-clothing'), 'Shoes', 'shoes', 4),
((SELECT id FROM shop_categories WHERE slug = 'fashion-clothing'), 'Bags & Accessories', 'bags-accessories', 5),
((SELECT id FROM shop_categories WHERE slug = 'fashion-clothing'), 'Watches', 'watches', 6),
((SELECT id FROM shop_categories WHERE slug = 'fashion-clothing'), 'Sunglasses', 'sunglasses', 7);

-- Insert subcategories for Home & Garden
INSERT INTO shop_subcategories (category_id, name, slug, sort_order) VALUES
((SELECT id FROM shop_categories WHERE slug = 'home-garden'), 'Furniture', 'furniture', 1),
((SELECT id FROM shop_categories WHERE slug = 'home-garden'), 'Home Decor', 'home-decor', 2),
((SELECT id FROM shop_categories WHERE slug = 'home-garden'), 'Kitchen & Dining', 'kitchen-dining', 3),
((SELECT id FROM shop_categories WHERE slug = 'home-garden'), 'Bedding & Bath', 'bedding-bath', 4),
((SELECT id FROM shop_categories WHERE slug = 'home-garden'), 'Garden & Outdoor', 'garden-outdoor', 5),
((SELECT id FROM shop_categories WHERE slug = 'home-garden'), 'Tools & Hardware', 'tools-hardware', 6);

-- Insert subcategories for Health & Beauty
INSERT INTO shop_subcategories (category_id, name, slug, sort_order) VALUES
((SELECT id FROM shop_categories WHERE slug = 'health-beauty'), 'Skincare', 'skincare', 1),
((SELECT id FROM shop_categories WHERE slug = 'health-beauty'), 'Makeup', 'makeup', 2),
((SELECT id FROM shop_categories WHERE slug = 'health-beauty'), 'Hair Care', 'hair-care', 3),
((SELECT id FROM shop_categories WHERE slug = 'health-beauty'), 'Fragrances', 'fragrances', 4),
((SELECT id FROM shop_categories WHERE slug = 'health-beauty'), 'Health Supplements', 'health-supplements', 5),
((SELECT id FROM shop_categories WHERE slug = 'health-beauty'), 'Personal Care', 'personal-care', 6);

-- Insert subcategories for Sports & Fitness
INSERT INTO shop_subcategories (category_id, name, slug, sort_order) VALUES
((SELECT id FROM shop_categories WHERE slug = 'sports-fitness'), 'Fitness Equipment', 'fitness-equipment', 1),
((SELECT id FROM shop_categories WHERE slug = 'sports-fitness'), 'Sports Apparel', 'sports-apparel', 2),
((SELECT id FROM shop_categories WHERE slug = 'sports-fitness'), 'Outdoor Sports', 'outdoor-sports', 3),
((SELECT id FROM shop_categories WHERE slug = 'sports-fitness'), 'Team Sports', 'team-sports', 4),
((SELECT id FROM shop_categories WHERE slug = 'sports-fitness'), 'Water Sports', 'water-sports', 5);

-- Update timestamps trigger for shop_categories
CREATE OR REPLACE FUNCTION update_shop_categories_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_shop_categories_updated_at
    BEFORE UPDATE ON shop_categories
    FOR EACH ROW
    EXECUTE FUNCTION update_shop_categories_updated_at();

-- Update timestamps trigger for shop_subcategories
CREATE OR REPLACE FUNCTION update_shop_subcategories_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_shop_subcategories_updated_at
    BEFORE UPDATE ON shop_subcategories
    FOR EACH ROW
    EXECUTE FUNCTION update_shop_subcategories_updated_at();
