import { NextRequest, NextResponse } from 'next/server'
import { PresentAllocationService } from '@/lib/services/presentAllocationService'
import { AdminService } from '@/lib/services/admin'

export async function GET(request: NextRequest) {
  try {
    // Check admin authentication
    const isAdmin = await AdminService.isAdminServerSide()
    if (!isAdmin) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const action = searchParams.get('action')

    if (action === 'pool-summary') {
      const poolSummary = await PresentAllocationService.getPresentPoolSummary()
      return NextResponse.json({
        success: true,
        data: poolSummary
      })
    }

    if (action === 'stats') {
      const stats = await PresentAllocationService.getPresentAllocationStats()
      return NextResponse.json({
        success: true,
        data: stats
      })
    }

    // Default: Get present allocations with filters
    const allocationType = searchParams.get('allocation_type')
    const isRedeemed = searchParams.get('is_redeemed')
    const dateFrom = searchParams.get('date_from')
    const dateTo = searchParams.get('date_to')
    const limit = parseInt(searchParams.get('limit') || '50')
    const offset = parseInt(searchParams.get('offset') || '0')

    const filters: any = { limit, offset }

    if (allocationType) {
      filters.allocationType = allocationType
    }

    if (isRedeemed !== null) {
      filters.isRedeemed = isRedeemed === 'true'
    }

    if (dateFrom) {
      filters.dateFrom = dateFrom
    }

    if (dateTo) {
      filters.dateTo = dateTo
    }

    const result = await PresentAllocationService.getAllPresentAllocations(filters)

    return NextResponse.json({
      success: true,
      data: result
    })
  } catch (error) {
    console.error('GET /api/admin/present-management error:', error)
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to fetch present data'
      },
      { status: 500 }
    )
  }
}