'use client'

import React, { useState, useRef } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import {
  User,
  Mail,
  Phone,
  MapPin,
  Edit2,
  Save,
  X,
  Camera,
  Calendar,
  Shield,
  Upload,
  AlertCircle,
  Sparkles,
  Award,
  CheckCircle,
  Users
} from 'lucide-react'
import DashboardLayout from '@/components/dashboard/DashboardLayout'
import { PremiumButton, PremiumCard, FadeIn, SlideInUp, StaggerContainer, StaggerItem } from '@/components/ui/premium'
import Input from '@/components/ui/Input'
import { useAuth } from '@/contexts/AuthContext'
import { StorageService } from '@/lib/services/storage'
import NewKYCVerificationPage from '@/components/kyc/NewKYCVerificationPage'
import InheritanceTab from '@/components/dashboard/InheritanceTab'

export default function ProfilePage() {
  const { user, updateProfile } = useAuth()
  const fileInputRef = useRef<HTMLInputElement>(null)
  const [isEditing, setIsEditing] = useState(false)
  const [loading, setLoading] = useState(false)
  const [uploadingAvatar, setUploadingAvatar] = useState(false)
  const [error, setError] = useState('')
  const [success, setSuccess] = useState('')
  const [activeTab, setActiveTab] = useState<'profile' | 'verification' | 'inheritance'>('profile')
  const [formData, setFormData] = useState({
    full_name: user?.full_name || '',
    phone: user?.phone || '',
    location: user?.location || ''
  })

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData(prev => ({
      ...prev,
      [e.target.name]: e.target.value
    }))
  }

  const handleSave = async () => {
    setLoading(true)
    setError('')
    setSuccess('')

    try {
      await updateProfile(formData)
      setIsEditing(false)
      setSuccess('Profile updated successfully!')
      setTimeout(() => setSuccess(''), 3000)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to update profile')
    } finally {
      setLoading(false)
    }
  }

  const handleCancel = () => {
    setFormData({
      full_name: user?.full_name || '',
      phone: user?.phone || '',
      location: user?.location || ''
    })
    setIsEditing(false)
    setError('')
  }

  const handleAvatarClick = () => {
    fileInputRef.current?.click()
  }

  const handleAvatarChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (!file || !user) return

    // Validate file type
    if (!file.type.startsWith('image/')) {
      setError('Please select a valid image file')
      return
    }

    // Validate file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      setError('Image size must be less than 5MB')
      return
    }

    try {
      setUploadingAvatar(true)
      setError('')

      // Upload the image
      const avatarUrl = await StorageService.uploadImage(file, user.id)

      // Update user profile with new avatar URL
      await updateProfile({ avatar_url: avatarUrl })

      setSuccess('Profile picture updated successfully!')
      setTimeout(() => setSuccess(''), 3000)
    } catch (error) {
      console.error('Error uploading avatar:', error)
      setError(error instanceof Error ? error.message : 'Failed to upload profile picture')
    } finally {
      setUploadingAvatar(false)
    }
  }

  if (!user) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-blue"></div>
        </div>
      </DashboardLayout>
    )
  }

  return (
    <DashboardLayout>
      <div className="space-y-8">
        {/* Enhanced Header */}
        <FadeIn>
          <PremiumCard variant="premium">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <motion.div
                  className="p-3 bg-gradient-to-br from-primary-blue to-secondary-blue text-white"
                  whileHover={{ scale: 1.1, rotate: 5 }}
                  transition={{ duration: 0.2 }}
                >
                  <User className="h-6 w-6" />
                </motion.div>
                <div>
                  <motion.h1
                    className="text-3xl font-bold font-heading text-gray-900"
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.2 }}
                  >
                    Profile Management
                  </motion.h1>
                  <motion.p
                    className="text-gray-600 flex items-center gap-2"
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.3 }}
                  >
                    <Sparkles className="h-4 w-4 text-primary-blue" />
                    Manage your personal information and preferences
                  </motion.p>
                </div>
              </div>

              <AnimatePresence mode="wait">
                {!isEditing ? (
                  <motion.div
                    key="edit"
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={{ opacity: 1, scale: 1 }}
                    exit={{ opacity: 0, scale: 0.8 }}
                    transition={{ duration: 0.2 }}
                  >
                    <PremiumButton
                      variant="outline"
                      onClick={() => setIsEditing(true)}
                      icon={<Edit2 className="h-4 w-4" />}
                    >
                      Edit Profile
                    </PremiumButton>
                  </motion.div>
                ) : (
                  <motion.div
                    key="actions"
                    className="flex space-x-3"
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={{ opacity: 1, scale: 1 }}
                    exit={{ opacity: 0, scale: 0.8 }}
                    transition={{ duration: 0.2 }}
                  >
                    <PremiumButton
                      variant="outline"
                      onClick={handleCancel}
                      disabled={loading}
                      icon={<X className="h-4 w-4" />}
                    >
                      Cancel
                    </PremiumButton>
                    <PremiumButton
                      variant="primary"
                      onClick={handleSave}
                      loading={loading}
                      icon={<Save className="h-4 w-4" />}
                    >
                      Save Changes
                    </PremiumButton>
                  </motion.div>
                )}
              </AnimatePresence>
            </div>
          </PremiumCard>
        </FadeIn>

        {/* Enhanced Success/Error Messages */}
        <AnimatePresence>
          {success && (
            <motion.div
              className="bg-gradient-to-r from-green-50 to-green-100 border border-green-200 text-green-800 px-6 py-4 shadow-lg"
              initial={{ opacity: 0, y: -20, scale: 0.95 }}
              animate={{ opacity: 1, y: 0, scale: 1 }}
              exit={{ opacity: 0, y: -20, scale: 0.95 }}
              transition={{ duration: 0.3 }}
            >
              <div className="flex items-center gap-3">
                <motion.div
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  transition={{ delay: 0.2, type: "spring" }}
                >
                  <CheckCircle className="h-5 w-5 text-green-600" />
                </motion.div>
                <span className="font-medium">{success}</span>
              </div>
            </motion.div>
          )}

          {error && (
            <motion.div
              className="bg-gradient-to-r from-red-50 to-red-100 border border-red-200 text-red-800 px-6 py-4 shadow-lg"
              initial={{ opacity: 0, y: -20, scale: 0.95 }}
              animate={{ opacity: 1, y: 0, scale: 1 }}
              exit={{ opacity: 0, y: -20, scale: 0.95 }}
              transition={{ duration: 0.3 }}
            >
              <div className="flex items-center gap-3">
                <motion.div
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  transition={{ delay: 0.2, type: "spring" }}
                >
                  <AlertCircle className="h-5 w-5 text-red-600" />
                </motion.div>
                <span className="font-medium">{error}</span>
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Tab Navigation */}
        <FadeIn delay={0.3}>
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8">
              <button
                onClick={() => setActiveTab('profile')}
                className={`py-2 px-1 border-b-2 font-medium text-sm transition-colors ${
                  activeTab === 'profile'
                    ? 'border-primary-blue text-primary-blue'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <div className="flex items-center space-x-2">
                  <User className="h-4 w-4" />
                  <span>Profile Information</span>
                </div>
              </button>
              <button
                onClick={() => setActiveTab('verification')}
                className={`py-2 px-1 border-b-2 font-medium text-sm transition-colors ${
                  activeTab === 'verification'
                    ? 'border-primary-blue text-primary-blue'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <div className="flex items-center space-x-2">
                  <Shield className="h-4 w-4" />
                  <span>Identity Verification</span>
                </div>
              </button>
              <button
                onClick={() => setActiveTab('inheritance')}
                className={`py-2 px-1 border-b-2 font-medium text-sm transition-colors ${
                  activeTab === 'inheritance'
                    ? 'border-primary-blue text-primary-blue'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <div className="flex items-center space-x-2">
                  <Users className="h-4 w-4" />
                  <span>Inheritance</span>
                </div>
              </button>
            </nav>
          </div>
        </FadeIn>

        {/* Tab Content */}
        <AnimatePresence mode="wait">
          {activeTab === 'profile' && (
            <motion.div
              key="profile"
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: 20 }}
              transition={{ duration: 0.3 }}
            >
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Enhanced Profile Picture & Basic Info */}
          <SlideInUp delay={0.2} className="lg:col-span-1">
            <PremiumCard variant="premium" className="text-center">
              <div className="relative inline-block mb-6">
                <motion.div
                  className="relative"
                  whileHover={{ scale: 1.05 }}
                  transition={{ duration: 0.2 }}
                >
                  <div
                    className="w-32 h-32 rounded-full overflow-hidden cursor-pointer group relative"
                    onClick={handleAvatarClick}
                  >
                    {user.avatar_url ? (
                      <motion.img
                        src={user.avatar_url}
                        alt="Profile picture"
                        className="w-full h-full object-cover object-center group-hover:opacity-80 transition-opacity"
                        whileHover={{ scale: 1.1 }}
                        transition={{ duration: 0.3 }}
                      />
                    ) : (
                      <motion.div
                        className="w-full h-full bg-gradient-to-br from-primary-blue to-secondary-blue flex items-center justify-center text-white text-3xl font-bold group-hover:opacity-80 transition-opacity"
                        whileHover={{ scale: 1.1 }}
                        transition={{ duration: 0.3 }}
                      >
                        {user.full_name?.charAt(0) || user.email?.charAt(0) || 'U'}
                      </motion.div>
                    )}
                  </div>

                  <motion.button
                    onClick={handleAvatarClick}
                    disabled={uploadingAvatar}
                    className="absolute bottom-0 right-0 w-10 h-10 bg-white rounded-full shadow-lg border border-gray-200 flex items-center justify-center hover:bg-gray-50 transition-colors disabled:opacity-50"
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    <AnimatePresence mode="wait">
                      {uploadingAvatar ? (
                        <motion.div
                          className="w-5 h-5 border-2 border-primary-blue border-t-transparent"
                          animate={{ rotate: 360 }}
                          transition={{ duration: 1, repeat: Infinity, ease: 'linear' }}
                        />
                      ) : (
                        <motion.div
                          initial={{ scale: 0 }}
                          animate={{ scale: 1 }}
                          exit={{ scale: 0 }}
                        >
                          <Camera className="h-5 w-5 text-gray-600" />
                        </motion.div>
                      )}
                    </AnimatePresence>
                  </motion.button>

                  {/* Verified Badge */}
                  <motion.div
                    className="absolute -top-2 -right-2 bg-green-500 text-white p-2 shadow-lg"
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    transition={{ delay: 0.5, type: "spring" }}
                  >
                    <CheckCircle className="h-4 w-4" />
                  </motion.div>
                </motion.div>

                <input
                  ref={fileInputRef}
                  type="file"
                  accept="image/*"
                  onChange={handleAvatarChange}
                  className="hidden"
                />
              </div>

              <motion.h3
                className="text-2xl font-bold font-heading text-gray-900 mb-2"
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3 }}
              >
                {user.full_name || 'User'}
              </motion.h3>

              <motion.p
                className="text-gray-600 mb-6"
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.4 }}
              >
                {user.email}
              </motion.p>

              <div className="space-y-3">
                {/* OKDOI ID Display */}
                <motion.div
                  className="flex items-center justify-center text-sm font-semibold text-primary-blue bg-primary-blue/10 px-4 py-2"
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.45 }}
                >
                  <span className="text-xs mr-2">OKDOI ID:</span>
                  {user.user_id || 'N/A'}
                </motion.div>

                <motion.div
                  className="flex items-center justify-center text-sm text-gray-600 bg-gray-50 px-4 py-2"
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.5 }}
                >
                  <Calendar className="h-4 w-4 mr-2 text-primary-blue" />
                  Member since {user.created_at ? new Date(user.created_at).toLocaleDateString() : 'N/A'}
                </motion.div>

                {(user.role === 'admin' || user.is_super_admin) && (
                  <motion.div
                    className="inline-flex items-center px-3 py-2 text-sm font-bold bg-gradient-to-r from-blue-100 to-blue-200 text-blue-800"
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ delay: 0.6 }}
                  >
                    <Award className="h-4 w-4 mr-2" />
                    Admin Account
                  </motion.div>
                )}
              </div>
            </PremiumCard>
          </SlideInUp>

          {/* Profile Details */}
          <div className="lg:col-span-2">
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-6">Personal Information</h3>
              
              <div className="space-y-6">
                {/* Email (Read-only) */}
                <div className="flex items-start space-x-3">
                  <Mail className="h-5 w-5 text-gray-400 mt-2" />
                  <div className="flex-1">
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Email Address
                    </label>
                    <p className="text-gray-900 bg-gray-50 px-3 py-2 rounded-lg">{user.email}</p>
                    <p className="text-xs text-gray-500 mt-1">Email cannot be changed</p>
                  </div>
                </div>

                {/* Full Name */}
                <div className="flex items-start space-x-3">
                  <User className="h-5 w-5 text-gray-400 mt-2" />
                  <div className="flex-1">
                    {isEditing ? (
                      <Input
                        label="Full Name"
                        name="full_name"
                        value={formData.full_name}
                        onChange={handleChange}
                        placeholder="Enter your full name"
                        fullWidth
                      />
                    ) : (
                      <>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Full Name
                        </label>
                        <p className="text-gray-900">{user.full_name || 'Not provided'}</p>
                      </>
                    )}
                  </div>
                </div>

                {/* Phone */}
                <div className="flex items-start space-x-3">
                  <Phone className="h-5 w-5 text-gray-400 mt-2" />
                  <div className="flex-1">
                    {isEditing ? (
                      <Input
                        label="Phone Number"
                        name="phone"
                        value={formData.phone}
                        onChange={handleChange}
                        placeholder="Enter your phone number"
                        fullWidth
                      />
                    ) : (
                      <>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Phone Number
                        </label>
                        <p className="text-gray-900">{user.phone || 'Not provided'}</p>
                      </>
                    )}
                  </div>
                </div>

                {/* Location */}
                <div className="flex items-start space-x-3">
                  <MapPin className="h-5 w-5 text-gray-400 mt-2" />
                  <div className="flex-1">
                    {isEditing ? (
                      <Input
                        label="Location"
                        name="location"
                        value={formData.location}
                        onChange={handleChange}
                        placeholder="Enter your city/location"
                        fullWidth
                      />
                    ) : (
                      <>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Location
                        </label>
                        <p className="text-gray-900">{user.location || 'Not provided'}</p>
                      </>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
            </motion.div>
          )}

          {activeTab === 'verification' && (
            <motion.div
              key="verification"
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              transition={{ duration: 0.3 }}
              className="bg-gray-50 -mx-6 -mb-6 px-6 pb-6"
            >
              <NewKYCVerificationPage />
            </motion.div>
          )}

          {activeTab === 'inheritance' && (
            <motion.div
              key="inheritance"
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              transition={{ duration: 0.3 }}
              className="bg-gray-50 -mx-6 -mb-6 px-6 pb-6"
            >
              <InheritanceTab />
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </DashboardLayout>
  )
}
