-- Create wallet system tables

-- User wallets table
CREATE TABLE user_wallets (
    id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id uuid REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL UNIQUE,
    balance decimal(12,2) DEFAULT 0.00 NOT NULL CHECK (balance >= 0),
    currency varchar(3) DEFAULT 'LKR' NOT NULL,
    is_active boolean DEFAULT true,
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now()
);

-- Wallet transactions table
CREATE TABLE wallet_transactions (
    id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
    wallet_id uuid REFERENCES user_wallets(id) ON DELETE CASCADE NOT NULL,
    transaction_type varchar(20) NOT NULL CHECK (transaction_type IN ('deposit', 'withdrawal', 'transfer_in', 'transfer_out', 'purchase', 'refund', 'commission')),
    amount decimal(12,2) NOT NULL CHECK (amount > 0),
    currency varchar(3) DEFAULT 'LKR' NOT NULL,
    balance_before decimal(12,2) NOT NULL,
    balance_after decimal(12,2) NOT NULL,
    reference_id uuid, -- Reference to related entity (deposit request, transfer, etc.)
    reference_type varchar(50), -- Type of reference (deposit_request, p2p_transfer, purchase, etc.)
    description text,
    metadata jsonb DEFAULT '{}',
    status varchar(20) DEFAULT 'completed' CHECK (status IN ('pending', 'completed', 'failed', 'cancelled')),
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now()
);

-- P2P transfers table
CREATE TABLE p2p_transfers (
    id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
    sender_id uuid REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    receiver_id uuid REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    amount decimal(12,2) NOT NULL CHECK (amount > 0),
    currency varchar(3) DEFAULT 'LKR' NOT NULL,
    description text,
    status varchar(20) DEFAULT 'pending' CHECK (status IN ('pending', 'completed', 'failed', 'cancelled')),
    sender_transaction_id uuid REFERENCES wallet_transactions(id),
    receiver_transaction_id uuid REFERENCES wallet_transactions(id),
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    CONSTRAINT check_different_users CHECK (sender_id != receiver_id)
);

-- Deposit requests table
CREATE TABLE deposit_requests (
    id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id uuid REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    amount decimal(12,2) NOT NULL CHECK (amount > 0),
    currency varchar(3) DEFAULT 'LKR' NOT NULL,
    bank_name varchar(100) NOT NULL,
    account_holder_name varchar(100) NOT NULL,
    account_number varchar(50) NOT NULL,
    transaction_reference varchar(100), -- Bank transaction reference
    deposit_slip_url text, -- URL to uploaded deposit slip image
    notes text,
    status varchar(20) DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'rejected')),
    admin_notes text,
    approved_by uuid REFERENCES auth.users(id),
    approved_at timestamp with time zone,
    wallet_transaction_id uuid REFERENCES wallet_transactions(id),
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now()
);

-- Create indexes for better performance
CREATE INDEX idx_user_wallets_user_id ON user_wallets(user_id);
CREATE INDEX idx_wallet_transactions_wallet_id ON wallet_transactions(wallet_id);
CREATE INDEX idx_wallet_transactions_type ON wallet_transactions(transaction_type);
CREATE INDEX idx_wallet_transactions_status ON wallet_transactions(status);
CREATE INDEX idx_wallet_transactions_created_at ON wallet_transactions(created_at DESC);
CREATE INDEX idx_p2p_transfers_sender_id ON p2p_transfers(sender_id);
CREATE INDEX idx_p2p_transfers_receiver_id ON p2p_transfers(receiver_id);
CREATE INDEX idx_p2p_transfers_status ON p2p_transfers(status);
CREATE INDEX idx_deposit_requests_user_id ON deposit_requests(user_id);
CREATE INDEX idx_deposit_requests_status ON deposit_requests(status);
CREATE INDEX idx_deposit_requests_created_at ON deposit_requests(created_at DESC);

-- Create function to automatically create wallet for new users
CREATE OR REPLACE FUNCTION create_user_wallet()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO user_wallets (user_id, balance, currency)
    VALUES (NEW.id, 0.00, 'LKR');
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger to automatically create wallet when user is created
CREATE TRIGGER trigger_create_user_wallet
    AFTER INSERT ON users
    FOR EACH ROW
    EXECUTE FUNCTION create_user_wallet();

-- Create function to update wallet balance
CREATE OR REPLACE FUNCTION update_wallet_balance(
    p_wallet_id uuid,
    p_amount decimal(12,2),
    p_transaction_type varchar(20),
    p_description text DEFAULT NULL,
    p_reference_id uuid DEFAULT NULL,
    p_reference_type varchar(50) DEFAULT NULL,
    p_metadata jsonb DEFAULT '{}'
)
RETURNS uuid AS $$
DECLARE
    v_current_balance decimal(12,2);
    v_new_balance decimal(12,2);
    v_transaction_id uuid;
BEGIN
    -- Get current balance with row lock
    SELECT balance INTO v_current_balance
    FROM user_wallets
    WHERE id = p_wallet_id
    FOR UPDATE;

    IF NOT FOUND THEN
        RAISE EXCEPTION 'Wallet not found';
    END IF;

    -- Calculate new balance based on transaction type
    CASE p_transaction_type
        WHEN 'deposit', 'transfer_in', 'refund' THEN
            v_new_balance := v_current_balance + p_amount;
        WHEN 'withdrawal', 'transfer_out', 'purchase' THEN
            v_new_balance := v_current_balance - p_amount;
            IF v_new_balance < 0 THEN
                RAISE EXCEPTION 'Insufficient balance';
            END IF;
        ELSE
            RAISE EXCEPTION 'Invalid transaction type';
    END CASE;

    -- Update wallet balance
    UPDATE user_wallets
    SET balance = v_new_balance, updated_at = now()
    WHERE id = p_wallet_id;

    -- Create transaction record
    INSERT INTO wallet_transactions (
        wallet_id,
        transaction_type,
        amount,
        balance_before,
        balance_after,
        reference_id,
        reference_type,
        description,
        metadata,
        status
    ) VALUES (
        p_wallet_id,
        p_transaction_type,
        p_amount,
        v_current_balance,
        v_new_balance,
        p_reference_id,
        p_reference_type,
        p_description,
        p_metadata,
        'completed'
    ) RETURNING id INTO v_transaction_id;

    RETURN v_transaction_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function for P2P transfers
CREATE OR REPLACE FUNCTION process_p2p_transfer(
    p_sender_id uuid,
    p_receiver_id uuid,
    p_amount decimal(12,2),
    p_description text DEFAULT NULL
)
RETURNS uuid AS $$
DECLARE
    v_sender_wallet_id uuid;
    v_receiver_wallet_id uuid;
    v_transfer_id uuid;
    v_sender_transaction_id uuid;
    v_receiver_transaction_id uuid;
BEGIN
    -- Validate users are different
    IF p_sender_id = p_receiver_id THEN
        RAISE EXCEPTION 'Cannot transfer to yourself';
    END IF;

    -- Get wallet IDs
    SELECT id INTO v_sender_wallet_id FROM user_wallets WHERE user_id = p_sender_id;
    SELECT id INTO v_receiver_wallet_id FROM user_wallets WHERE user_id = p_receiver_id;

    IF v_sender_wallet_id IS NULL THEN
        RAISE EXCEPTION 'Sender wallet not found';
    END IF;

    IF v_receiver_wallet_id IS NULL THEN
        RAISE EXCEPTION 'Receiver wallet not found';
    END IF;

    -- Create transfer record
    INSERT INTO p2p_transfers (sender_id, receiver_id, amount, description, status)
    VALUES (p_sender_id, p_receiver_id, p_amount, p_description, 'pending')
    RETURNING id INTO v_transfer_id;

    -- Process sender transaction (debit)
    SELECT update_wallet_balance(
        v_sender_wallet_id,
        p_amount,
        'transfer_out',
        COALESCE(p_description, 'P2P Transfer to user'),
        v_transfer_id,
        'p2p_transfer'
    ) INTO v_sender_transaction_id;

    -- Process receiver transaction (credit)
    SELECT update_wallet_balance(
        v_receiver_wallet_id,
        p_amount,
        'transfer_in',
        COALESCE(p_description, 'P2P Transfer from user'),
        v_transfer_id,
        'p2p_transfer'
    ) INTO v_receiver_transaction_id;

    -- Update transfer record with transaction IDs and mark as completed
    UPDATE p2p_transfers
    SET 
        sender_transaction_id = v_sender_transaction_id,
        receiver_transaction_id = v_receiver_transaction_id,
        status = 'completed',
        updated_at = now()
    WHERE id = v_transfer_id;

    RETURN v_transfer_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- RLS Policies

-- User wallets policies
ALTER TABLE user_wallets ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view own wallet" ON user_wallets
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can update own wallet" ON user_wallets
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "System can insert wallets" ON user_wallets
    FOR INSERT WITH CHECK (true);

CREATE POLICY "Admins can view all wallets" ON user_wallets
    FOR ALL USING (is_admin(auth.uid()));

-- Wallet transactions policies
ALTER TABLE wallet_transactions ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view own transactions" ON wallet_transactions
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM user_wallets 
            WHERE user_wallets.id = wallet_transactions.wallet_id 
            AND user_wallets.user_id = auth.uid()
        )
    );

CREATE POLICY "System can insert transactions" ON wallet_transactions
    FOR INSERT WITH CHECK (true);

CREATE POLICY "Admins can view all transactions" ON wallet_transactions
    FOR ALL USING (is_admin(auth.uid()));

-- P2P transfers policies
ALTER TABLE p2p_transfers ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view own transfers" ON p2p_transfers
    FOR SELECT USING (auth.uid() = sender_id OR auth.uid() = receiver_id);

CREATE POLICY "Users can create transfers" ON p2p_transfers
    FOR INSERT WITH CHECK (auth.uid() = sender_id);

CREATE POLICY "Admins can view all transfers" ON p2p_transfers
    FOR ALL USING (is_admin(auth.uid()));

-- Deposit requests policies
ALTER TABLE deposit_requests ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view own deposit requests" ON deposit_requests
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can create deposit requests" ON deposit_requests
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own pending requests" ON deposit_requests
    FOR UPDATE USING (auth.uid() = user_id AND status = 'pending');

CREATE POLICY "Admins can view all deposit requests" ON deposit_requests
    FOR ALL USING (is_admin(auth.uid()));

-- Update TABLES constant in the application
-- Add these to src/lib/supabase.ts:
-- USER_WALLETS: 'user_wallets',
-- WALLET_TRANSACTIONS: 'wallet_transactions',
-- P2P_TRANSFERS: 'p2p_transfers',
-- DEPOSIT_REQUESTS: 'deposit_requests',
