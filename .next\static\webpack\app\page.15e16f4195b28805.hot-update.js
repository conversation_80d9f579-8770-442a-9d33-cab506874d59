"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/contexts/AuthContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/AuthContext.tsx ***!
  \**************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: function() { return /* binding */ AuthProvider; },\n/* harmony export */   useAuth: function() { return /* binding */ useAuth; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/auth */ \"(app-pages-browser)/./src/lib/auth.ts\");\n/* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction AuthProvider(param) {\n    let { children } = param;\n    _s();\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [initialized, setInitialized] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        let isMounted = true;\n        // Get initial session with better error handling\n        const getInitialSession = async ()=>{\n            try {\n                console.log(\"AuthProvider: Getting initial session...\");\n                // First check if we have a valid session\n                const session = await _lib_auth__WEBPACK_IMPORTED_MODULE_2__.AuthService.getSession();\n                if ((session === null || session === void 0 ? void 0 : session.user) && isMounted) {\n                    console.log(\"AuthProvider: Found existing session for user:\", session.user.email);\n                    const currentUser = await _lib_auth__WEBPACK_IMPORTED_MODULE_2__.AuthService.getCurrentUser();\n                    if (isMounted) {\n                        setUser(currentUser);\n                        console.log(\"AuthProvider: User set successfully:\", currentUser === null || currentUser === void 0 ? void 0 : currentUser.email);\n                    }\n                } else {\n                    console.log(\"AuthProvider: No existing session found\");\n                    if (isMounted) {\n                        setUser(null);\n                    }\n                }\n            } catch (error) {\n                console.error(\"AuthProvider: Error getting initial session:\", error);\n                if (isMounted) {\n                    setUser(null);\n                }\n            } finally{\n                if (isMounted) {\n                    setLoading(false);\n                    setInitialized(true);\n                    console.log(\"AuthProvider: Initialization complete\");\n                }\n            }\n        };\n        getInitialSession();\n        // Listen for auth changes with improved handling\n        const { data: { subscription } } = _lib_auth__WEBPACK_IMPORTED_MODULE_2__.AuthService.onAuthStateChange(async (event, session)=>{\n            var _session_user;\n            console.log(\"AuthProvider: Auth state change:\", event, session === null || session === void 0 ? void 0 : (_session_user = session.user) === null || _session_user === void 0 ? void 0 : _session_user.email);\n            if (!isMounted) return;\n            try {\n                if (event === \"SIGNED_IN\" && (session === null || session === void 0 ? void 0 : session.user)) {\n                    console.log(\"AuthProvider: User signed in, fetching profile...\");\n                    const currentUser = await _lib_auth__WEBPACK_IMPORTED_MODULE_2__.AuthService.getCurrentUser();\n                    if (isMounted) {\n                        setUser(currentUser);\n                        console.log(\"AuthProvider: User profile loaded:\", currentUser === null || currentUser === void 0 ? void 0 : currentUser.email);\n                    }\n                } else if (event === \"SIGNED_OUT\") {\n                    console.log(\"AuthProvider: User signed out\");\n                    if (isMounted) {\n                        setUser(null);\n                    }\n                } else if (event === \"TOKEN_REFRESHED\" && (session === null || session === void 0 ? void 0 : session.user)) {\n                    console.log(\"AuthProvider: Token refreshed, updating user...\");\n                    const currentUser = await _lib_auth__WEBPACK_IMPORTED_MODULE_2__.AuthService.getCurrentUser();\n                    if (isMounted) {\n                        setUser(currentUser);\n                    }\n                }\n            } catch (error) {\n                console.error(\"AuthProvider: Error handling auth state change:\", error);\n                if (isMounted) {\n                    setUser(null);\n                }\n            } finally{\n                if (isMounted && !initialized) {\n                    setLoading(false);\n                    setInitialized(true);\n                }\n            }\n        });\n        return ()=>{\n            isMounted = false;\n            subscription.unsubscribe();\n        };\n    }, []);\n    const signIn = async (email, password)=>{\n        setLoading(true);\n        try {\n            await _lib_auth__WEBPACK_IMPORTED_MODULE_2__.AuthService.signIn(email, password);\n        // User state will be updated by the auth state change listener\n        } catch (error) {\n            setLoading(false);\n            throw error;\n        }\n    };\n    const signUp = async (email, password, userData)=>{\n        setLoading(true);\n        try {\n            const result = await _lib_auth__WEBPACK_IMPORTED_MODULE_2__.AuthService.signUp(email, password, userData);\n            // Keep loading state active during email verification flow\n            // Loading will be managed by the signup form component\n            return {\n                requireEmailVerification: result.requireEmailVerification,\n                referrer: result.referrer\n            };\n        } catch (error) {\n            setLoading(false);\n            throw error;\n        }\n    };\n    const verifyEmailOtp = async (email, token)=>{\n        setLoading(true);\n        try {\n            await _lib_auth__WEBPACK_IMPORTED_MODULE_2__.AuthService.verifyEmailOtp(email, token);\n        // User state will be updated by the auth state change listener\n        } catch (error) {\n            setLoading(false);\n            throw error;\n        }\n    };\n    const resendEmailOtp = async (email)=>{\n        try {\n            await _lib_auth__WEBPACK_IMPORTED_MODULE_2__.AuthService.resendEmailOtp(email);\n        } catch (error) {\n            throw error;\n        }\n    };\n    const signOut = async ()=>{\n        setLoading(true);\n        try {\n            await _lib_auth__WEBPACK_IMPORTED_MODULE_2__.AuthService.signOut();\n        // User state will be updated by the auth state change listener\n        } catch (error) {\n            setLoading(false);\n            throw error;\n        }\n    };\n    const updateProfile = async (updates)=>{\n        if (!user) throw new Error(\"No user logged in\");\n        try {\n            const updatedUser = await _lib_auth__WEBPACK_IMPORTED_MODULE_2__.AuthService.updateProfile(user.id, updates);\n            setUser(updatedUser);\n        } catch (error) {\n            throw error;\n        }\n    };\n    const value = {\n        user,\n        loading,\n        signIn,\n        signUp,\n        signOut,\n        updateProfile,\n        verifyEmailOtp,\n        resendEmailOtp\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\okdoi\\\\src\\\\contexts\\\\AuthContext.tsx\",\n        lineNumber: 191,\n        columnNumber: 5\n    }, this);\n}\n_s(AuthProvider, \"FKjX8Ko0uy7WJEUSeSk+H5pWLtA=\");\n_c = AuthProvider;\nfunction useAuth() {\n    _s1();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error(\"useAuth must be used within an AuthProvider\");\n    }\n    return context;\n}\n_s1(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/contexts/AuthContext.tsx\n"));

/***/ })

});