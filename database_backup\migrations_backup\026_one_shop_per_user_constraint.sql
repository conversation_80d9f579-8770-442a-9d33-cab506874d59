-- Add constraint to ensure one shop per user
-- This migration adds a unique constraint on user_id in vendor_shops table

-- First, check if there are any users with multiple shops and handle them
-- We'll keep the first shop (oldest) and mark others as rejected
WITH duplicate_shops AS (
    SELECT 
        user_id,
        array_agg(id ORDER BY created_at) as shop_ids,
        count(*) as shop_count
    FROM vendor_shops 
    GROUP BY user_id 
    HAVING count(*) > 1
),
shops_to_reject AS (
    SELECT 
        unnest(shop_ids[2:]) as shop_id
    FROM duplicate_shops
)
UPDATE vendor_shops 
SET 
    status = 'rejected',
    updated_at = NOW()
WHERE id IN (SELECT shop_id FROM shops_to_reject);

-- Add unique constraint on user_id to prevent multiple shops per user
ALTER TABLE vendor_shops 
ADD CONSTRAINT vendor_shops_user_id_unique UNIQUE (user_id);

-- Add comment to document the constraint
COMMENT ON CONSTRAINT vendor_shops_user_id_unique ON vendor_shops 
IS 'Ensures each user can only have one vendor shop';
