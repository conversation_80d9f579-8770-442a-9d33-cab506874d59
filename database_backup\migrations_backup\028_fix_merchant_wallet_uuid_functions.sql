-- Fix merchant wallet UUID generation functions
-- Replace uuid_generate_v4() with gen_random_uuid() for consistency

-- Drop existing tables if they exist (to recreate with correct UUID function)
DROP TABLE IF EXISTS merchant_to_main_transfers CASCADE;
DROP TABLE IF EXISTS merchant_wallet_transactions CASCADE;
DROP TABLE IF EXISTS merchant_wallets CASCADE;

-- Recreate merchant_wallets table with correct UUID function
CREATE TABLE merchant_wallets (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    shop_id UUID NOT NULL REFERENCES vendor_shops(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    balance DECIMAL(12,2) DEFAULT 0.00 NOT NULL CHECK (balance >= 0),
    total_earned DECIMAL(12,2) DEFAULT 0.00 NOT NULL,
    total_withdrawn DECIMAL(12,2) DEFAULT 0.00 NOT NULL,
    total_views INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(shop_id),
    UNIQUE(user_id, shop_id)
);

-- Recreate merchant_wallet_transactions table
CREATE TABLE merchant_wallet_transactions (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    merchant_wallet_id UUID NOT NULL REFERENCES merchant_wallets(id) ON DELETE CASCADE,
    transaction_id VARCHAR(50) NOT NULL UNIQUE,
    type VARCHAR(20) NOT NULL CHECK (type IN ('credit', 'debit')),
    category VARCHAR(30) NOT NULL CHECK (category IN ('order_payment', 'transfer_to_main', 'commission_deduction', 'refund', 'adjustment')),
    amount DECIMAL(12,2) NOT NULL CHECK (amount > 0),
    balance_before DECIMAL(12,2) NOT NULL,
    balance_after DECIMAL(12,2) NOT NULL,
    description TEXT,
    reference_id UUID, -- Can reference order_id, transfer_id, etc.
    reference_type VARCHAR(20), -- 'order', 'transfer', 'refund', etc.
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID REFERENCES users(id)
);

-- Recreate merchant_to_main_transfers table
CREATE TABLE merchant_to_main_transfers (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    merchant_wallet_id UUID NOT NULL REFERENCES merchant_wallets(id) ON DELETE CASCADE,
    user_wallet_id UUID NOT NULL REFERENCES user_wallets(id) ON DELETE CASCADE,
    transfer_id VARCHAR(50) NOT NULL UNIQUE,
    amount DECIMAL(12,2) NOT NULL CHECK (amount > 0),
    status VARCHAR(20) DEFAULT 'completed' CHECK (status IN ('pending', 'completed', 'failed', 'cancelled')),
    description TEXT DEFAULT 'Transfer from merchant wallet to main wallet',
    merchant_transaction_id UUID REFERENCES merchant_wallet_transactions(id),
    main_transaction_id UUID REFERENCES wallet_transactions(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    completed_at TIMESTAMP WITH TIME ZONE,
    failed_reason TEXT
);

-- Recreate indexes
CREATE INDEX idx_merchant_wallets_shop_id ON merchant_wallets(shop_id);
CREATE INDEX idx_merchant_wallets_user_id ON merchant_wallets(user_id);
CREATE INDEX idx_merchant_wallet_transactions_wallet_id ON merchant_wallet_transactions(merchant_wallet_id);
CREATE INDEX idx_merchant_wallet_transactions_type ON merchant_wallet_transactions(type);
CREATE INDEX idx_merchant_wallet_transactions_category ON merchant_wallet_transactions(category);
CREATE INDEX idx_merchant_wallet_transactions_created_at ON merchant_wallet_transactions(created_at DESC);
CREATE INDEX idx_merchant_to_main_transfers_merchant_wallet_id ON merchant_to_main_transfers(merchant_wallet_id);
CREATE INDEX idx_merchant_to_main_transfers_status ON merchant_to_main_transfers(status);

-- Enable Row Level Security
ALTER TABLE merchant_wallets ENABLE ROW LEVEL SECURITY;
ALTER TABLE merchant_wallet_transactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE merchant_to_main_transfers ENABLE ROW LEVEL SECURITY;

-- Recreate RLS Policies
CREATE POLICY "Shop owners can view their merchant wallet" ON merchant_wallets
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Shop owners can update their merchant wallet" ON merchant_wallets
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Shop owners can view their merchant wallet transactions" ON merchant_wallet_transactions
    FOR SELECT USING (
        merchant_wallet_id IN (
            SELECT id FROM merchant_wallets WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Shop owners can view their transfers" ON merchant_to_main_transfers
    FOR SELECT USING (
        merchant_wallet_id IN (
            SELECT id FROM merchant_wallets WHERE user_id = auth.uid()
        )
    );

-- Recreate merchant wallets for existing approved shops
INSERT INTO merchant_wallets (shop_id, user_id)
SELECT id, user_id
FROM vendor_shops
WHERE status = 'approved'
ON CONFLICT (shop_id) DO NOTHING;
