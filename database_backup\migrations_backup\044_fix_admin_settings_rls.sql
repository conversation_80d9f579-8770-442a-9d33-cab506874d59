-- Fix admin_settings RLS policies to allow public read access for essential settings
-- This fixes the 406 Not Acceptable error during user signup

-- Drop existing restrictive policies
DROP POLICY IF EXISTS "Admin users can read admin settings" ON admin_settings;
DROP POLICY IF EXISTS "admin_settings_select_policy" ON admin_settings;
DROP POLICY IF EXISTS "Admin users can insert admin settings" ON admin_settings;
DROP POLICY IF EXISTS "admin_settings_insert_policy" ON admin_settings;
DROP POLICY IF EXISTS "Admin users can update admin settings" ON admin_settings;
DROP POLICY IF EXISTS "admin_settings_update_policy" ON admin_settings;
DROP POLICY IF EXISTS "Admin users can delete admin settings" ON admin_settings;
DROP POLICY IF EXISTS "admin_settings_delete_policy" ON admin_settings;

-- Create new policies that allow public read access but restrict write access to admins
-- Allow anyone to read admin settings (needed for signup process)
CREATE POLICY "Public can read admin settings" ON admin_settings
    FOR SELECT USING (true);

-- Only admins can insert new settings
CREATE POLICY "Admin users can insert admin settings" ON admin_settings
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM users 
            WHERE users.id = auth.uid() 
            AND (users.role = 'admin' OR users.is_super_admin = true)
        )
    );

-- Only admins can update existing settings
CREATE POLICY "Admin users can update admin settings" ON admin_settings
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM users 
            WHERE users.id = auth.uid() 
            AND (users.role = 'admin' OR users.is_super_admin = true)
        )
    );

-- Only admins can delete settings
CREATE POLICY "Admin users can delete admin settings" ON admin_settings
    FOR DELETE USING (
        EXISTS (
            SELECT 1 FROM users 
            WHERE users.id = auth.uid() 
            AND (users.role = 'admin' OR users.is_super_admin = true)
        )
    );

-- Ensure the table has RLS enabled
ALTER TABLE admin_settings ENABLE ROW LEVEL SECURITY;

-- Verify essential settings exist for signup process
INSERT INTO admin_settings (key, value, description) VALUES
('require_email_verification', 'false', 'Whether email verification is required for new users'),
('allow_registration', 'true', 'Whether new user registration is allowed'),
('site_name', '"OKDOI"', 'Name of the website'),
('free_ads_limit', '2', 'Number of free ads new users can post without a subscription')
ON CONFLICT (key) DO NOTHING;
