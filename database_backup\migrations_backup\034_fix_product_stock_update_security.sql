-- Fix Product Stock Update Security
-- This migration fixes the issue where product stock quantities were not being reduced after sales
-- due to Row Level Security (RLS) policies preventing customers from updating shop products.

-- =====================================================
-- 1. UPDATE STOCK UPDATE FUNCTION WITH SECURITY DEFINER
-- =====================================================

-- Drop and recreate the function with SECURITY DEFINER to bypass RLS
CREATE OR REPLACE FUNCTION public.update_product_stock(
    p_product_id uuid, 
    p_quantity_sold integer
) 
RETURNS void 
LANGUAGE plpgsql 
SECURITY DEFINER -- This allows the function to run with elevated privileges
AS $function$
BEGIN
    -- Update stock quantity, ensuring it doesn't go below 0
    UPDATE shop_products 
    SET 
        stock_quantity = GREATEST(stock_quantity - p_quantity_sold, 0),
        updated_at = now()
    WHERE id = p_product_id;
    
    -- Log the stock update for debugging
    INSERT INTO public.stock_update_log (
        product_id,
        quantity_changed,
        operation_type,
        created_at
    ) VALUES (
        p_product_id,
        p_quantity_sold,
        CASE 
            WHEN p_quantity_sold > 0 THEN 'sale'
            WHEN p_quantity_sold < 0 THEN 'restock'
            ELSE 'adjustment'
        END,
        now()
    );
    
EXCEPTION
    WHEN OTHERS THEN
        -- Log error but don't fail the transaction
        RAISE WARNING 'Failed to update stock for product %: %', p_product_id, SQLERRM;
        -- Re-raise the exception to fail the transaction
        RAISE;
END;
$function$;

-- =====================================================
-- 2. CREATE STOCK UPDATE LOG TABLE
-- =====================================================

-- Create table to log stock updates for debugging and audit purposes
CREATE TABLE IF NOT EXISTS public.stock_update_log (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    product_id UUID NOT NULL REFERENCES shop_products(id) ON DELETE CASCADE,
    quantity_changed INTEGER NOT NULL,
    operation_type VARCHAR(20) NOT NULL CHECK (operation_type IN ('sale', 'restock', 'adjustment')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create index for better performance
CREATE INDEX IF NOT EXISTS idx_stock_update_log_product_id ON stock_update_log(product_id);
CREATE INDEX IF NOT EXISTS idx_stock_update_log_created_at ON stock_update_log(created_at);

-- =====================================================
-- 3. ENABLE RLS ON STOCK UPDATE LOG
-- =====================================================

ALTER TABLE stock_update_log ENABLE ROW LEVEL SECURITY;

-- Allow shop owners to view their product stock logs
CREATE POLICY "Shop owners can view their product stock logs" ON stock_update_log
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM shop_products sp
            JOIN vendor_shops vs ON vs.id = sp.shop_id
            WHERE sp.id = stock_update_log.product_id 
            AND vs.user_id = auth.uid()
        )
    );

-- Allow admins to view all stock logs
CREATE POLICY "Admins can view all stock logs" ON stock_update_log
    FOR SELECT USING (
        EXISTS (SELECT 1 FROM users WHERE id = auth.uid() AND role = 'admin')
    );

-- =====================================================
-- 4. GRANT NECESSARY PERMISSIONS
-- =====================================================

-- Grant execute permission on the function to authenticated users
GRANT EXECUTE ON FUNCTION public.update_product_stock(uuid, integer) TO authenticated;

-- =====================================================
-- 5. ADD COMMENTS FOR DOCUMENTATION
-- =====================================================

COMMENT ON FUNCTION public.update_product_stock(uuid, integer) IS 
'Updates product stock quantity after sales or restocking. Uses SECURITY DEFINER to bypass RLS policies. Negative quantities restore stock (for cancellations/returns).';

COMMENT ON TABLE public.stock_update_log IS 
'Audit log for all product stock quantity changes including sales, restocks, and manual adjustments.';
