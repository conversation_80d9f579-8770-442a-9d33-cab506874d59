'use client'

import React, { useState, useEffect } from 'react'
import { 
  Banknote, 
  ChevronDown, 
  ChevronUp, 
  Calendar,
  Package,
  TrendingUp,
  Users,
  Crown,
  Gift,
  Ticket,
  PartyPopper,
  Car,
  Phone,
  Home,
  CreditCard,
  Star,
  Award,
  Target,
  Zap
} from 'lucide-react'
import { formatCurrency } from '@/lib/utils'
import { CommissionSystemService } from '@/lib/services/commissionSystem'

interface CommissionTransaction {
  id: string
  transaction_id: string
  user_id: string
  beneficiary_id: string
  subscription_purchase_id?: string
  commission_type: string
  commission_level: number
  package_value: number
  commission_rate: number
  commission_amount: number
  currency: string
  status: 'pending' | 'processed' | 'failed' | 'cancelled'
  processed_at?: string
  wallet_transaction_id?: string
  metadata: Record<string, any>
  created_at: string
  updated_at: string
}

interface GroupedCommissionTransaction {
  subscription_purchase_id: string
  package_value: number
  total_amount: number
  transaction_count: number
  created_at: string
  transactions: CommissionTransaction[]
}

interface CommissionTransactionsTabProps {
  userId: string
  transactions: any[] // Wallet transactions
}

const getCommissionIcon = (commissionType: string) => {
  const iconMap: Record<string, React.ComponentType<any>> = {
    'direct': TrendingUp,
    'level_commission': Users,
    'voucher': Ticket,
    'festival_bonus': PartyPopper,
    'saving': Banknote,
    'gift_center': Gift,
    'entertainment': Star,
    'medical': Award,
    'education': Target,
    'credit': CreditCard,
    'zm_specific_commissions': Crown,
    'zm_petral_allowance': Car,
    'zm_leasing_facility': Home,
    'zm_phone_bill': Phone,
    'rsm_specific_commissions': Crown,
    'rsm_petral_allowance': Car,
    'rsm_leasing_facility': Home,
    'rsm_phone_bill': Phone,
    'okdoi_head_universal': Crown,
    'present_user': Gift,
    'annual_present_user': Gift,
    'present_leader': Crown,
    'annual_present_leader': Crown
  }
  
  const IconComponent = iconMap[commissionType] || Banknote
  return <IconComponent className="h-4 w-4 text-blue-500" />
}

const getCommissionTypeLabel = (commissionType: string) => {
  const labelMap: Record<string, string> = {
    'direct': 'Direct Commission',
    'level_commission': 'Level Commission',
    'voucher': 'Voucher Commission',
    'festival_bonus': 'Festival Bonus',
    'saving': 'Saving Commission',
    'gift_center': 'Gift Center',
    'entertainment': 'Entertainment',
    'medical': 'Medical',
    'education': 'Education',
    'credit': 'Credit',
    'zm_specific_commissions': 'ZM Bonus',
    'zm_petral_allowance': 'ZM Petrol Allowance',
    'zm_leasing_facility': 'ZM Leasing Facility',
    'zm_phone_bill': 'ZM Phone Bill',
    'rsm_specific_commissions': 'RSM Bonus',
    'rsm_petral_allowance': 'RSM Petrol Allowance',
    'rsm_leasing_facility': 'RSM Leasing Facility',
    'rsm_phone_bill': 'RSM Phone Bill',
    'okdoi_head_universal': 'OKDOI Head Commission',
    'present_user': 'Present User (Gift)',
    'annual_present_user': 'Annual Present User (Gift)',
    'present_leader': 'Present Leader (Gift)',
    'annual_present_leader': 'Annual Present Leader (Gift)'
  }
  
  return labelMap[commissionType] || commissionType.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
}

export default function CommissionTransactionsTab({ userId, transactions }: CommissionTransactionsTabProps) {
  const [groupedTransactions, setGroupedTransactions] = useState<GroupedCommissionTransaction[]>([])
  const [expandedGroups, setExpandedGroups] = useState<Set<string>>(new Set())
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchCommissionTransactions()
  }, [userId])

  const fetchCommissionTransactions = async () => {
    try {
      setLoading(true)
      // Fetch all commission transactions (increase limit to get all)
      const { transactions: commissionTransactions } = await CommissionSystemService.getUserCommissionTransactions(
        userId,
        {}, // no filters
        1, // page 1
        1000 // high limit to get all transactions
      )

      console.log('Fetched commission transactions:', commissionTransactions)

      // Group transactions by subscription_purchase_id
      const grouped = commissionTransactions.reduce((acc, transaction) => {
        const key = transaction.subscription_purchase_id || 'no_subscription'

        if (!acc[key]) {
          acc[key] = {
            subscription_purchase_id: key,
            package_value: transaction.package_value || 0,
            total_amount: 0,
            transaction_count: 0,
            created_at: transaction.created_at,
            transactions: []
          }
        }

        acc[key].total_amount += parseFloat(transaction.commission_amount.toString())
        acc[key].transaction_count += 1
        acc[key].transactions.push(transaction)

        // Use the earliest transaction date
        if (new Date(transaction.created_at) < new Date(acc[key].created_at)) {
          acc[key].created_at = transaction.created_at
        }

        return acc
      }, {} as Record<string, GroupedCommissionTransaction>)

      console.log('Grouped commission transactions:', grouped)

      // Convert to array and sort by date (newest first)
      const groupedArray = Object.values(grouped).sort((a, b) =>
        new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
      )

      setGroupedTransactions(groupedArray)
    } catch (error) {
      console.error('Error fetching commission transactions:', error)
    } finally {
      setLoading(false)
    }
  }

  const toggleGroupExpansion = (subscriptionId: string) => {
    const newExpanded = new Set(expandedGroups)
    if (newExpanded.has(subscriptionId)) {
      newExpanded.delete(subscriptionId)
    } else {
      newExpanded.add(subscriptionId)
    }
    setExpandedGroups(newExpanded)
  }

  if (loading) {
    return (
      <div className="p-6">
        <div className="flex items-center justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      </div>
    )
  }

  return (
    <div className="p-6">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">Commission Transactions</h3>
      <div className="space-y-4">
        {groupedTransactions.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            <Banknote className="h-12 w-12 text-gray-300 mx-auto mb-4" />
            <p>No commission transactions found</p>
            <p className="text-sm mt-2">Commissions will appear here when you earn them from referrals or other activities.</p>
          </div>
        ) : (
          groupedTransactions.map((group) => (
            <div key={group.subscription_purchase_id} className="border border-gray-200 rounded-lg overflow-hidden">
              {/* Group Header */}
              <div 
                className="bg-gray-50 p-4 cursor-pointer hover:bg-gray-100 transition-colors"
                onClick={() => toggleGroupExpansion(group.subscription_purchase_id)}
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                      <Package className="h-5 w-5 text-blue-600" />
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-900">
                        Commission from Rs {formatCurrency(group.package_value)} Package
                      </p>
                      <p className="text-xs text-gray-500">
                        {group.transaction_count} commission types • {new Date(group.created_at).toLocaleDateString()}
                      </p>
                      <p className="text-xs text-gray-400">
                        Purchase ID: {group.subscription_purchase_id.slice(-8)}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-3">
                    <div className="text-right">
                      <p className="text-sm font-medium text-green-600">
                        +{formatCurrency(group.total_amount)}
                      </p>
                    </div>
                    {expandedGroups.has(group.subscription_purchase_id) ? (
                      <ChevronUp className="h-5 w-5 text-gray-400" />
                    ) : (
                      <ChevronDown className="h-5 w-5 text-gray-400" />
                    )}
                  </div>
                </div>
              </div>

              {/* Expanded Commission Details */}
              {expandedGroups.has(group.subscription_purchase_id) && (
                <div className="bg-white">
                  {group.transactions.map((transaction) => (
                    <div key={transaction.id} className="flex items-center justify-between py-3 px-4 border-b border-gray-100 last:border-b-0">
                      <div className="flex items-center space-x-3">
                        {getCommissionIcon(transaction.commission_type)}
                        <div>
                          <p className="text-sm font-medium text-gray-900">
                            {getCommissionTypeLabel(transaction.commission_type)}
                          </p>
                          <p className="text-xs text-gray-500">
                            Level {transaction.commission_level} • {new Date(transaction.created_at).toLocaleString()}
                          </p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="text-sm font-medium text-green-600">
                          +{formatCurrency(transaction.commission_amount)}
                        </p>
                        <p className="text-xs text-gray-500 capitalize">
                          {transaction.status}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          ))
        )}
      </div>
    </div>
  )
}
