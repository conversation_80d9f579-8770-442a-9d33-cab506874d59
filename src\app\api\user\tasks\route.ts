import { NextRequest, NextResponse } from 'next/server'
import { GiftTasksService } from '@/lib/services/giftTasksService'
import { cookies } from 'next/headers'
import { createServerClient } from '@supabase/ssr'

export async function GET(request: NextRequest) {
  try {
    // Create Supabase client for auth using SSR
    const cookieStore = cookies()
    const supabaseAuth = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value
          },
        },
      }
    )

    // Get the current user
    const { data: { user }, error: authError } = await supabaseAuth.auth.getUser()

    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const userId = user.id
    const { searchParams } = new URL(request.url)
    const status = searchParams.get('status')

    const tasks = await GiftTasksService.getUserTasks(userId, status || undefined)

    return NextResponse.json({
      success: true,
      data: tasks
    })
  } catch (error) {
    console.error('GET /api/user/tasks error:', error)
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to fetch user tasks'
      },
      { status: 500 }
    )
  }
}