import { supabase, TABLES } from '@/lib/supabase'
import { SubscriptionPackage, UserSubscription, AdBoost, SubscriptionUsage } from '@/types'
import { AdminSettingsService } from './adminSettings'

export class SubscriptionService {
  /**
   * Get all active subscription packages
   */
  static async getPackages(): Promise<SubscriptionPackage[]> {
    try {
      const { data, error } = await supabase
        .from('subscription_packages')
        .select('*')
        .eq('is_active', true)
        .order('sort_order', { ascending: true })

      if (error) {
        console.error('Error fetching packages:', error)
        throw new Error(`Failed to fetch subscription packages: ${error.message}`)
      }

      return data || []
    } catch (error) {
      console.error('Error in getPackages:', error)
      // Return empty array if table doesn't exist yet
      if (error instanceof Error && error.message.includes('relation "subscription_packages" does not exist')) {
        return []
      }
      throw error
    }
  }

  /**
   * Get user's current active subscription
   */
  static async getUserActiveSubscription(userId: string): Promise<UserSubscription | null> {
    try {
      // First, try to get subscriptions without .single() to avoid 406 errors
      const { data, error } = await supabase
        .from('user_subscriptions')
        .select(`
          *,
          package:subscription_packages(*)
        `)
        .eq('user_id', userId)
        .eq('status', 'active')
        .gt('expires_at', new Date().toISOString())
        .order('expires_at', { ascending: false })
        .limit(1)

      if (error) {
        console.error('Error fetching user subscription:', error)
        throw new Error(`Failed to fetch user subscription: ${error.message}`)
      }

      // Return the first result or null if no results
      return data && data.length > 0 ? data[0] : null
    } catch (error) {
      console.error('Error in getUserActiveSubscription:', error)
      // Return null if table doesn't exist yet or any other error
      if (error instanceof Error && (
        error.message.includes('relation "user_subscriptions" does not exist') ||
        error.message.includes('406') ||
        error.message.includes('Not Acceptable')
      )) {
        return null
      }
      // For other errors, still return null to prevent dashboard crashes
      return null
    }
  }

  /**
   * Get user's subscription usage statistics
   */
  static async getUserSubscriptionUsage(userId: string): Promise<SubscriptionUsage> {
    try {
      const subscription = await this.getUserActiveSubscription(userId)

      if (!subscription) {
        return {
          ads_remaining: 0,
          boosts_remaining: 0,
          ads_used: 0,
          boosts_used: 0,
          subscription_expires_at: null,
          has_active_subscription: false
        }
      }

      const package_data = subscription.package as SubscriptionPackage

      return {
        ads_remaining: Math.max(0, package_data.ad_limit - subscription.ads_used),
        boosts_remaining: Math.max(0, package_data.boost_limit - subscription.boosts_used),
        ads_used: subscription.ads_used,
        boosts_used: subscription.boosts_used,
        subscription_expires_at: subscription.expires_at,
        has_active_subscription: true
      }
    } catch (error) {
      console.error('Error in getUserSubscriptionUsage:', error)
      // Return default values if tables don't exist
      return {
        ads_remaining: 0,
        boosts_remaining: 0,
        ads_used: 0,
        boosts_used: 0,
        subscription_expires_at: null,
        has_active_subscription: false
      }
    }
  }

  /**
   * Purchase a subscription package
   */
  static async purchaseSubscription(
    userId: string,
    packageId: string,
    walletId: string
  ): Promise<UserSubscription> {
    try {
      // Get the current user's session token
      const { data: { session } } = await supabase.auth.getSession()

      if (!session?.access_token) {
        throw new Error('Authentication required')
      }

      // Call the server-side API endpoint for subscription purchase
      // This ensures commission distribution happens on the server with admin privileges
      const response = await fetch('/api/subscription/purchase', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${session.access_token}`
        },
        body: JSON.stringify({
          packageId,
          walletId
        })
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || 'Failed to purchase subscription')
      }

      if (!result.success) {
        throw new Error(result.error || 'Subscription purchase failed')
      }

      console.log(`Subscription purchased successfully: ${result.data.id}`)
      return result.data
    } catch (error) {
      console.error('Error in purchaseSubscription:', error)
      throw error
    }
  }

  /**
   * Get free ads usage for a user
   */
  static async getFreeAdsUsage(userId: string): Promise<{ freeAdsUsed: number; freeAdsLimit: number; canUseFreeAds: boolean }> {
    try {
      // Get free ads limit from admin settings
      const freeAdsLimit = await AdminSettingsService.getSetting('free_ads_limit') || 2

      // Count ads posted by user without active subscription
      const { count: freeAdsUsed, error } = await supabase
        .from(TABLES.ADS)
        .select('*', { count: 'exact', head: true })
        .eq('user_id', userId)

      if (error) {
        console.error('Error counting free ads:', error)
        return { freeAdsUsed: 0, freeAdsLimit, canUseFreeAds: true }
      }

      const canUseFreeAds = (freeAdsUsed || 0) < freeAdsLimit

      return {
        freeAdsUsed: freeAdsUsed || 0,
        freeAdsLimit,
        canUseFreeAds
      }
    } catch (error) {
      console.error('Error getting free ads usage:', error)
      return { freeAdsUsed: 0, freeAdsLimit: 2, canUseFreeAds: true }
    }
  }

  /**
   * Check if user can post an ad (has remaining ad limit or free ads)
   */
  static async canUserPostAd(userId: string): Promise<{ canPost: boolean; reason?: string }> {
    const usage = await this.getUserSubscriptionUsage(userId)

    // If user has active subscription, check subscription limits
    if (usage.has_active_subscription) {
      if (usage.ads_remaining <= 0) {
        return { canPost: false, reason: 'Ad limit reached. Please upgrade your subscription or wait for renewal.' }
      }
      return { canPost: true }
    }

    // If no active subscription, check free ads
    const freeAdsUsage = await this.getFreeAdsUsage(userId)
    if (freeAdsUsage.canUseFreeAds) {
      return { canPost: true, reason: `Free ad ${freeAdsUsage.freeAdsUsed + 1} of ${freeAdsUsage.freeAdsLimit}` }
    }

    return {
      canPost: false,
      reason: `You've used all ${freeAdsUsage.freeAdsLimit} free ads. Please purchase a subscription to continue posting.`
    }
  }

  /**
   * Check if user can boost an ad
   */
  static async canUserBoostAd(userId: string): Promise<{ canBoost: boolean; reason?: string }> {
    const usage = await this.getUserSubscriptionUsage(userId)

    if (!usage.has_active_subscription) {
      return { canBoost: false, reason: 'No active subscription. Please purchase a subscription package.' }
    }

    if (usage.boosts_remaining <= 0) {
      return { canBoost: false, reason: 'Boost limit reached. Please upgrade your subscription or wait for renewal.' }
    }

    return { canBoost: true }
  }

  /**
   * Boost an ad
   */
  static async boostAd(userId: string, adId: string): Promise<AdBoost> {
    try {
      // Check if user can boost
      const canBoost = await this.canUserBoostAd(userId)
      if (!canBoost.canBoost) {
        throw new Error(canBoost.reason || 'Cannot boost ad')
      }

      // Get user's active subscription
      const subscription = await this.getUserActiveSubscription(userId)
      if (!subscription) {
        throw new Error('No active subscription found')
      }

      // Check if ad exists and belongs to user
      const { data: ad, error: adError } = await supabase
        .from(TABLES.ADS)
        .select('id, user_id, is_boosted')
        .eq('id', adId)
        .eq('user_id', userId)
        .single()

      if (adError || !ad) {
        throw new Error('Ad not found or does not belong to user')
      }

      if (ad.is_boosted) {
        throw new Error('Ad is already boosted')
      }

      // Calculate boost expiration (3 days)
      const boostExpiresAt = new Date()
      boostExpiresAt.setDate(boostExpiresAt.getDate() + 3)

      // Create boost record
      const { data: boost, error: boostError } = await supabase
        .from('ad_boosts')
        .insert({
          ad_id: adId,
          user_id: userId,
          subscription_id: subscription.id,
          expires_at: boostExpiresAt.toISOString()
        })
        .select('*')
        .single()

      if (boostError) {
        throw new Error(`Failed to create boost: ${boostError.message}`)
      }

      // Update ad to mark as boosted
      const { error: adUpdateError } = await supabase
        .from(TABLES.ADS)
        .update({
          is_boosted: true,
          boost_expires_at: boostExpiresAt.toISOString(),
          updated_at: new Date().toISOString()
        })
        .eq('id', adId)

      if (adUpdateError) {
        // Rollback boost creation
        await supabase
          .from('ad_boosts')
          .delete()
          .eq('id', boost.id)

        throw new Error(`Failed to update ad: ${adUpdateError.message}`)
      }

      return boost
    } catch (error) {
      console.error('Error in boostAd:', error)
      throw error
    }
  }

  /**
   * Get user's subscription history
   */
  static async getUserSubscriptionHistory(userId: string): Promise<UserSubscription[]> {
    const { data, error } = await supabase
      .from('user_subscriptions')
      .select(`
        *,
        package:subscription_packages(*)
      `)
      .eq('user_id', userId)
      .order('created_at', { ascending: false })

    if (error) {
      throw new Error(`Failed to fetch subscription history: ${error.message}`)
    }

    return data || []
  }

  /**
   * Expire old boosts (to be called by background job)
   */
  static async expireBoosts(): Promise<void> {
    const { error } = await supabase.rpc('expire_ad_boosts')

    if (error) {
      throw new Error(`Failed to expire boosts: ${error.message}`)
    }
  }

  /**
   * Expire old subscriptions (to be called by background job)
   */
  static async expireSubscriptions(): Promise<void> {
    const { error } = await supabase.rpc('expire_subscriptions')

    if (error) {
      throw new Error(`Failed to expire subscriptions: ${error.message}`)
    }
  }
}
