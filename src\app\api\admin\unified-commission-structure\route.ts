import { NextRequest, NextResponse } from 'next/server'
import { supabaseAdmin } from '@/lib/supabase'
import { ExtendedCommissionStructure } from '@/types'

export async function GET(request: NextRequest) {
  try {
    // Get unified commission structures (one per package)
    const { data: structures, error } = await supabaseAdmin
      .from('commission_structure')
      .select('*')
      .eq('commission_type', 'unified_structure')
      .order('package_value', { ascending: true })

    if (error) {
      throw new Error(`Failed to fetch commission structures: ${error.message}`)
    }

    // Transform the data to match the expected extended schema with absolute amounts
    const transformedStructures = (structures || []).map(structure => ({
      id: structure.id,
      commission_type: structure.commission_type,
      package_value: structure.package_value,
      // Budget configuration
      network_distribution_budget: structure.network_distribution_budget || 0,
      company_wallet_amount: structure.company_wallet_amount || 0,
      // Commission amounts (absolute values)
      direct_commission_amount: structure.direct_commission_amount || 0,
      level_commission_amount: structure.level_commission_amount || 0,
      voucher_amount: structure.voucher_amount || 0,
      festival_bonus_amount: structure.festival_bonus_amount || 0,
      saving_amount: structure.saving_amount || 0,
      gift_center_amount: structure.gift_center_amount || 0,
      entertainment_amount: structure.entertainment_amount || 0,
      medical_amount: structure.medical_amount || 0,
      education_amount: structure.education_amount || 0,
      credit_amount: structure.credit_amount || 0,
      // ZM and RSM amounts
      zm_bonus_amount: structure.zm_bonus_amount || 0,
      zm_petral_allowance_amount: structure.zm_petral_allowance_amount || 0,
      zm_leasing_facility_amount: structure.zm_leasing_facility_amount || 0,
      zm_phone_bill_amount: structure.zm_phone_bill_amount || 0,
      rsm_bonus_amount: structure.rsm_bonus_amount || 0,
      rsm_petral_allowance_amount: structure.rsm_petral_allowance_amount || 0,
      rsm_leasing_facility_amount: structure.rsm_leasing_facility_amount || 0,
      rsm_phone_bill_amount: structure.rsm_phone_bill_amount || 0,
      // Gift system amounts
      present_user_amount: structure.present_user_amount || 0,
      present_leader_amount: structure.present_leader_amount || 0,
      annual_present_user_amount: structure.annual_present_user_amount || 0,
      annual_present_leader_amount: structure.annual_present_leader_amount || 0,
      // OKDOI Head amounts (package-specific)
      okdoi_head_amount_2000: structure.okdoi_head_amount_2000 || 0,
      okdoi_head_amount_5000: structure.okdoi_head_amount_5000 || 0,
      okdoi_head_amount_10000: structure.okdoi_head_amount_10000 || 0,
      okdoi_head_amount_50000: structure.okdoi_head_amount_50000 || 0,
      is_active: structure.is_active,
      created_at: structure.created_at,
      updated_at: structure.updated_at
    }))

    return NextResponse.json({
      success: true,
      data: transformedStructures
    })
  } catch (error) {
    console.error('Error fetching unified commission structures:', error)
    return NextResponse.json(
      { error: 'Failed to fetch commission structures' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()

    // Validate required fields
    if (!body.package_value) {
      return NextResponse.json(
        { error: 'Package value is required' },
        { status: 400 }
      )
    }

    // Check if unified structure already exists for this package
    const { data: existing, error: checkError } = await supabaseAdmin
      .from('commission_structure')
      .select('id')
      .eq('commission_type', 'unified_structure')
      .eq('package_value', body.package_value)
      .single()

    if (existing) {
      return NextResponse.json(
        { error: 'Commission structure already exists for this package' },
        { status: 400 }
      )
    }

    // Create new unified commission structure with absolute amounts
    const { data: newStructure, error } = await supabaseAdmin
      .from('commission_structure')
      .insert({
        commission_type: 'unified_structure',
        package_value: body.package_value,
        // Budget configuration
        network_distribution_budget: body.network_distribution_budget || 0,
        company_wallet_amount: body.company_wallet_amount || 0,
        // Commission amounts
        direct_commission_amount: body.direct_commission_amount || 0,
        level_commission_amount: body.level_commission_amount || 0,
        voucher_amount: body.voucher_amount || 0,
        festival_bonus_amount: body.festival_bonus_amount || 0,
        saving_amount: body.saving_amount || 0,
        gift_center_amount: body.gift_center_amount || 0,
        entertainment_amount: body.entertainment_amount || 0,
        medical_amount: body.medical_amount || 0,
        education_amount: body.education_amount || 0,
        credit_amount: body.credit_amount || 0,
        // ZM and RSM amounts
        zm_bonus_amount: body.zm_bonus_amount || 0,
        zm_petral_allowance_amount: body.zm_petral_allowance_amount || 0,
        zm_leasing_facility_amount: body.zm_leasing_facility_amount || 0,
        zm_phone_bill_amount: body.zm_phone_bill_amount || 0,
        rsm_bonus_amount: body.rsm_bonus_amount || 0,
        rsm_petral_allowance_amount: body.rsm_petral_allowance_amount || 0,
        rsm_leasing_facility_amount: body.rsm_leasing_facility_amount || 0,
        rsm_phone_bill_amount: body.rsm_phone_bill_amount || 0,
        // Gift system amounts
        present_user_amount: body.present_user_amount || 0,
        present_leader_amount: body.present_leader_amount || 0,
        annual_present_user_amount: body.annual_present_user_amount || 0,
        annual_present_leader_amount: body.annual_present_leader_amount || 0,
        // OKDOI Head amounts
        okdoi_head_amount_2000: body.okdoi_head_amount_2000 || 0,
        okdoi_head_amount_5000: body.okdoi_head_amount_5000 || 0,
        okdoi_head_amount_10000: body.okdoi_head_amount_10000 || 0,
        okdoi_head_amount_50000: body.okdoi_head_amount_50000 || 0,
        okdoi_head_rate_2000: body.okdoi_head_rate_2000 || 0.025,
        okdoi_head_rate_5000: body.okdoi_head_rate_5000 || 0.02,
        okdoi_head_rate_10000: body.okdoi_head_rate_10000 || 0.02,
        okdoi_head_rate_50000: body.okdoi_head_rate_50000 || 0.02,
        // ZM rates
        zm_bonus_rate: body.zm_bonus_rate || 0.02,
        zm_petral_allowance_rate: body.zm_petral_allowance_rate || 0.005,
        zm_leasing_facility_rate: body.zm_leasing_facility_rate || 0.01,
        zm_phone_bill_rate: body.zm_phone_bill_rate || 0.001,
        // RSM rates
        rsm_bonus_rate: body.rsm_bonus_rate || 0.025,
        rsm_petral_allowance_rate: body.rsm_petral_allowance_rate || 0.005,
        rsm_leasing_facility_rate: body.rsm_leasing_facility_rate || 0.01,
        rsm_phone_bill_rate: body.rsm_phone_bill_rate || 0.001,
        is_active: body.is_active !== undefined ? body.is_active : true
      })
      .select()
      .single()

    if (error) {
      throw new Error(`Failed to create commission structure: ${error.message}`)
    }

    return NextResponse.json({
      success: true,
      message: 'Commission structure created successfully',
      data: newStructure
    })
  } catch (error) {
    console.error('Error creating commission structure:', error)
    return NextResponse.json(
      { error: 'Failed to create commission structure' },
      { status: 500 }
    )
  }
}




