-- New Commission Distribution Algorithm Implementation
-- Based on Commission_Distribution_Explained.md requirements
-- Date: 2025-01-09

-- =====================================================
-- 1. ADD LEFTOVER COMMISSION CATEGORY TO WALLET TRANSACTIONS
-- =====================================================

-- Add new transaction category for leftover commissions
DO $$
BEGIN
    -- Check if the constraint exists and modify it
    IF EXISTS (
        SELECT 1 FROM information_schema.check_constraints 
        WHERE constraint_name = 'wallet_transactions_transaction_type_check'
    ) THEN
        -- Drop the existing constraint
        ALTER TABLE wallet_transactions DROP CONSTRAINT wallet_transactions_transaction_type_check;
        
        -- Add the new constraint with leftover_commission
        ALTER TABLE wallet_transactions ADD CONSTRAINT wallet_transactions_transaction_type_check 
        CHECK (transaction_type IN (
            'deposit', 'withdrawal', 'commission', 'referral_bonus', 'purchase', 'refund', 
            'transfer_sent', 'transfer_received', 'merchant_transfer', 'leftover_commission'
        ));
    END IF;
END $$;

-- =====================================================
-- 2. UPDATE COMMISSION STRUCTURE FOR PACKAGE-SPECIFIC OKDOI HEAD RATES
-- =====================================================

-- Add package-specific OKDOI Head commission rates
ALTER TABLE commission_structure 
ADD COLUMN IF NOT EXISTS okdoi_head_rate_2000 DECIMAL(5,4) DEFAULT 0.025,
ADD COLUMN IF NOT EXISTS okdoi_head_rate_5000 DECIMAL(5,4) DEFAULT 0.02,
ADD COLUMN IF NOT EXISTS okdoi_head_rate_10000 DECIMAL(5,4) DEFAULT 0.02,
ADD COLUMN IF NOT EXISTS okdoi_head_rate_50000 DECIMAL(5,4) DEFAULT 0.02;

-- Update existing commission structures with new OKDOI Head rates
UPDATE commission_structure SET
    okdoi_head_rate_2000 = 0.025,  -- 2.50%
    okdoi_head_rate_5000 = 0.02,   -- 2%
    okdoi_head_rate_10000 = 0.02,  -- 2%
    okdoi_head_rate_50000 = 0.02   -- 2%
WHERE is_active = true;

-- =====================================================
-- 3. CREATE HELPER FUNCTION FOR OKDOI HEAD COMMISSION RATE
-- =====================================================

CREATE OR REPLACE FUNCTION get_okdoi_head_commission_rate(package_amount DECIMAL(12,2))
RETURNS DECIMAL(5,4) AS $$
BEGIN
    RETURN CASE 
        WHEN package_amount <= 2000 THEN 0.025  -- 2.50%
        WHEN package_amount <= 5000 THEN 0.02   -- 2%
        WHEN package_amount <= 10000 THEN 0.02  -- 2%
        WHEN package_amount <= 50000 THEN 0.02  -- 2%
        ELSE 0.02  -- Default 2% for packages above 50000
    END;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- 4. NEW COMMISSION DISTRIBUTION ALGORITHM
-- =====================================================

CREATE OR REPLACE FUNCTION calculate_commission_distribution_new(
    purchaser_id UUID,
    package_id UUID,
    package_amount DECIMAL(12,2)
) RETURNS VOID AS $$
DECLARE
    commission_record RECORD;
    beneficiary_record RECORD;
    commission_amount DECIMAL(12,2);
    transaction_id_val VARCHAR(50);
    okdoi_head_id UUID;
    total_distributed DECIMAL(12,2) := 0;
    max_levels INTEGER;
    beneficiary_level_limit INTEGER;
    current_level INTEGER;
    commission_types TEXT[] := ARRAY[
        'level_commission', 'voucher', 'festival_bonus', 'saving', 
        'gift_center', 'entertainment', 'medical', 'education', 'credit'
    ];
    commission_type_name TEXT;
    commission_rate DECIMAL(5,4);
    leftover_commission_total DECIMAL(12,2) := 0;
    okdoi_head_commission_rate DECIMAL(5,4);
    okdoi_head_commission_amount DECIMAL(12,2);
    beneficiary_wallet_id UUID;
    wallet_transaction_id UUID;
BEGIN
    -- Generate unique transaction ID
    transaction_id_val := 'COM-' || TO_CHAR(NOW(), 'YYYYMMDD') || '-' || UPPER(SUBSTRING(MD5(RANDOM()::TEXT) FROM 1 FOR 8));

    -- Get OKDOI Head user ID
    SELECT id INTO okdoi_head_id FROM users WHERE user_type = 'okdoi_head' LIMIT 1;
    
    IF okdoi_head_id IS NULL THEN
        RAISE EXCEPTION 'OKDOI Head user not found';
    END IF;

    -- Get the maximum depth of the referral hierarchy from this purchaser
    SELECT get_max_referral_depth(purchaser_id) INTO max_levels;
    
    -- If no upline exists, set max_levels to at least 10 to check for potential beneficiaries
    IF max_levels = 0 THEN
        max_levels := 10;
    END IF;

    -- Get commission structure for this package value
    FOR commission_record IN
        SELECT * FROM commission_structure
        WHERE package_value <= package_amount
        AND is_active = true
        ORDER BY package_value DESC
        LIMIT 1
    LOOP
        -- =====================================================
        -- STEP 1: PROCESS OKDOI HEAD UNIVERSAL COMMISSION FIRST
        -- =====================================================
        
        -- Get package-specific OKDOI Head commission rate
        okdoi_head_commission_rate := get_okdoi_head_commission_rate(package_amount);
        okdoi_head_commission_amount := package_amount * okdoi_head_commission_rate;
        
        -- Get OKDOI Head's wallet
        SELECT id INTO beneficiary_wallet_id 
        FROM user_wallets 
        WHERE user_id = okdoi_head_id;
        
        IF beneficiary_wallet_id IS NOT NULL AND okdoi_head_commission_amount > 0 THEN
            -- Create wallet transaction for OKDOI Head
            INSERT INTO wallet_transactions (
                wallet_id, transaction_type, amount, description, 
                reference_id, reference_type, status, metadata
            ) VALUES (
                beneficiary_wallet_id, 'commission', okdoi_head_commission_amount,
                'OKDOI Head Universal Commission from subscription',
                package_id, 'subscription', 'completed',
                '{"commission_type": "okdoi_head_universal", "package_amount": ' || package_amount || '}'
            ) RETURNING id INTO wallet_transaction_id;
            
            -- Update wallet balance
            UPDATE user_wallets 
            SET balance = balance + okdoi_head_commission_amount,
                updated_at = NOW()
            WHERE id = beneficiary_wallet_id;
            
            -- Create commission transaction record
            INSERT INTO commission_transactions (
                user_id, beneficiary_id, subscription_purchase_id, commission_type,
                commission_level, commission_rate, commission_amount, status, 
                wallet_transaction_id, transaction_id, created_at, metadata
            ) VALUES (
                purchaser_id, okdoi_head_id, package_id, 'okdoi_head_universal',
                0, okdoi_head_commission_rate, okdoi_head_commission_amount, 
                'processed', wallet_transaction_id, transaction_id_val, NOW(),
                '{"source": "universal_commission", "applies_to": "all_users"}'
            );
            
            -- Update OKDOI Head's total commission earned
            UPDATE users 
            SET total_commission_earned = COALESCE(total_commission_earned, 0) + okdoi_head_commission_amount,
                updated_at = NOW()
            WHERE id = okdoi_head_id;
        END IF;

        -- =====================================================
        -- STEP 2: PROCESS DIRECT COMMISSION (Level 1 only)
        -- =====================================================
        
        -- Get direct referrer (level 1)
        SELECT u.* INTO beneficiary_record
        FROM users u
        JOIN referral_hierarchy rh ON rh.ancestor_id = u.id
        WHERE rh.user_id = purchaser_id AND rh.level_difference = 1
        LIMIT 1;
        
        IF beneficiary_record.id IS NOT NULL THEN
            commission_amount := package_amount * commission_record.direct_commission_rate;
            
            IF commission_amount > 0 THEN
                -- Get beneficiary's wallet
                SELECT id INTO beneficiary_wallet_id 
                FROM user_wallets 
                WHERE user_id = beneficiary_record.id;
                
                IF beneficiary_wallet_id IS NOT NULL THEN
                    -- Create wallet transaction
                    INSERT INTO wallet_transactions (
                        wallet_id, transaction_type, amount, description, 
                        reference_id, reference_type, status, metadata
                    ) VALUES (
                        beneficiary_wallet_id, 'commission', commission_amount,
                        'Direct Commission from subscription',
                        package_id, 'subscription', 'completed',
                        '{"commission_type": "direct_commission", "level": 1}'
                    ) RETURNING id INTO wallet_transaction_id;
                    
                    -- Update wallet balance
                    UPDATE user_wallets 
                    SET balance = balance + commission_amount,
                        updated_at = NOW()
                    WHERE id = beneficiary_wallet_id;
                    
                    -- Create commission transaction record
                    INSERT INTO commission_transactions (
                        user_id, beneficiary_id, subscription_purchase_id, commission_type,
                        commission_level, commission_rate, commission_amount, status, 
                        wallet_transaction_id, transaction_id, created_at, metadata
                    ) VALUES (
                        purchaser_id, beneficiary_record.id, package_id, 'direct_commission',
                        1, commission_record.direct_commission_rate, commission_amount, 
                        'processed', wallet_transaction_id, transaction_id_val || '-DIRECT', NOW(),
                        '{"beneficiary_type": "' || beneficiary_record.user_type || '"}'
                    );
                    
                    -- Update beneficiary's total commission earned
                    UPDATE users 
                    SET total_commission_earned = COALESCE(total_commission_earned, 0) + commission_amount,
                        updated_at = NOW()
                    WHERE id = beneficiary_record.id;
                END IF;
            END IF;
        END IF;

        -- =====================================================
        -- STEP 3: PROCESS ALL 9 COMMISSION TYPES (excluding Direct)
        -- NEW REQUIREMENT: All 9 types distributed to user + 9 levels down
        -- =====================================================

        -- Loop through each commission type
        FOREACH commission_type_name IN ARRAY commission_types
        LOOP
            -- Get the commission rate for this type
            commission_rate := CASE commission_type_name
                WHEN 'level_commission' THEN commission_record.level_commission_rate
                WHEN 'voucher' THEN commission_record.voucher_rate
                WHEN 'festival_bonus' THEN commission_record.festival_bonus_rate
                WHEN 'saving' THEN commission_record.saving_rate
                WHEN 'gift_center' THEN commission_record.gift_center_rate
                WHEN 'entertainment' THEN commission_record.entertainment_rate
                WHEN 'medical' THEN commission_record.medical_rate
                WHEN 'education' THEN commission_record.education_rate
                WHEN 'credit' THEN commission_record.credit_rate
                ELSE 0
            END;

            IF commission_rate > 0 THEN
                -- FIRST: Give commission to the purchaser themselves (Level 0)
                commission_amount := package_amount * commission_rate;

                -- Get purchaser's wallet
                SELECT id INTO beneficiary_wallet_id
                FROM user_wallets
                WHERE user_id = purchaser_id;

                IF beneficiary_wallet_id IS NOT NULL THEN
                    -- Create wallet transaction for purchaser
                    INSERT INTO wallet_transactions (
                        wallet_id, transaction_type, amount, description,
                        reference_id, reference_type, status, metadata
                    ) VALUES (
                        beneficiary_wallet_id, 'commission', commission_amount,
                        commission_type_name || ' Commission (Self)',
                        package_id, 'subscription', 'completed',
                        '{"commission_type": "' || commission_type_name || '", "level": 0, "self_commission": true}'
                    ) RETURNING id INTO wallet_transaction_id;

                    -- Update wallet balance
                    UPDATE user_wallets
                    SET balance = balance + commission_amount,
                        updated_at = NOW()
                    WHERE id = beneficiary_wallet_id;

                    -- Create commission transaction record
                    INSERT INTO commission_transactions (
                        user_id, beneficiary_id, subscription_purchase_id, commission_type,
                        commission_level, commission_rate, commission_amount, status,
                        wallet_transaction_id, transaction_id, created_at, metadata
                    ) VALUES (
                        purchaser_id, purchaser_id, package_id, commission_type_name,
                        0, commission_rate, commission_amount,
                        'processed', wallet_transaction_id, transaction_id_val || '-' || UPPER(commission_type_name) || '-SELF', NOW(),
                        '{"self_commission": true, "commission_type": "' || commission_type_name || '"}'
                    );

                    -- Update purchaser's total commission earned
                    UPDATE users
                    SET total_commission_earned = COALESCE(total_commission_earned, 0) + commission_amount,
                        updated_at = NOW()
                    WHERE id = purchaser_id;
                END IF;

                -- SECOND: Distribute to 9 levels up the hierarchy
                FOR i IN 1..9 LOOP
                    -- Get beneficiary at this level
                    SELECT u.* INTO beneficiary_record
                    FROM users u
                    JOIN referral_hierarchy rh ON rh.ancestor_id = u.id
                    WHERE rh.user_id = purchaser_id AND rh.level_difference = i
                    LIMIT 1;

                    IF beneficiary_record.id IS NOT NULL THEN
                        -- Check if this beneficiary can receive commissions at this level
                        SELECT get_commission_level_limit(beneficiary_record.user_type) INTO beneficiary_level_limit;

                        IF i <= beneficiary_level_limit THEN
                            -- Beneficiary can receive this commission
                            commission_amount := package_amount * commission_rate;

                            -- Get beneficiary's wallet
                            SELECT id INTO beneficiary_wallet_id
                            FROM user_wallets
                            WHERE user_id = beneficiary_record.id;

                            IF beneficiary_wallet_id IS NOT NULL THEN
                                -- Create wallet transaction
                                INSERT INTO wallet_transactions (
                                    wallet_id, transaction_type, amount, description,
                                    reference_id, reference_type, status, metadata
                                ) VALUES (
                                    beneficiary_wallet_id, 'commission', commission_amount,
                                    commission_type_name || ' Commission Level ' || i,
                                    package_id, 'subscription', 'completed',
                                    '{"commission_type": "' || commission_type_name || '", "level": ' || i || '}'
                                ) RETURNING id INTO wallet_transaction_id;

                                -- Update wallet balance
                                UPDATE user_wallets
                                SET balance = balance + commission_amount,
                                    updated_at = NOW()
                                WHERE id = beneficiary_wallet_id;

                                -- Create commission transaction record
                                INSERT INTO commission_transactions (
                                    user_id, beneficiary_id, subscription_purchase_id, commission_type,
                                    commission_level, commission_rate, commission_amount, status,
                                    wallet_transaction_id, transaction_id, created_at, metadata
                                ) VALUES (
                                    purchaser_id, beneficiary_record.id, package_id, commission_type_name,
                                    i, commission_rate, commission_amount,
                                    'processed', wallet_transaction_id, transaction_id_val || '-' || UPPER(commission_type_name) || '-L' || i, NOW(),
                                    '{"beneficiary_type": "' || beneficiary_record.user_type || '", "level_limit": ' || beneficiary_level_limit || '}'
                                );

                                -- Update beneficiary's total commission earned
                                UPDATE users
                                SET total_commission_earned = COALESCE(total_commission_earned, 0) + commission_amount,
                                    updated_at = NOW()
                                WHERE id = beneficiary_record.id;
                            END IF;
                        ELSE
                            -- Beneficiary exists but level exceeds their limit - goes to leftover
                            commission_amount := package_amount * commission_rate;
                            leftover_commission_total := leftover_commission_total + commission_amount;
                        END IF;
                    ELSE
                        -- No beneficiary at this level - goes to leftover
                        commission_amount := package_amount * commission_rate;
                        leftover_commission_total := leftover_commission_total + commission_amount;
                    END IF;
                END LOOP;
            END IF;
        END LOOP;

        -- =====================================================
        -- STEP 4: PROCESS LEFTOVER COMMISSIONS
        -- =====================================================

        IF leftover_commission_total > 0 AND okdoi_head_id IS NOT NULL THEN
            -- Get OKDOI Head's wallet
            SELECT id INTO beneficiary_wallet_id
            FROM user_wallets
            WHERE user_id = okdoi_head_id;

            IF beneficiary_wallet_id IS NOT NULL THEN
                -- Create wallet transaction for leftover commissions
                INSERT INTO wallet_transactions (
                    wallet_id, transaction_type, amount, description,
                    reference_id, reference_type, status, metadata
                ) VALUES (
                    beneficiary_wallet_id, 'leftover_commission', leftover_commission_total,
                    'Leftover Commissions from subscription',
                    package_id, 'subscription', 'completed',
                    '{"commission_type": "leftover_commission", "package_amount": ' || package_amount || '}'
                ) RETURNING id INTO wallet_transaction_id;

                -- Update wallet balance
                UPDATE user_wallets
                SET balance = balance + leftover_commission_total,
                    updated_at = NOW()
                WHERE id = beneficiary_wallet_id;

                -- Create commission transaction record
                INSERT INTO commission_transactions (
                    user_id, beneficiary_id, subscription_purchase_id, commission_type,
                    commission_level, commission_rate, commission_amount, status,
                    wallet_transaction_id, transaction_id, created_at, metadata
                ) VALUES (
                    purchaser_id, okdoi_head_id, package_id, 'leftover_commission',
                    99, 0, leftover_commission_total,
                    'processed', wallet_transaction_id, transaction_id_val || '-LEFTOVER', NOW(),
                    '{"source": "leftover_commission", "total_leftover": ' || leftover_commission_total || '}'
                );

                -- Update OKDOI Head's total commission earned
                UPDATE users
                SET total_commission_earned = COALESCE(total_commission_earned, 0) + leftover_commission_total,
                    updated_at = NOW()
                WHERE id = okdoi_head_id;
            END IF;
        END IF;
    END LOOP;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- 5. REPLACE THE OLD FUNCTION WITH THE NEW ONE
-- =====================================================

-- Drop the old function and replace with new implementation
DROP FUNCTION IF EXISTS calculate_commission_distribution(UUID, UUID, DECIMAL);

-- Rename the new function to replace the old one
CREATE OR REPLACE FUNCTION calculate_commission_distribution(
    purchaser_id UUID,
    package_id UUID,
    package_amount DECIMAL(12,2)
) RETURNS VOID AS $$
BEGIN
    -- Call the new implementation
    PERFORM calculate_commission_distribution_new(purchaser_id, package_id, package_amount);
END;
$$ LANGUAGE plpgsql;

-- Add comment explaining the new algorithm
COMMENT ON FUNCTION calculate_commission_distribution(UUID, UUID, DECIMAL) IS
'New commission distribution algorithm implementing requirements from Commission_Distribution_Explained.md:
1. OKDOI Head gets package-specific universal commission from ALL users
2. Direct commission goes to level 1 referrer only
3. All 9 commission types (excluding direct) are distributed to purchaser + 9 levels up
4. Leftover commissions (when fewer than 10 levels exist) go to OKDOI Head
5. User type level limits are respected (Normal users: 10 levels, RSM/ZM/OKDOI Head: unlimited)';

-- =====================================================
-- 6. UPDATE COMMISSION TRANSACTION LEVEL CONSTRAINT
-- =====================================================

-- Update the commission_level constraint to allow level 0 (self) and level 99 (leftover)
ALTER TABLE commission_transactions DROP CONSTRAINT IF EXISTS commission_transactions_commission_level_check;
ALTER TABLE commission_transactions ADD CONSTRAINT commission_transactions_commission_level_check
CHECK (commission_level BETWEEN 0 AND 99);
