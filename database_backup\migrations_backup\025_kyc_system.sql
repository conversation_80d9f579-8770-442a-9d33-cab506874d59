-- KY<PERSON> (Know Your Customer) Verification System Migration
-- Creates comprehensive KYC verification system for OKDOI marketplace

-- Add KYC status to users table
ALTER TABLE users ADD COLUMN IF NOT EXISTS kyc_status varchar(20) DEFAULT 'not_submitted' 
CHECK (kyc_status IN ('not_submitted', 'pending', 'approved', 'rejected'));

-- Add KYC submission timestamp
ALTER TABLE users ADD COLUMN IF NOT EXISTS kyc_submitted_at timestamp with time zone;
ALTER TABLE users ADD COLUMN IF NOT EXISTS kyc_approved_at timestamp with time zone;

-- Create KYC submissions table
CREATE TABLE kyc_submissions (
    id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id uuid REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    
    -- Document URLs (stored in Supabase Storage)
    id_document_front_url text NOT NULL,
    id_document_back_url text NOT NULL,
    selfie_photo_url text NOT NULL,
    address_proof_url text NOT NULL,
    
    -- Document metadata
    id_document_type varchar(50) NOT NULL CHECK (id_document_type IN ('national_id', 'passport', 'driving_license')),
    id_document_number varchar(100),
    
    -- Personal information for verification
    full_name varchar(255) NOT NULL,
    date_of_birth date,
    address text NOT NULL,
    
    -- Submission status and tracking
    status varchar(20) DEFAULT 'pending' CHECK (status IN ('pending', 'under_review', 'approved', 'rejected')),
    submission_notes text, -- User notes during submission
    
    -- Admin review fields
    reviewed_by uuid REFERENCES auth.users(id) ON DELETE SET NULL,
    reviewed_at timestamp with time zone,
    rejection_reason text,
    admin_notes text,
    
    -- Timestamps
    created_at timestamp with time zone DEFAULT now(),
    updated_at timestamp with time zone DEFAULT now(),
    
    -- Ensure one active submission per user
    UNIQUE(user_id)
);

-- Create KYC document types reference table
CREATE TABLE kyc_document_types (
    id varchar(50) PRIMARY KEY,
    name varchar(100) NOT NULL,
    description text,
    is_active boolean DEFAULT true,
    created_at timestamp with time zone DEFAULT now()
);

-- Insert default document types
INSERT INTO kyc_document_types (id, name, description) VALUES
('national_id', 'National Identity Card', 'Sri Lankan National Identity Card'),
('passport', 'Passport', 'Valid passport document'),
('driving_license', 'Driving License', 'Valid driving license');

-- Create KYC status history table for audit trail
CREATE TABLE kyc_status_history (
    id uuid DEFAULT gen_random_uuid() PRIMARY KEY,
    kyc_submission_id uuid REFERENCES kyc_submissions(id) ON DELETE CASCADE NOT NULL,
    user_id uuid REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    
    -- Status change tracking
    previous_status varchar(20),
    new_status varchar(20) NOT NULL,
    changed_by uuid REFERENCES auth.users(id) ON DELETE SET NULL,
    change_reason text,
    admin_notes text,
    
    -- Timestamps
    created_at timestamp with time zone DEFAULT now()
);

-- Create indexes for performance
CREATE INDEX idx_kyc_submissions_user_id ON kyc_submissions(user_id);
CREATE INDEX idx_kyc_submissions_status ON kyc_submissions(status);
CREATE INDEX idx_kyc_submissions_created_at ON kyc_submissions(created_at);
CREATE INDEX idx_kyc_status_history_submission_id ON kyc_status_history(kyc_submission_id);
CREATE INDEX idx_kyc_status_history_user_id ON kyc_status_history(user_id);
CREATE INDEX idx_users_kyc_status ON users(kyc_status);

-- Create function to update user KYC status when submission status changes
CREATE OR REPLACE FUNCTION update_user_kyc_status()
RETURNS TRIGGER AS $$
BEGIN
    -- Update user's KYC status based on submission status
    UPDATE users 
    SET 
        kyc_status = NEW.status,
        kyc_submitted_at = CASE 
            WHEN NEW.status = 'pending' AND OLD.status IS NULL THEN NEW.created_at
            ELSE kyc_submitted_at
        END,
        kyc_approved_at = CASE 
            WHEN NEW.status = 'approved' THEN NEW.reviewed_at
            ELSE NULL
        END,
        updated_at = now()
    WHERE id = NEW.user_id;
    
    -- Insert status history record
    INSERT INTO kyc_status_history (
        kyc_submission_id,
        user_id,
        previous_status,
        new_status,
        changed_by,
        change_reason,
        admin_notes
    ) VALUES (
        NEW.id,
        NEW.user_id,
        OLD.status,
        NEW.status,
        NEW.reviewed_by,
        NEW.rejection_reason,
        NEW.admin_notes
    );
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for KYC status updates
CREATE TRIGGER trigger_update_user_kyc_status
    AFTER INSERT OR UPDATE ON kyc_submissions
    FOR EACH ROW
    EXECUTE FUNCTION update_user_kyc_status();

-- Create function to check KYC verification status
CREATE OR REPLACE FUNCTION is_kyc_verified(user_uuid uuid)
RETURNS boolean AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM users 
        WHERE id = user_uuid AND kyc_status = 'approved'
    );
END;
$$ LANGUAGE plpgsql;

-- Update updated_at timestamp function for KYC tables
CREATE OR REPLACE FUNCTION update_kyc_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create triggers for updated_at
CREATE TRIGGER trigger_kyc_submissions_updated_at
    BEFORE UPDATE ON kyc_submissions
    FOR EACH ROW
    EXECUTE FUNCTION update_kyc_updated_at();

-- Row Level Security (RLS) policies

-- Enable RLS on KYC tables
ALTER TABLE kyc_submissions ENABLE ROW LEVEL SECURITY;
ALTER TABLE kyc_status_history ENABLE ROW LEVEL SECURITY;
ALTER TABLE kyc_document_types ENABLE ROW LEVEL SECURITY;

-- KYC submissions policies
CREATE POLICY "Users can view their own KYC submissions" ON kyc_submissions
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own KYC submissions" ON kyc_submissions
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own pending KYC submissions" ON kyc_submissions
    FOR UPDATE USING (auth.uid() = user_id AND status = 'pending');

-- Admin policies for KYC submissions (assuming admin role exists)
CREATE POLICY "Admins can view all KYC submissions" ON kyc_submissions
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM users 
            WHERE id = auth.uid() AND (
                email LIKE '%@okdoi.com' OR 
                email IN ('<EMAIL>', '<EMAIL>')
            )
        )
    );

CREATE POLICY "Admins can update KYC submissions" ON kyc_submissions
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM users 
            WHERE id = auth.uid() AND (
                email LIKE '%@okdoi.com' OR 
                email IN ('<EMAIL>', '<EMAIL>')
            )
        )
    );

-- KYC status history policies
CREATE POLICY "Users can view their own KYC status history" ON kyc_status_history
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "System can insert KYC status history" ON kyc_status_history
    FOR INSERT WITH CHECK (true);

CREATE POLICY "Admins can view all KYC status history" ON kyc_status_history
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM users 
            WHERE id = auth.uid() AND (
                email LIKE '%@okdoi.com' OR 
                email IN ('<EMAIL>', '<EMAIL>')
            )
        )
    );

-- Document types policies (read-only for all authenticated users)
CREATE POLICY "Authenticated users can view document types" ON kyc_document_types
    FOR SELECT USING (auth.role() = 'authenticated');

-- Create storage bucket for KYC documents (if not exists)
-- This will be handled by the storage service

-- Add comments for documentation
COMMENT ON TABLE kyc_submissions IS 'Stores KYC verification submissions with document references';
COMMENT ON TABLE kyc_status_history IS 'Audit trail for KYC status changes';
COMMENT ON TABLE kyc_document_types IS 'Reference table for supported KYC document types';
COMMENT ON COLUMN users.kyc_status IS 'Current KYC verification status of the user';
COMMENT ON FUNCTION is_kyc_verified(uuid) IS 'Helper function to check if user has completed KYC verification';
