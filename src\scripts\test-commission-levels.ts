import { supabase, TABLES } from '@/lib/supabase'

async function testCommissionLevels() {
  console.log('🧪 Testing Commission Level Limits...\n')

  try {
    // Test 1: Verify commission level limit function
    console.log('1. Testing commission level limit function:')
    
    const userTypes = ['zonal_manager', 'rsm', 'user', 'okdoi_head']
    
    for (const userType of userTypes) {
      const { data: limit, error } = await supabase.rpc('get_commission_level_limit', {
        beneficiary_user_type: userType
      })
      
      if (error) {
        console.error(`❌ Error testing ${userType}:`, error.message)
        continue
      }
      
      const expected = userType === 'user' ? 10 : 999
      const status = limit === expected ? '✅' : '❌'
      console.log(`   ${status} ${userType}: ${limit} (expected: ${expected})`)
    }

    // Test 2: Check if the updated commission distribution function exists
    console.log('\n2. Testing commission distribution function:')
    
    const { data: functions, error: funcError } = await supabase.rpc('pg_get_functiondef', {
      funcoid: 'calculate_commission_distribution(uuid,uuid,numeric)'
    })
    
    if (funcError) {
      console.log('❌ Error checking function:', funcError.message)
    } else {
      console.log('✅ Commission distribution function exists and is updated')
    }

    // Test 3: Create a simple test scenario
    console.log('\n3. Creating test scenario:')
    
    // Clean up any existing test data
    await supabase.from(TABLES.COMMISSION_TRANSACTIONS).delete().like('transaction_id', 'TEST-%')
    await supabase.from(TABLES.REFERRAL_HIERARCHY).delete().like('user_id', '00000000-0000-0000-0000-%')
    await supabase.from(TABLES.USERS).delete().like('email', '%@commission-test.com')
    
    // Create test users
    const { data: okdoiHead, error: headError } = await supabase
      .from(TABLES.USERS)
      .insert({
        email: '<EMAIL>',
        full_name: 'OKDOI Head Test',
        user_type: 'okdoi_head',
        role: 'admin'
      })
      .select()
      .single()
    
    if (headError) {
      console.error('❌ Error creating OKDOI Head:', headError.message)
      return
    }
    
    const { data: zm, error: zmError } = await supabase
      .from(TABLES.USERS)
      .insert({
        email: '<EMAIL>',
        full_name: 'ZM Test',
        user_type: 'zonal_manager',
        role: 'user'
      })
      .select()
      .single()
    
    if (zmError) {
      console.error('❌ Error creating ZM:', zmError.message)
      return
    }
    
    const { data: normalUser, error: userError } = await supabase
      .from(TABLES.USERS)
      .insert({
        email: '<EMAIL>',
        full_name: 'Normal User Test',
        user_type: 'user',
        role: 'user'
      })
      .select()
      .single()
    
    if (userError) {
      console.error('❌ Error creating normal user:', userError.message)
      return
    }
    
    // Create referral hierarchy: OKDOI Head -> ZM -> Normal User
    await supabase.from(TABLES.REFERRAL_HIERARCHY).insert([
      {
        user_id: zm.id,
        ancestor_id: okdoiHead.id,
        level_difference: 1
      },
      {
        user_id: normalUser.id,
        ancestor_id: zm.id,
        level_difference: 1
      },
      {
        user_id: normalUser.id,
        ancestor_id: okdoiHead.id,
        level_difference: 2
      }
    ])
    
    console.log('✅ Test hierarchy created:')
    console.log(`   OKDOI Head: ${okdoiHead.id}`)
    console.log(`   ZM: ${zm.id}`)
    console.log(`   Normal User: ${normalUser.id}`)
    
    // Test commission distribution
    console.log('\n4. Testing commission distribution:')
    
    const testSubscriptionId = '00000000-0000-0000-0000-000000000001'
    const packageAmount = 5000
    
    const { error: distError } = await supabase.rpc('calculate_commission_distribution', {
      purchaser_id: normalUser.id,
      package_id: testSubscriptionId,
      package_amount: packageAmount
    })
    
    if (distError) {
      console.error('❌ Error in commission distribution:', distError.message)
      return
    }
    
    // Check results
    const { data: transactions, error: transError } = await supabase
      .from(TABLES.COMMISSION_TRANSACTIONS)
      .select(`
        *,
        beneficiary:users!commission_transactions_beneficiary_id_fkey(user_type, full_name)
      `)
      .eq('subscription_purchase_id', testSubscriptionId)
      .order('commission_level')
    
    if (transError) {
      console.error('❌ Error fetching transactions:', transError.message)
      return
    }
    
    console.log(`✅ Created ${transactions?.length || 0} commission transactions:`)
    
    transactions?.forEach((t: any) => {
      const beneficiaryType = (t.beneficiary as any)?.user_type || 'unknown'
      const beneficiaryName = (t.beneficiary as any)?.full_name || 'unknown'
      console.log(`   Level ${t.commission_level}: ${beneficiaryName} (${beneficiaryType}) - Rs ${t.commission_amount}`)
    })

    // Analyze by user type
    const zmTransactions = transactions?.filter((t: any) => (t.beneficiary as any)?.user_type === 'zonal_manager') || []
    const okdoiTransactions = transactions?.filter((t: any) => (t.beneficiary as any)?.user_type === 'okdoi_head') || []
    
    console.log('\n5. Analysis:')
    console.log(`   ZM received ${zmTransactions.length} transactions`)
    console.log(`   OKDOI Head received ${okdoiTransactions.length} transactions`)
    
    if (zmTransactions.length > 0) {
      console.log('✅ ZM can receive commissions (unlimited levels)')
    } else {
      console.log('❌ ZM did not receive commissions')
    }
    
    // Clean up test data
    console.log('\n6. Cleaning up test data...')
    await supabase.from(TABLES.COMMISSION_TRANSACTIONS).delete().eq('subscription_purchase_id', testSubscriptionId)
    await supabase.from(TABLES.REFERRAL_HIERARCHY).delete().eq('user_id', zm.id)
    await supabase.from(TABLES.REFERRAL_HIERARCHY).delete().eq('user_id', normalUser.id)
    await supabase.from(TABLES.USERS).delete().in('id', [okdoiHead.id, zm.id, normalUser.id])
    
    console.log('✅ Test completed successfully!')
    
  } catch (error) {
    console.error('❌ Test failed:', error)
  }
}

// Run the test
testCommissionLevels().then(() => {
  console.log('\n🎉 Commission level testing completed!')
  process.exit(0)
}).catch(error => {
  console.error('💥 Test execution failed:', error)
  process.exit(1)
})
