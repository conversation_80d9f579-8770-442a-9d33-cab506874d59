'use client'

import { supabase } from '@/lib/supabase'
import type { Session, User } from '@supabase/supabase-js'

export interface SessionState {
  session: Session | null
  user: User | null
  isLoading: boolean
  error: string | null
}

export class SessionManager {
  private static instance: SessionManager
  private sessionState: SessionState = {
    session: null,
    user: null,
    isLoading: true,
    error: null
  }
  private listeners: Set<(state: SessionState) => void> = new Set()
  private refreshPromise: Promise<boolean> | null = null
  private initialized = false

  private constructor() {
    this.initialize()
  }

  static getInstance(): SessionManager {
    if (!SessionManager.instance) {
      SessionManager.instance = new SessionManager()
    }
    return SessionManager.instance
  }

  private async initialize() {
    if (this.initialized) return
    this.initialized = true

    try {
      console.log('SessionManager: Initializing...')
      
      // Get initial session
      const { data: { session }, error } = await supabase.auth.getSession()
      
      if (error) {
        console.error('SessionManager: Initial session error:', error)
        this.updateState({
          session: null,
          user: null,
          isLoading: false,
          error: error.message
        })
        return
      }

      this.updateState({
        session,
        user: session?.user || null,
        isLoading: false,
        error: null
      })

      // Set up auth state listener
      supabase.auth.onAuthStateChange(async (event, session) => {
        console.log('SessionManager: Auth state changed:', event)
        
        switch (event) {
          case 'SIGNED_IN':
          case 'TOKEN_REFRESHED':
            this.updateState({
              session,
              user: session?.user || null,
              isLoading: false,
              error: null
            })
            break
            
          case 'SIGNED_OUT':
            this.updateState({
              session: null,
              user: null,
              isLoading: false,
              error: null
            })
            break
            
          case 'PASSWORD_RECOVERY':
          case 'USER_UPDATED':
            // Don't change loading state for these events
            this.updateState({
              session,
              user: session?.user || null,
              isLoading: this.sessionState.isLoading,
              error: null
            })
            break
            
          default:
            // For unknown events, just update session data
            this.updateState({
              session,
              user: session?.user || null,
              isLoading: false,
              error: null
            })
        }
      })

      console.log('SessionManager: Initialized successfully')
    } catch (error) {
      console.error('SessionManager: Initialization failed:', error)
      this.updateState({
        session: null,
        user: null,
        isLoading: false,
        error: error instanceof Error ? error.message : 'Session initialization failed'
      })
    }
  }

  private updateState(newState: Partial<SessionState>) {
    this.sessionState = { ...this.sessionState, ...newState }
    this.notifyListeners()
  }

  private notifyListeners() {
    this.listeners.forEach(listener => {
      try {
        listener(this.sessionState)
      } catch (error) {
        console.error('SessionManager: Listener error:', error)
      }
    })
  }

  public getState(): SessionState {
    return { ...this.sessionState }
  }

  public subscribe(listener: (state: SessionState) => void): () => void {
    this.listeners.add(listener)
    
    // Immediately call with current state
    listener(this.sessionState)
    
    // Return unsubscribe function
    return () => {
      this.listeners.delete(listener)
    }
  }

  public async refreshSession(): Promise<boolean> {
    // Prevent multiple concurrent refresh attempts
    if (this.refreshPromise) {
      return await this.refreshPromise
    }

    this.refreshPromise = this.performRefresh()
    
    try {
      return await this.refreshPromise
    } finally {
      this.refreshPromise = null
    }
  }

  private async performRefresh(): Promise<boolean> {
    try {
      console.log('SessionManager: Refreshing session...')
      
      this.updateState({ isLoading: true, error: null })
      
      const { data, error } = await supabase.auth.refreshSession()
      
      if (error) {
        console.error('SessionManager: Refresh failed:', error)
        this.updateState({
          session: null,
          user: null,
          isLoading: false,
          error: error.message
        })
        return false
      }

      if (data.session) {
        console.log('SessionManager: Session refreshed successfully')
        this.updateState({
          session: data.session,
          user: data.session.user,
          isLoading: false,
          error: null
        })
        return true
      }

      console.log('SessionManager: No session after refresh')
      this.updateState({
        session: null,
        user: null,
        isLoading: false,
        error: 'No session available'
      })
      return false
    } catch (error) {
      console.error('SessionManager: Refresh error:', error)
      this.updateState({
        session: null,
        user: null,
        isLoading: false,
        error: error instanceof Error ? error.message : 'Session refresh failed'
      })
      return false
    }
  }

  public async isSessionValid(): Promise<boolean> {
    const { session } = this.sessionState
    
    if (!session) {
      return false
    }

    // Check if token is expired
    const now = Math.floor(Date.now() / 1000)
    const expiresAt = session.expires_at || 0
    
    // If token expires in less than 5 minutes, try to refresh
    if (expiresAt - now < 300) {
      console.log('SessionManager: Token expiring soon, refreshing...')
      return await this.refreshSession()
    }

    return true
  }

  public async signOut(): Promise<void> {
    try {
      console.log('SessionManager: Signing out...')
      this.updateState({ isLoading: true })
      
      const { error } = await supabase.auth.signOut()
      
      if (error) {
        console.error('SessionManager: Sign out error:', error)
        this.updateState({
          isLoading: false,
          error: error.message
        })
      } else {
        this.updateState({
          session: null,
          user: null,
          isLoading: false,
          error: null
        })
      }
    } catch (error) {
      console.error('SessionManager: Sign out failed:', error)
      this.updateState({
        isLoading: false,
        error: error instanceof Error ? error.message : 'Sign out failed'
      })
    }
  }

  public clearError() {
    this.updateState({ error: null })
  }
}

// Export singleton instance
export const sessionManager = SessionManager.getInstance()
