import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'
import { createServerClient } from '@supabase/ssr'
import { cookies } from 'next/headers'

// Create admin Supabase client with service role key
const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  }
)

export async function GET(request: NextRequest) {
  try {
    // Create Supabase client for auth using SSR
    const cookieStore = cookies()
    const supabaseAuth = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return cookieStore.get(name)?.value
          },
        },
      }
    )
    
    // Get the current user
    const { data: { user }, error: authError } = await supabaseAuth.auth.getUser()

    const { searchParams } = new URL(request.url)
    const userId = searchParams.get('userId')

    // If no userId provided and no authenticated user, return error
    if (!userId && (!user || authError)) {
      return NextResponse.json({ error: 'Unauthorized or missing userId' }, { status: 401 })
    }

    // Use provided userId or authenticated user's ID
    const targetUserId = userId || user!.id

    // Validate UUID format
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i
    if (!uuidRegex.test(targetUserId)) {
      return NextResponse.json({ error: 'Invalid user ID format' }, { status: 400 })
    }

    // Get commission transactions for the user (excluding gift system commissions from user view)
    const { data: commissionTransactions, error: commissionError } = await supabaseAdmin
      .from('commission_transactions')
      .select('commission_type, commission_amount, metadata')
      .eq('beneficiary_id', targetUserId)
      .eq('status', 'processed')

    if (commissionError) {
      throw new Error(`Failed to fetch commission transactions: ${commissionError.message}`)
    }

    // Initialize commission breakdown with all categories
    const commissionBreakdown = {
      directCommission: 0,
      levelCommission: 0,
      voucherCommission: 0,
      festivalBonus: 0,
      savingCommission: 0,
      giftCenterCommission: 0,
      entertainmentCommission: 0,
      medicalCommission: 0,
      educationCommission: 0,
      creditCommission: 0,
      // ZM specific
      zmBonuses: 0,
      petralAllowanceZM: 0,
      leasingFacilityZM: 0,
      phoneBillZM: 0,
      // RSM specific
      rsmBonuses: 0,
      petralAllowanceRSM: 0,
      leasingFacilityRSM: 0,
      phoneBillRSM: 0,
      // OKDOI Head specific
      okdoiHeadCommission: 0,
      leftoverCommission: 0,
      totalCommissions: 0
    }

    // Aggregate commission amounts by type (filter out gift system commissions for user view)
    if (commissionTransactions && commissionTransactions.length > 0) {
      commissionTransactions.forEach(transaction => {
        // Skip gift system commissions and leftover commissions for user wallets
        const isGiftSystemCommission = transaction.metadata?.gift_system === true ||
          ['present_user', 'annual_present_user', 'present_leader', 'annual_present_leader'].includes(transaction.commission_type)

        const isLeftoverCommission = transaction.commission_type === 'leftover_commission'

        if (isGiftSystemCommission || isLeftoverCommission) {
          return // Skip gift system and leftover commissions in user wallet view
        }

        const amount = parseFloat(transaction.commission_amount) || 0

        switch (transaction.commission_type) {
          case 'direct':
          case 'direct_commission':
            commissionBreakdown.directCommission += amount
            break
          case 'level_commission':
          case 'sales':
            commissionBreakdown.levelCommission += amount
            break
          case 'voucher':
            commissionBreakdown.voucherCommission += amount
            break
          case 'festival_bonus':
            commissionBreakdown.festivalBonus += amount
            break
          case 'saving':
            commissionBreakdown.savingCommission += amount
            break
          case 'gift_center':
            commissionBreakdown.giftCenterCommission += amount
            break
          case 'entertainment':
            commissionBreakdown.entertainmentCommission += amount
            break
          case 'medical':
            commissionBreakdown.medicalCommission += amount
            break
          case 'education':
            commissionBreakdown.educationCommission += amount
            break
          case 'credit':
            commissionBreakdown.creditCommission += amount
            break
          case 'okdoi_head_universal':
          case 'okdoi_head_commission':
          case 'okdoi_head':
            commissionBreakdown.okdoiHeadCommission += amount
            break
          case 'leftover_commission':
          case 'leftover_credit':
          case 'leftover_education':
          case 'leftover_entertainment':
          case 'leftover_festival_bonus':
          case 'leftover_gift_center':
          case 'leftover_medical':
          case 'leftover_sales':
          case 'leftover_saving':
          case 'leftover_voucher':
            commissionBreakdown.leftoverCommission += amount
            break
          // ZM specific commissions
          case 'zm_bonus':
            commissionBreakdown.zmBonuses += amount
            break
          case 'zm_petral_allowance':
            commissionBreakdown.petralAllowanceZM += amount
            break
          case 'zm_leasing_facility':
            commissionBreakdown.leasingFacilityZM += amount
            break
          case 'zm_phone_bill':
            commissionBreakdown.phoneBillZM += amount
            break
          // RSM specific commissions
          case 'rsm_bonus':
            commissionBreakdown.rsmBonuses += amount
            break
          case 'rsm_petral_allowance':
            commissionBreakdown.petralAllowanceRSM += amount
            break
          case 'rsm_leasing_facility':
            commissionBreakdown.leasingFacilityRSM += amount
            break
          case 'rsm_phone_bill':
            commissionBreakdown.phoneBillRSM += amount
            break
          // Handle unified structure commissions
          case 'unified_structure':
            // These are typically level commissions
            commissionBreakdown.levelCommission += amount
            break
          case 'unified_structure_unallocated':
            // These go to OKDOI Head
            commissionBreakdown.okdoiHeadCommission += amount
            break
          default:
            // Log unknown commission types for debugging
            console.warn(`Unknown commission type: ${transaction.commission_type}`)
            break
        }
      })

      // Calculate total commissions
      commissionBreakdown.totalCommissions = Object.values(commissionBreakdown)
        .filter((value, index, array) => index < array.length - 1) // Exclude totalCommissions itself
        .reduce((sum, value) => sum + (typeof value === 'number' ? value : 0), 0)
    }

    // For admin requests, also include gift system commissions separately
    const url = new URL(request.url)
    const includeGiftSystem = url.searchParams.get('includeGiftSystem') === 'true'

    if (includeGiftSystem) {
      // Get gift system commissions separately for admin view
      const { data: giftSystemTransactions, error: giftError } = await supabaseAdmin
        .from('commission_transactions')
        .select('commission_type, commission_amount')
        .eq('beneficiary_id', targetUserId)
        .eq('status', 'processed')
        .in('commission_type', ['present_user', 'annual_present_user', 'present_leader', 'annual_present_leader'])

      if (!giftError && giftSystemTransactions) {
        const giftSystemBreakdown = {
          presentUser: 0,
          annualPresentUser: 0,
          presentLeader: 0,
          annualPresentLeader: 0,
          totalGiftSystem: 0
        }

        giftSystemTransactions.forEach(transaction => {
          const amount = parseFloat(transaction.commission_amount) || 0

          switch (transaction.commission_type) {
            case 'present_user':
              giftSystemBreakdown.presentUser += amount
              break
            case 'annual_present_user':
              giftSystemBreakdown.annualPresentUser += amount
              break
            case 'present_leader':
              giftSystemBreakdown.presentLeader += amount
              break
            case 'annual_present_leader':
              giftSystemBreakdown.annualPresentLeader += amount
              break
          }
          giftSystemBreakdown.totalGiftSystem += amount
        })

        return NextResponse.json({
          success: true,
          data: {
            ...commissionBreakdown,
            giftSystemCommissions: giftSystemBreakdown
          }
        })
      }
    }

    return NextResponse.json({
      success: true,
      data: commissionBreakdown
    })

  } catch (error) {
    console.error('GET /api/commission-breakdown error:', error)
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to fetch commission breakdown'
      },
      { status: 500 }
    )
  }
}
