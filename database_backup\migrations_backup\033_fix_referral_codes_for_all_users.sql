-- Migration: Fix referral codes for all users
-- Description: Ensure all existing users have referral codes, not just ZMs and RSMs

-- Update existing users who don't have referral codes
UPDATE users 
SET referral_code = generate_referral_code() 
WHERE referral_code IS NULL;

-- Verify the update worked
DO $$
DECLARE
    users_without_codes INTEGER;
BEGIN
    SELECT COUNT(*) INTO users_without_codes FROM users WHERE referral_code IS NULL;
    
    IF users_without_codes > 0 THEN
        RAISE NOTICE 'Warning: % users still without referral codes', users_without_codes;
    ELSE
        RAISE NOTICE 'Success: All users now have referral codes';
    END IF;
END $$;
