{"backup_timestamp": "2025-09-08T19:30:00.000Z", "project_info": {"name": "OKDOI Marketplace", "supabase_project_ref": "vnmydqbwjjufnxngpnqo", "supabase_url": "https://vnmydqbwjjufnxngpnqo.supabase.co", "region": "East US (North Virginia)", "created_at": "2025-08-27 10:07:48"}, "database_info": {"version": "PostgreSQL 17.4", "total_tables": 53, "total_records": 1201, "rls_enabled": true}, "application_info": {"framework": "Next.js", "database": "Supabase PostgreSQL", "authentication": "Supabase Auth", "storage": "Supabase Storage", "deployment": "Development"}, "backup_files": {"main_backup": "complete_backup_2025-09-08T19-27-34-847Z.json", "schema_backup": "schema_backup_2025-09-08T19-26-43-475Z.json", "migrations_backup": "migrations_backup/", "backup_summary": "backup_summary.json", "backup_report": "BACKUP_REPORT.md"}, "key_statistics": {"users": 77, "ads": 12, "shops": 5, "products": 6, "wallet_transactions": 49, "commission_transactions": 21, "cities": 271, "districts": 25, "migrations": 49}, "backup_verification": {"status": "VERIFIED", "all_critical_tables_present": true, "data_integrity_checks_passed": true, "backup_file_size_mb": 0.63, "verification_timestamp": "2025-09-08T19:30:00.000Z"}, "notes": ["Complete database backup successfully created", "All 53 tables backed up with 1,201 total records", "Schema and migrations preserved", "Backup verified and ready for use", "Store backup files securely and encrypt if needed"]}