'use client'

import { useState, useEffect } from 'react'
import { adminLogger, type AdminLogEntry } from '@/lib/adminLogger'
import { Bug, Download, Trash2, RefreshCw, AlertTriangle, Info, AlertCircle } from 'lucide-react'

export default function AdminDebugPanel() {
  const [isOpen, setIsOpen] = useState(false)
  const [logs, setLogs] = useState<AdminLogEntry[]>([])
  const [selectedCategory, setSelectedCategory] = useState<string>('all')
  const [selectedLevel, setSelectedLevel] = useState<string>('all')

  useEffect(() => {
    if (isOpen) {
      refreshLogs()
    }
  }, [isOpen])

  const refreshLogs = () => {
    setLogs(adminLogger.getLogs())
  }

  const clearLogs = () => {
    adminLogger.clearLogs()
    setLogs([])
  }

  const exportLogs = () => {
    const logsJson = adminLogger.exportLogs()
    const blob = new Blob([logsJson], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `admin-logs-${new Date().toISOString().split('T')[0]}.json`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }

  const filteredLogs = logs.filter(log => {
    const categoryMatch = selectedCategory === 'all' || log.category === selectedCategory
    const levelMatch = selectedLevel === 'all' || log.level === selectedLevel
    return categoryMatch && levelMatch
  })

  const issueSummary = adminLogger.getIssueSummary()

  const getLevelIcon = (level: string) => {
    switch (level) {
      case 'error':
        return <AlertTriangle className="h-4 w-4 text-red-500" />
      case 'warn':
        return <AlertCircle className="h-4 w-4 text-yellow-500" />
      case 'info':
        return <Info className="h-4 w-4 text-blue-500" />
      default:
        return <Bug className="h-4 w-4 text-gray-500" />
    }
  }

  const getLevelColor = (level: string) => {
    switch (level) {
      case 'error':
        return 'text-red-600 bg-red-50'
      case 'warn':
        return 'text-yellow-600 bg-yellow-50'
      case 'info':
        return 'text-blue-600 bg-blue-50'
      default:
        return 'text-gray-600 bg-gray-50'
    }
  }

  if (!adminLogger.isLoggingEnabled()) {
    return (
      <div className="fixed bottom-4 right-4 z-50">
        <button
          onClick={() => {
            adminLogger.setEnabled(true)
            setIsOpen(true)
          }}
          className="bg-gray-600 hover:bg-gray-700 text-white p-2 rounded-full shadow-lg transition-colors"
          title="Enable Debug Mode"
        >
          <Bug className="h-5 w-5" />
        </button>
      </div>
    )
  }

  return (
    <>
      {/* Debug Toggle Button */}
      <div className="fixed bottom-4 right-4 z-50">
        <button
          onClick={() => setIsOpen(!isOpen)}
          className={`p-2 rounded-full shadow-lg transition-colors ${
            issueSummary.totalErrors > 0
              ? 'bg-red-600 hover:bg-red-700 text-white animate-pulse'
              : 'bg-blue-600 hover:bg-blue-700 text-white'
          }`}
          title={`Debug Panel (${issueSummary.totalErrors} errors, ${issueSummary.totalWarnings} warnings)`}
        >
          <Bug className="h-5 w-5" />
        </button>
      </div>

      {/* Debug Panel */}
      {isOpen && (
        <div className="fixed inset-0 z-50 overflow-hidden">
          <div className="absolute inset-0 bg-black bg-opacity-50" onClick={() => setIsOpen(false)} />
          <div className="absolute right-0 top-0 h-full w-full max-w-2xl bg-white shadow-xl">
            <div className="flex flex-col h-full">
              {/* Header */}
              <div className="flex items-center justify-between p-4 border-b">
                <h2 className="text-lg font-semibold text-gray-900">Admin Debug Panel</h2>
                <div className="flex items-center space-x-2">
                  <button
                    onClick={refreshLogs}
                    className="p-2 text-gray-500 hover:text-gray-700 rounded"
                    title="Refresh Logs"
                  >
                    <RefreshCw className="h-4 w-4" />
                  </button>
                  <button
                    onClick={exportLogs}
                    className="p-2 text-gray-500 hover:text-gray-700 rounded"
                    title="Export Logs"
                  >
                    <Download className="h-4 w-4" />
                  </button>
                  <button
                    onClick={clearLogs}
                    className="p-2 text-gray-500 hover:text-gray-700 rounded"
                    title="Clear Logs"
                  >
                    <Trash2 className="h-4 w-4" />
                  </button>
                  <button
                    onClick={() => setIsOpen(false)}
                    className="p-2 text-gray-500 hover:text-gray-700 rounded"
                  >
                    ×
                  </button>
                </div>
              </div>

              {/* Summary */}
              <div className="p-4 bg-gray-50 border-b">
                <div className="grid grid-cols-3 gap-4 text-sm">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-red-600">{issueSummary.totalErrors}</div>
                    <div className="text-gray-600">Errors</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-yellow-600">{issueSummary.totalWarnings}</div>
                    <div className="text-gray-600">Warnings</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-blue-600">{logs.length}</div>
                    <div className="text-gray-600">Total Logs</div>
                  </div>
                </div>
              </div>

              {/* Filters */}
              <div className="p-4 border-b bg-white">
                <div className="flex space-x-4">
                  <select
                    value={selectedCategory}
                    onChange={(e) => setSelectedCategory(e.target.value)}
                    className="px-3 py-1 border rounded text-sm"
                  >
                    <option value="all">All Categories</option>
                    <option value="auth">Auth</option>
                    <option value="session">Session</option>
                    <option value="admin_check">Admin Check</option>
                    <option value="navigation">Navigation</option>
                    <option value="error">Error</option>
                  </select>
                  <select
                    value={selectedLevel}
                    onChange={(e) => setSelectedLevel(e.target.value)}
                    className="px-3 py-1 border rounded text-sm"
                  >
                    <option value="all">All Levels</option>
                    <option value="error">Errors</option>
                    <option value="warn">Warnings</option>
                    <option value="info">Info</option>
                    <option value="debug">Debug</option>
                  </select>
                </div>
              </div>

              {/* Logs */}
              <div className="flex-1 overflow-auto p-4">
                <div className="space-y-2">
                  {filteredLogs.length === 0 ? (
                    <div className="text-center text-gray-500 py-8">
                      No logs found matching the current filters.
                    </div>
                  ) : (
                    filteredLogs.reverse().map((log, index) => (
                      <div
                        key={index}
                        className={`p-3 rounded-lg border ${getLevelColor(log.level)}`}
                      >
                        <div className="flex items-start space-x-2">
                          {getLevelIcon(log.level)}
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center space-x-2 text-sm">
                              <span className="font-medium">{log.category}</span>
                              <span className="text-gray-500">
                                {new Date(log.timestamp).toLocaleTimeString()}
                              </span>
                              {log.userEmail && (
                                <span className="text-xs bg-gray-200 px-2 py-1 rounded">
                                  {log.userEmail}
                                </span>
                              )}
                            </div>
                            <div className="mt-1 text-sm">{log.message}</div>
                            {log.data && (
                              <details className="mt-2">
                                <summary className="cursor-pointer text-xs text-gray-600">
                                  Show Details
                                </summary>
                                <pre className="mt-1 text-xs bg-gray-100 p-2 rounded overflow-auto max-h-32">
                                  {JSON.stringify(log.data, null, 2)}
                                </pre>
                              </details>
                            )}
                          </div>
                        </div>
                      </div>
                    ))
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  )
}
