'use client'

import { ButtonHTMLAttributes, forwardRef } from 'react'
import { motion, HTMLMotionProps } from 'framer-motion'
import { clsx } from 'clsx'
import { buttonVariants } from '@/lib/animations'

interface ButtonProps extends Omit<ButtonHTMLAttributes<HTMLButtonElement>, 'type'> {
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'danger' | 'premium' | 'glass'
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl'
  loading?: boolean
  fullWidth?: boolean
  icon?: React.ReactNode
  iconPosition?: 'left' | 'right'
  animated?: boolean
  type?: 'button' | 'submit' | 'reset'
}

const Button = forwardRef<HTMLButtonElement, ButtonProps>(
  ({
    className,
    variant = 'primary',
    size = 'md',
    loading = false,
    fullWidth = false,
    disabled,
    children,
    icon,
    iconPosition = 'left',
    animated = true,
    type = 'button',
    ...props
  }, ref) => {
    const baseClasses = 'inline-flex items-center justify-center font-semibold transition-all duration-300 focus:outline-none focus:ring-4 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed relative overflow-hidden'
    
    const variants = {
      primary: 'bg-premium-gradient text-white shadow-lg hover:shadow-xl focus:ring-primary-blue/50 border-0',
      secondary: 'bg-white text-primary-blue border-2 border-primary-blue hover:bg-primary-blue hover:text-white focus:ring-primary-blue/50 shadow-md hover:shadow-lg',
      outline: 'border-2 border-primary-blue text-primary-blue bg-transparent hover:bg-primary-blue hover:text-white focus:ring-primary-blue/50',
      ghost: 'text-primary-blue bg-transparent hover:bg-primary-blue/10 focus:ring-primary-blue/50',
      danger: 'bg-gradient-to-r from-red-500 to-red-600 text-white shadow-lg hover:shadow-xl focus:ring-red-500/50 border-0',
      premium: 'bg-gradient-to-r from-primary-blue via-secondary-blue to-primary-600 text-white shadow-premium hover:shadow-glow focus:ring-primary-blue/50 border-0',
      glass: 'glass-card text-primary-blue hover:bg-white/20 focus:ring-primary-blue/50 backdrop-blur-md'
    }

    const sizes = {
      xs: 'px-2.5 py-1.5 text-xs',
      sm: 'px-3 py-2 text-sm',
      md: 'px-4 py-2.5 text-sm',
      lg: 'px-6 py-3 text-base',
      xl: 'px-8 py-4 text-lg'
    }

    const LoadingSpinner = () => (
      <motion.div
        className="absolute inset-0 flex items-center justify-center"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
      >
        <svg
          className="animate-spin h-5 w-5"
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
        >
          <circle
            className="opacity-25"
            cx="12"
            cy="12"
            r="10"
            stroke="currentColor"
            strokeWidth="4"
          />
          <path
            className="opacity-75"
            fill="currentColor"
            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
          />
        </svg>
      </motion.div>
    )

    const ButtonContent = () => (
      <span className={`flex items-center gap-2 ${loading ? 'opacity-0' : 'opacity-100'} transition-opacity duration-200`}>
        {icon && iconPosition === 'left' && (
          <span className="flex-shrink-0">{icon}</span>
        )}
        {children}
        {icon && iconPosition === 'right' && (
          <span className="flex-shrink-0">{icon}</span>
        )}
      </span>
    )

    const buttonClasses = clsx(
      baseClasses,
      variants[variant],
      sizes[size],
      fullWidth && 'w-full',
      loading && 'cursor-wait',
      className
    )

    if (animated) {
      return (
        <motion.button
          ref={ref}
          className={buttonClasses}
          disabled={disabled || loading}
          type={type}
          variants={buttonVariants}
          initial="rest"
          whileHover={!disabled && !loading ? "hover" : "rest"}
          whileTap={!disabled && !loading ? "tap" : "rest"}
          {...props}
        >
          <ButtonContent />
          {loading && <LoadingSpinner />}
        </motion.button>
      )
    }

    return (
      <button
        ref={ref}
        className={buttonClasses}
        disabled={disabled || loading}
        type={type}
        {...props}
      >
        <ButtonContent />
        {loading && <LoadingSpinner />}
      </button>
    )
  }
)

Button.displayName = 'Button'

export default Button

// Export as named export for compatibility
export { Button }
