import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'
import { CommissionSystemService } from '@/lib/services/commissionSystem'
import { PresentAllocationService } from '@/lib/services/presentAllocation'

// Admin client for server-side operations
const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  }
)

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { subscriptionId, userId, packagePrice } = body

    if (!subscriptionId || !userId || !packagePrice) {
      return NextResponse.json({ 
        error: 'subscriptionId, userId, and packagePrice are required' 
      }, { status: 400 })
    }

    console.log(`Processing background commissions for subscription ${subscriptionId}`)

    // Verify subscription exists and is valid
    const { data: subscription, error: subError } = await supabaseAdmin
      .from('user_subscriptions')
      .select('id, user_id, package_id')
      .eq('id', subscriptionId)
      .eq('user_id', userId)
      .single()

    if (subError || !subscription) {
      console.error('Subscription not found for commission processing:', subError)
      return NextResponse.json({ 
        error: 'Subscription not found' 
      }, { status: 404 })
    }

    let commissionResult = null
    let presentResult = null

    // Process commission distribution
    try {
      commissionResult = await CommissionSystemService.distributeCommissions(
        userId,
        subscriptionId,
        packagePrice
      )
      console.log(`Commission distribution completed for subscription ${subscriptionId}:`, commissionResult)
    } catch (commissionError) {
      console.error('Failed to distribute commissions:', commissionError)
      // Continue with present allocation even if commissions fail
    }

    // Process present allocations
    try {
      presentResult = await PresentAllocationService.allocatePresentsFromPurchase(
        userId,
        packagePrice,
        subscriptionId,
        `SUB_${subscriptionId}`
      )
      console.log(`Present allocation completed for subscription ${subscriptionId}`)
    } catch (presentError) {
      console.error('Present allocation failed:', presentError)
    }

    // Update subscription with processing status
    try {
      await supabaseAdmin
        .from('user_subscriptions')
        .update({ 
          commission_processed: commissionResult ? true : false,
          present_processed: presentResult ? true : false,
          processed_at: new Date().toISOString()
        })
        .eq('id', subscriptionId)
    } catch (updateError) {
      console.error('Failed to update subscription processing status:', updateError)
    }

    return NextResponse.json({
      success: true,
      message: 'Commission processing completed',
      data: {
        commissionResult,
        presentResult,
        subscriptionId
      }
    })

  } catch (error) {
    console.error('Error in commission processing API:', error)
    return NextResponse.json({
      error: error instanceof Error ? error.message : 'Internal server error'
    }, { status: 500 })
  }
}
