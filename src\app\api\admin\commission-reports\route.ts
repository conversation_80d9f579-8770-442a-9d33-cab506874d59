import { NextRequest, NextResponse } from 'next/server'
import { supabaseAdmin } from '@/lib/supabase'
import { AdminService } from '@/lib/services/admin'

export async function GET(request: NextRequest) {
  try {
    // Check admin authentication
    const isAdmin = await AdminService.isAdminServerSide()
    if (!isAdmin) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const limit = parseInt(searchParams.get('limit') || '50')
    const offset = parseInt(searchParams.get('offset') || '0')

    // Fetch subscription purchases with their commission transactions
    const { data: subscriptionPurchases, error } = await supabaseAdmin
      .from('user_subscriptions')
      .select(`
        id,
        user_id,
        package_id,
        purchased_at,
        user:users!user_subscriptions_user_id_fkey(
          id,
          email,
          full_name,
          user_type
        ),
        package:subscription_packages!user_subscriptions_package_id_fkey(
          id,
          name,
          price
        ),
        commission_transactions!commission_transactions_subscription_purchase_id_fkey(
          id,
          transaction_id,
          user_id,
          beneficiary_id,
          commission_type,
          commission_level,
          package_value,
          commission_rate,
          commission_amount,
          status,
          created_at,
          processed_at,
          user:users!commission_transactions_user_id_fkey(
            id,
            email,
            full_name,
            user_type
          ),
          beneficiary:users!commission_transactions_beneficiary_id_fkey(
            id,
            email,
            full_name,
            user_type
          )
        )
      `)
      .order('purchased_at', { ascending: false })
      .range(offset, offset + limit - 1)

    if (error) {
      console.error('Error fetching commission reports:', error)
      return NextResponse.json(
        { success: false, error: 'Failed to fetch commission reports' },
        { status: 500 }
      )
    }

    // Filter out purchases without commission transactions for cleaner display
    const purchasesWithCommissions = subscriptionPurchases?.filter(
      purchase => purchase.commission_transactions && purchase.commission_transactions.length > 0
    ) || []

    // Calculate statistics
    const stats = {
      totalPurchases: purchasesWithCommissions.length,
      totalCommissions: 0,
      totalTransactions: 0,
      uniqueUsers: new Set<string>(),
      byType: {} as Record<string, { count: number; amount: number }>,
      byStatus: {} as Record<string, { count: number; amount: number }>
    }

    purchasesWithCommissions.forEach(purchase => {
      stats.uniqueUsers.add(purchase.user_id)
      
      purchase.commission_transactions.forEach(transaction => {
        stats.totalCommissions += transaction.commission_amount
        stats.totalTransactions++

        // By type
        if (!stats.byType[transaction.commission_type]) {
          stats.byType[transaction.commission_type] = { count: 0, amount: 0 }
        }
        stats.byType[transaction.commission_type].count++
        stats.byType[transaction.commission_type].amount += transaction.commission_amount

        // By status
        if (!stats.byStatus[transaction.status]) {
          stats.byStatus[transaction.status] = { count: 0, amount: 0 }
        }
        stats.byStatus[transaction.status].count++
        stats.byStatus[transaction.status].amount += transaction.commission_amount
      })
    })

    const finalStats = {
      ...stats,
      uniqueUsers: stats.uniqueUsers.size
    }

    return NextResponse.json({
      success: true,
      data: {
        purchases: purchasesWithCommissions,
        stats: finalStats
      }
    })

  } catch (error) {
    console.error('GET /api/admin/commission-reports error:', error)
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to fetch commission reports'
      },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    // Check admin authentication
    const isAdmin = await AdminService.isAdminServerSide()
    if (!isAdmin) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const { action } = body

    if (action === 'export') {
      // Export commission transactions data
      const { data: allTransactions, error: exportError } = await supabaseAdmin
        .from('commission_transactions')
        .select(`
          *,
          user:users!commission_transactions_user_id_fkey(email, full_name),
          beneficiary:users!commission_transactions_beneficiary_id_fkey(email, full_name),
          subscription:user_subscriptions!commission_transactions_subscription_purchase_id_fkey(
            package:subscription_packages(name, price)
          )
        `)
        .order('created_at', { ascending: false })

      if (exportError) {
        throw new Error(`Failed to export transactions: ${exportError.message}`)
      }

      return NextResponse.json({
        success: true,
        data: allTransactions
      })
    }

    if (action === 'process-pending') {
      // Process pending commission transactions
      const { data: pendingTransactions, error: fetchError } = await supabaseAdmin
        .from('commission_transactions')
        .select('*')
        .eq('status', 'pending')
        .limit(100)

      if (fetchError) {
        throw new Error(`Failed to fetch pending transactions: ${fetchError.message}`)
      }

      let processedCount = 0
      let totalAmount = 0
      const errors: string[] = []

      for (const transaction of pendingTransactions || []) {
        try {
          // Update transaction status to processed
          const { error: updateError } = await supabaseAdmin
            .from('commission_transactions')
            .update({
              status: 'processed',
              processed_at: new Date().toISOString()
            })
            .eq('id', transaction.id)

          if (updateError) {
            errors.push(`Failed to process transaction ${transaction.id}: ${updateError.message}`)
            continue
          }

          // Credit the beneficiary's wallet
          const { error: walletError } = await supabaseAdmin.rpc('credit_user_wallet', {
            user_id: transaction.beneficiary_id,
            amount: transaction.commission_amount,
            transaction_type: 'commission',
            description: `Commission from ${transaction.commission_type} - Level ${transaction.commission_level}`,
            reference_id: transaction.id,
            reference_type: 'commission_transaction'
          })

          if (walletError) {
            errors.push(`Failed to credit wallet for transaction ${transaction.id}: ${walletError.message}`)
            // Revert transaction status
            await supabaseAdmin
              .from('commission_transactions')
              .update({ status: 'failed' })
              .eq('id', transaction.id)
            continue
          }

          processedCount++
          totalAmount += transaction.commission_amount

        } catch (transactionError) {
          errors.push(`Error processing transaction ${transaction.id}: ${transactionError}`)
        }
      }

      return NextResponse.json({
        success: true,
        data: {
          processed: processedCount,
          totalAmount,
          errors
        }
      })
    }

    return NextResponse.json(
      { success: false, error: 'Invalid action' },
      { status: 400 }
    )

  } catch (error) {
    console.error('POST /api/admin/commission-reports error:', error)
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to process request'
      },
      { status: 500 }
    )
  }
}
