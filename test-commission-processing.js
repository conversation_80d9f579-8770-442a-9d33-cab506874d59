// Test script to process pending commissions
const { CommissionSystemService } = require('./src/lib/services/commissionSystem.ts')

async function testCommissionProcessing() {
  try {
    console.log('Starting commission processing test...')
    
    const result = await CommissionSystemService.processPendingCommissions()
    
    console.log('Commission processing result:', result)
    console.log(`Processed: ${result.processed} commissions`)
    console.log(`Total amount: Rs ${result.totalAmount}`)
    
    if (result.errors.length > 0) {
      console.log('Errors:', result.errors)
    }
    
  } catch (error) {
    console.error('Error testing commission processing:', error)
  }
}

testCommissionProcessing()
