import { NextRequest, NextResponse } from 'next/server'
import { GiftTasksService } from '@/lib/services/giftTasksService'
import { AdminService } from '@/lib/services/admin'

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check admin authentication
    const isAdmin = await AdminService.isAdminServerSide()
    if (!isAdmin) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const taskId = params.id

    const task = await GiftTasksService.updateTask(taskId, body)

    return NextResponse.json({
      success: true,
      message: 'Task updated successfully',
      data: task
    })
  } catch (error) {
    console.error('PUT /api/admin/user-tasks/[id] error:', error)
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to update task'
      },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check admin authentication
    const isAdmin = await AdminService.isAdminServerSide()
    if (!isAdmin) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const taskId = params.id

    await GiftTasksService.deleteTask(taskId)

    return NextResponse.json({
      success: true,
      message: 'Task deleted successfully'
    })
  } catch (error) {
    console.error('DELETE /api/admin/user-tasks/[id] error:', error)
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to delete task'
      },
      { status: 500 }
    )
  }
}