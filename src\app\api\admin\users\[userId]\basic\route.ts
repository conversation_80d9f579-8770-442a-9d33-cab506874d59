import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  }
)

export async function GET(
  request: NextRequest,
  { params }: { params: { userId: string } }
) {
  try {
    const userId = params.userId

    // Fetch basic user information
    const { data: user, error } = await supabaseAdmin
      .from('users')
      .select('id, full_name, email, user_id')
      .eq('id', userId)
      .single()

    if (error) {
      console.error('Error fetching user basic info:', error)
      return NextResponse.json({ error: 'Failed to fetch user information' }, { status: 500 })
    }

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 })
    }

    return NextResponse.json(user)
  } catch (error) {
    console.error('Error in basic user info API:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
