import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'
import { AdminService } from '@/lib/services/admin'

// Create admin Supabase client with service role key
const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  }
)

interface ValidationResult {
  subscriptionId: string
  packageName: string
  packageValue: number
  totalCommissionsDistributed: number
  companyWalletAmount: number
  calculatedTotal: number
  isValid: boolean
  discrepancy: number
  expectedCommissions: number
  expectedCompanyProfit: number
}

export async function GET(request: NextRequest) {
  try {
    // Check admin authentication
    const isAdmin = await AdminService.isAdminServerSide()
    if (!isAdmin) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get all subscription purchases with their commission distributions
    const { data: subscriptions, error: subscriptionError } = await supabaseAdmin
      .from('user_subscriptions')
      .select(`
        id,
        user_id,
        created_at,
        subscription_packages (
          name,
          price
        )
      `)
      .order('created_at', { ascending: false })

    if (subscriptionError) {
      throw new Error(`Failed to fetch subscriptions: ${subscriptionError.message}`)
    }

    const validationResults: ValidationResult[] = []

    for (const subscription of subscriptions || []) {
      const subscriptionId = subscription.id
      const packageName = subscription.subscription_packages?.name || 'Unknown'
      const packageValue = parseFloat(subscription.subscription_packages?.price || '0')

      // Get total commissions distributed for this subscription
      const { data: commissions, error: commissionError } = await supabaseAdmin
        .from('commission_transactions')
        .select('commission_amount')
        .eq('subscription_purchase_id', subscriptionId)
        .eq('status', 'processed')

      if (commissionError) {
        console.error(`Error fetching commissions for ${subscriptionId}:`, commissionError)
        continue
      }

      const totalCommissionsDistributed = commissions?.reduce(
        (sum, c) => sum + parseFloat(c.commission_amount), 0
      ) || 0

      // Get company wallet amount for this subscription
      const { data: companyTransactions, error: companyError } = await supabaseAdmin
        .from('company_wallet_transactions')
        .select('amount')
        .eq('subscription_purchase_id', subscriptionId)
        .eq('transaction_type', 'company_profit')

      if (companyError) {
        console.error(`Error fetching company transactions for ${subscriptionId}:`, companyError)
        continue
      }

      const companyWalletAmount = companyTransactions?.reduce(
        (sum, c) => sum + parseFloat(c.amount), 0
      ) || 0

      // Calculate expected values based on package value
      let expectedCommissions = 0
      let expectedCompanyProfit = 0

      switch (packageValue) {
        case 2000:
          expectedCommissions = 1500
          expectedCompanyProfit = 500
          break
        case 5000:
          expectedCommissions = 4000
          expectedCompanyProfit = 1000
          break
        case 10000:
          expectedCommissions = 8000
          expectedCompanyProfit = 2000
          break
        case 50000:
          expectedCommissions = 40000
          expectedCompanyProfit = 10000
          break
        default:
          // Calculate based on 75% commissions, 25% company profit
          expectedCommissions = packageValue * 0.75
          expectedCompanyProfit = packageValue * 0.25
      }

      const calculatedTotal = totalCommissionsDistributed + companyWalletAmount
      const isValid = Math.abs(calculatedTotal - packageValue) < 0.01 // Allow for small rounding differences
      const discrepancy = packageValue - calculatedTotal

      validationResults.push({
        subscriptionId,
        packageName,
        packageValue,
        totalCommissionsDistributed,
        companyWalletAmount,
        calculatedTotal,
        isValid,
        discrepancy,
        expectedCommissions,
        expectedCompanyProfit
      })
    }

    // Calculate summary statistics
    const totalSubscriptions = validationResults.length
    const validSubscriptions = validationResults.filter(r => r.isValid).length
    const invalidSubscriptions = totalSubscriptions - validSubscriptions
    const totalDiscrepancy = validationResults.reduce((sum, r) => sum + Math.abs(r.discrepancy), 0)

    const summary = {
      totalSubscriptions,
      validSubscriptions,
      invalidSubscriptions,
      validationRate: totalSubscriptions > 0 ? (validSubscriptions / totalSubscriptions) * 100 : 0,
      totalDiscrepancy,
      averageDiscrepancy: totalSubscriptions > 0 ? totalDiscrepancy / totalSubscriptions : 0
    }

    return NextResponse.json({
      success: true,
      data: {
        summary,
        validationResults: validationResults.slice(0, 50), // Limit to first 50 for performance
        invalidResults: validationResults.filter(r => !r.isValid)
      }
    })

  } catch (error) {
    console.error('GET /api/admin/validate-commission-distribution error:', error)
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to validate commission distribution'
      },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    // Check admin authentication
    const isAdmin = await AdminService.isAdminServerSide()
    if (!isAdmin) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const { subscriptionId } = body

    if (!subscriptionId) {
      return NextResponse.json({ error: 'Subscription ID is required' }, { status: 400 })
    }

    // Get detailed breakdown for a specific subscription
    const { data: subscription, error: subscriptionError } = await supabaseAdmin
      .from('user_subscriptions')
      .select(`
        id,
        user_id,
        created_at,
        subscription_packages (
          name,
          price
        )
      `)
      .eq('id', subscriptionId)
      .single()

    if (subscriptionError) {
      throw new Error(`Failed to fetch subscription: ${subscriptionError.message}`)
    }

    // Get detailed commission breakdown
    const { data: commissions, error: commissionError } = await supabaseAdmin
      .from('commission_transactions')
      .select(`
        commission_type,
        commission_amount,
        beneficiary_id,
        users!commission_transactions_beneficiary_id_fkey (
          email,
          full_name
        )
      `)
      .eq('subscription_purchase_id', subscriptionId)
      .eq('status', 'processed')

    if (commissionError) {
      throw new Error(`Failed to fetch commissions: ${commissionError.message}`)
    }

    // Get company wallet transaction
    const { data: companyTransaction, error: companyError } = await supabaseAdmin
      .from('company_wallet_transactions')
      .select('*')
      .eq('subscription_purchase_id', subscriptionId)
      .eq('transaction_type', 'company_profit')
      .single()

    if (companyError && companyError.code !== 'PGRST116') {
      throw new Error(`Failed to fetch company transaction: ${companyError.message}`)
    }

    const packageValue = parseFloat(subscription.subscription_packages?.price || '0')
    const totalCommissions = commissions?.reduce((sum, c) => sum + parseFloat(c.commission_amount), 0) || 0
    const companyAmount = companyTransaction ? parseFloat(companyTransaction.amount) : 0

    return NextResponse.json({
      success: true,
      data: {
        subscription,
        commissions: commissions || [],
        companyTransaction,
        summary: {
          packageValue,
          totalCommissions,
          companyAmount,
          calculatedTotal: totalCommissions + companyAmount,
          discrepancy: packageValue - (totalCommissions + companyAmount),
          isValid: Math.abs(packageValue - (totalCommissions + companyAmount)) < 0.01
        }
      }
    })

  } catch (error) {
    console.error('POST /api/admin/validate-commission-distribution error:', error)
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to validate specific subscription'
      },
      { status: 500 }
    )
  }
}
