'use client'

import { useState, useEffect } from 'react'
import AdminLayout from '@/components/admin/AdminLayout'
import Button from '@/components/ui/Button'
import Card, { CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import Badge from '@/components/ui/badge'
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import Input from '@/components/ui/Input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Gift, TrendingUp, Users, Calendar, DollarSign, Download, RefreshCw } from 'lucide-react'
import { toast } from 'sonner'

interface PresentPool {
  pool_type: 'normal_present' | 'annual_present_user' | 'annual_present_leader'
  total_allocated: number
  total_distributed: number
  available_balance: number
  allocation_count: number
  last_updated: string
}

interface PresentAllocation {
  id: string
  user_id: string
  allocation_type: 'present_leader' | 'annual_present_user' | 'annual_present_leader'
  source_transaction_id?: string
  package_value: number
  allocation_amount: number
  allocation_date: string
  is_redeemed: boolean
  redeemed_at?: string
  redeemed_amount: number
  user: {
    email: string
    full_name?: string
  }
}

interface PresentStats {
  totalAllocations: number
  totalAmount: number
  totalRedeemed: number
  totalPending: number
  byType: Record<string, {
    count: number
    amount: number
    redeemed: number
  }>
}

export default function PresentManagementPage() {
  const [pools, setPools] = useState<PresentPool[]>([])
  const [allocations, setAllocations] = useState<PresentAllocation[]>([])
  const [stats, setStats] = useState<PresentStats | null>(null)
  const [loading, setLoading] = useState(true)
  const [activeTab, setActiveTab] = useState('overview')
  const [filters, setFilters] = useState({
    allocation_type: '',
    is_redeemed: '',
    date_from: '',
    date_to: ''
  })

  useEffect(() => {
    fetchPresentData()
  }, [])

  const fetchPresentData = async () => {
    try {
      setLoading(true)

      // Fetch pool summary
      const poolResponse = await fetch('/api/admin/present-management?action=pool-summary')
      const poolData = await poolResponse.json()

      if (poolData.success) {
        setPools(poolData.data)
      }

      // Fetch stats
      const statsResponse = await fetch('/api/admin/present-management?action=stats')
      const statsData = await statsResponse.json()

      if (statsData.success) {
        setStats(statsData.data)
      }

      // Fetch allocations
      await fetchAllocations()

    } catch (error) {
      console.error('Error fetching present data:', error)
      toast.error('Failed to fetch present data')
    } finally {
      setLoading(false)
    }
  }

  const fetchAllocations = async () => {
    try {
      const params = new URLSearchParams()

      if (filters.allocation_type) params.append('allocation_type', filters.allocation_type)
      if (filters.is_redeemed) params.append('is_redeemed', filters.is_redeemed)
      if (filters.date_from) params.append('date_from', filters.date_from)
      if (filters.date_to) params.append('date_to', filters.date_to)
      params.append('limit', '100')

      const response = await fetch(`/api/admin/present-management?${params}`)
      const data = await response.json()

      if (data.success) {
        setAllocations(data.data.allocations)
      }
    } catch (error) {
      console.error('Error fetching allocations:', error)
    }
  }

  const getPoolTypeLabel = (poolType: string) => {
    switch (poolType) {
      case 'normal_present':
        return 'Normal Present Pool'
      case 'annual_present_user':
        return 'Annual Present (Users)'
      case 'annual_present_leader':
        return 'Annual Present (Leaders)'
      default:
        return poolType
    }
  }

  const getAllocationTypeBadge = (allocationType: string) => {
    switch (allocationType) {
      case 'present_leader':
        return <Badge variant="default">Present Leader (2%)</Badge>
      case 'annual_present_user':
        return <Badge variant="secondary">Annual Present User (1%)</Badge>
      case 'annual_present_leader':
        return <Badge variant="outline">Annual Present Leader (2%)</Badge>
      default:
        return <Badge>{allocationType}</Badge>
    }
  }

  if (loading) {
    return (
      <AdminLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-amber-600 mx-auto"></div>
            <p className="mt-2 text-gray-600">Loading present data...</p>
          </div>
        </div>
      </AdminLayout>
    )
  }

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Present Management</h1>
            <p className="text-gray-600 mt-1">
              Monitor present pools, allocations, and redemptions (Admin-Only Visible)
            </p>
          </div>
          <div className="flex items-center gap-2">
            <Button variant="outline" onClick={fetchPresentData}>
              <RefreshCw className="w-4 h-4 mr-2" />
              Refresh
            </Button>
            <Button className="bg-amber-600 hover:bg-amber-700">
              <Download className="w-4 h-4 mr-2" />
              Export Data
            </Button>
          </div>
        </div>

        {/* Stats Overview */}
        {stats && (
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium text-gray-600">Total Allocations</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-gray-900">{stats.totalAllocations}</div>
                <p className="text-xs text-gray-500 mt-1">All present allocations</p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium text-gray-600">Total Amount</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-blue-600">
                  Rs {stats.totalAmount.toLocaleString()}
                </div>
                <p className="text-xs text-gray-500 mt-1">Total allocated</p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium text-gray-600">Total Redeemed</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-green-600">
                  Rs {stats.totalRedeemed.toLocaleString()}
                </div>
                <p className="text-xs text-gray-500 mt-1">Successfully redeemed</p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium text-gray-600">Pending Amount</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-orange-600">
                  Rs {stats.totalPending.toLocaleString()}
                </div>
                <p className="text-xs text-gray-500 mt-1">Awaiting redemption</p>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Present Pool Overview */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {pools.map((pool) => (
            <Card key={pool.pool_type}>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Gift className="w-5 h-5 text-amber-600" />
                  {getPoolTypeLabel(pool.pool_type)}
                </CardTitle>
                <p className="text-sm text-gray-500">
                  {pool.allocation_count} allocations
                </p>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600">Total Allocated:</span>
                    <span className="font-medium">Rs {pool.total_allocated.toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600">Total Distributed:</span>
                    <span className="font-medium text-green-600">Rs {pool.total_distributed.toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600">Available Balance:</span>
                    <span className="font-medium text-blue-600">Rs {pool.available_balance.toLocaleString()}</span>
                  </div>
                </div>

                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className="bg-amber-600 h-2 rounded-full"
                    style={{
                      width: `${pool.total_allocated > 0 ? (pool.total_distributed / pool.total_allocated) * 100 : 0}%`
                    }}
                  ></div>
                </div>

                <p className="text-xs text-gray-500">
                  Last updated: {new Date(pool.last_updated).toLocaleString()}
                </p>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Recent Allocations */}
        <Card>
          <CardHeader>
            <CardTitle>Recent Present Allocations</CardTitle>
            <p className="text-sm text-gray-500">
              Latest present allocations from package purchases (Admin-Only Visible)
            </p>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>User</TableHead>
                  <TableHead>Type</TableHead>
                  <TableHead>Package Value</TableHead>
                  <TableHead>Allocation Amount</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Date</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {allocations.slice(0, 10).map((allocation) => (
                  <TableRow key={allocation.id}>
                    <TableCell>
                      <div>
                        <div className="font-medium">{allocation.user.email}</div>
                        {allocation.user.full_name && (
                          <div className="text-sm text-gray-500">{allocation.user.full_name}</div>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>{getAllocationTypeBadge(allocation.allocation_type)}</TableCell>
                    <TableCell>Rs {allocation.package_value.toLocaleString()}</TableCell>
                    <TableCell>
                      <div className="font-medium">Rs {allocation.allocation_amount.toLocaleString()}</div>
                    </TableCell>
                    <TableCell>
                      <Badge variant={allocation.is_redeemed ? "default" : "secondary"}>
                        {allocation.is_redeemed ? 'Redeemed' : 'Pending'}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      {new Date(allocation.allocation_date).toLocaleDateString()}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>

            {allocations.length === 0 && (
              <div className="text-center py-8">
                <Gift className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No allocations found</h3>
                <p className="text-gray-500">
                  Present allocations will appear here when users purchase subscription packages.
                </p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </AdminLayout>
  )
}