-- Simple fix for user profile creation during signup
-- Add INSERT policy if it doesn't exist

-- Check if the INSERT policy exists, if not create it
DO $$
BEGIN
    -- Check if any INSERT policy exists for users table
    IF NOT EXISTS (
        SELECT 1 FROM pg_policies 
        WHERE tablename = 'users' 
        AND cmd = 'INSERT'
        AND policyname LIKE '%insert%'
    ) THEN
        -- Create INSERT policy for authenticated users
        EXECUTE 'CREATE POLICY "Users can insert own profile" ON users FOR INSERT WITH CHECK (auth.uid() = id)';
        RAISE NOTICE 'Created INSERT policy for users table';
    ELSE
        RAISE NOTICE 'INSERT policy already exists for users table';
    END IF;
END $$;

-- Ensure the trigger for referral code generation is working
-- Re-create the simple referral code trigger if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_trigger 
        WHERE tgname = 'trigger_auto_generate_simple_referral_code'
    ) THEN
        -- Create the trigger
        CREATE TRIGGER trigger_auto_generate_simple_referral_code
            BEFORE INSERT ON users
            FOR EACH ROW
            EXECUTE FUNCTION auto_generate_simple_referral_code();
        RAISE NOTICE 'Created referral code generation trigger';
    ELSE
        RAISE NOTICE 'Referral code generation trigger already exists';
    END IF;
END $$;
