import { NextRequest, NextResponse } from 'next/server'
import { supabaseAdmin } from '@/lib/supabase'

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params

    if (!id) {
      return NextResponse.json(
        { error: 'Structure ID is required' },
        { status: 400 }
      )
    }

    // Get the commission structure by ID
    const { data: structure, error } = await supabaseAdmin
      .from('commission_structure')
      .select('*')
      .eq('id', id)
      .single()

    if (error) {
      return NextResponse.json(
        { error: `Failed to fetch commission structure: ${error.message}` },
        { status: 500 }
      )
    }

    if (!structure) {
      return NextResponse.json(
        { error: 'Commission structure not found' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      data: structure
    })

  } catch (error) {
    console.error('Error fetching commission structure:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params

    if (!id) {
      return NextResponse.json(
        { error: 'Structure ID is required' },
        { status: 400 }
      )
    }

    const body = await request.json()

    // Debug: Log the incoming request body
    console.log('Commission structure update request body:', JSON.stringify(body, null, 2))

    // First get the current structure to preserve existing OKDOI Head rates
    const { data: currentStructure, error: fetchError } = await supabaseAdmin
      .from('commission_structure')
      .select('*')
      .eq('id', id)
      .single()

    if (fetchError) {
      return NextResponse.json(
        { error: `Failed to fetch current structure: ${fetchError.message}` },
        { status: 500 }
      )
    }

    // Prepare update object with absolute amounts
    const updateData: any = {
      // Budget configuration
      network_distribution_budget: body.network_distribution_budget !== undefined ? body.network_distribution_budget : currentStructure.network_distribution_budget,
      company_wallet_amount: body.company_wallet_amount !== undefined ? body.company_wallet_amount : currentStructure.company_wallet_amount,
      // Commission amounts
      direct_commission_amount: body.direct_commission_amount !== undefined ? body.direct_commission_amount : currentStructure.direct_commission_amount,
      level_commission_amount: body.level_commission_amount !== undefined ? body.level_commission_amount : currentStructure.level_commission_amount,
      voucher_amount: body.voucher_amount !== undefined ? body.voucher_amount : currentStructure.voucher_amount,
      festival_bonus_amount: body.festival_bonus_amount !== undefined ? body.festival_bonus_amount : currentStructure.festival_bonus_amount,
      saving_amount: body.saving_amount !== undefined ? body.saving_amount : currentStructure.saving_amount,
      gift_center_amount: body.gift_center_amount !== undefined ? body.gift_center_amount : currentStructure.gift_center_amount,
      entertainment_amount: body.entertainment_amount !== undefined ? body.entertainment_amount : currentStructure.entertainment_amount,
      medical_amount: body.medical_amount !== undefined ? body.medical_amount : currentStructure.medical_amount,
      education_amount: body.education_amount !== undefined ? body.education_amount : currentStructure.education_amount,
      credit_amount: body.credit_amount !== undefined ? body.credit_amount : currentStructure.credit_amount,
      // ZM and RSM amounts
      zm_bonus_amount: body.zm_bonus_amount !== undefined ? body.zm_bonus_amount : currentStructure.zm_bonus_amount,
      zm_petral_allowance_amount: body.zm_petral_allowance_amount !== undefined ? body.zm_petral_allowance_amount : currentStructure.zm_petral_allowance_amount,
      zm_leasing_facility_amount: body.zm_leasing_facility_amount !== undefined ? body.zm_leasing_facility_amount : currentStructure.zm_leasing_facility_amount,
      zm_phone_bill_amount: body.zm_phone_bill_amount !== undefined ? body.zm_phone_bill_amount : currentStructure.zm_phone_bill_amount,
      rsm_bonus_amount: body.rsm_bonus_amount !== undefined ? body.rsm_bonus_amount : currentStructure.rsm_bonus_amount,
      rsm_petral_allowance_amount: body.rsm_petral_allowance_amount !== undefined ? body.rsm_petral_allowance_amount : currentStructure.rsm_petral_allowance_amount,
      rsm_leasing_facility_amount: body.rsm_leasing_facility_amount !== undefined ? body.rsm_leasing_facility_amount : currentStructure.rsm_leasing_facility_amount,
      rsm_phone_bill_amount: body.rsm_phone_bill_amount !== undefined ? body.rsm_phone_bill_amount : currentStructure.rsm_phone_bill_amount,
      // Gift system amounts
      present_user_amount: body.present_user_amount !== undefined ? body.present_user_amount : currentStructure.present_user_amount,
      present_leader_amount: body.present_leader_amount !== undefined ? body.present_leader_amount : currentStructure.present_leader_amount,
      annual_present_user_amount: body.annual_present_user_amount !== undefined ? body.annual_present_user_amount : currentStructure.annual_present_user_amount,
      annual_present_leader_amount: body.annual_present_leader_amount !== undefined ? body.annual_present_leader_amount : currentStructure.annual_present_leader_amount,
      // OKDOI Head amounts (preserve existing values, only update if provided)
      okdoi_head_amount_2000: body.okdoi_head_amount_2000 !== undefined ? body.okdoi_head_amount_2000 : currentStructure.okdoi_head_amount_2000,
      okdoi_head_amount_5000: body.okdoi_head_amount_5000 !== undefined ? body.okdoi_head_amount_5000 : currentStructure.okdoi_head_amount_5000,
      okdoi_head_amount_10000: body.okdoi_head_amount_10000 !== undefined ? body.okdoi_head_amount_10000 : currentStructure.okdoi_head_amount_10000,
      okdoi_head_amount_50000: body.okdoi_head_amount_50000 !== undefined ? body.okdoi_head_amount_50000 : currentStructure.okdoi_head_amount_50000,
      is_active: body.is_active !== undefined ? body.is_active : currentStructure.is_active,
      updated_at: new Date().toISOString()
    }

    // Debug: Log the update data being sent to database
    console.log('Update data being sent to database:', JSON.stringify(updateData, null, 2))

    // Update unified commission structure
    const { data: updatedStructure, error } = await supabaseAdmin
      .from('commission_structure')
      .update(updateData)
      .eq('id', id)
      .select()
      .single()

    if (error) {
      console.error('Supabase error:', error)
      return NextResponse.json(
        { error: `Failed to update commission structure: ${error.message}` },
        { status: 500 }
      )
    }

    if (!updatedStructure) {
      return NextResponse.json(
        { error: 'Commission structure not found' },
        { status: 404 }
      )
    }

    // Debug: Log the updated structure returned from database
    console.log('Updated structure returned from database:', JSON.stringify(updatedStructure, null, 2))

    return NextResponse.json({
      success: true,
      data: updatedStructure,
      message: 'Commission structure updated successfully'
    })

  } catch (error) {
    console.error('Error updating commission structure:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params

    if (!id) {
      return NextResponse.json(
        { error: 'Structure ID is required' },
        { status: 400 }
      )
    }

    // Delete commission structure
    const { error } = await supabaseAdmin
      .from('commission_structure')
      .delete()
      .eq('id', id)

    if (error) {
      console.error('Supabase error:', error)
      return NextResponse.json(
        { error: `Failed to delete commission structure: ${error.message}` },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      message: 'Commission structure deleted successfully'
    })

  } catch (error) {
    console.error('Error deleting commission structure:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
