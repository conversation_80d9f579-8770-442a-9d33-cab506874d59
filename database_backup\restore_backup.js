const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');

// Load environment variables
require('dotenv').config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase configuration in .env.local');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function restoreTable(tableName, data) {
  console.log(`Restoring table: ${tableName} (${data.length} records)`);
  
  if (data.length === 0) {
    console.log(`  ⚠️  No data to restore for ${tableName}`);
    return { success: true, count: 0 };
  }
  
  try {
    // Insert data in batches to avoid timeout
    const batchSize = 100;
    let totalInserted = 0;
    
    for (let i = 0; i < data.length; i += batchSize) {
      const batch = data.slice(i, i + batchSize);
      
      const { data: insertedData, error } = await supabase
        .from(tableName)
        .insert(batch);
      
      if (error) {
        console.error(`  ❌ Error inserting batch for ${tableName}:`, error);
        return { success: false, error: error.message };
      }
      
      totalInserted += batch.length;
      console.log(`  ✓ Inserted ${totalInserted}/${data.length} records`);
    }
    
    return { success: true, count: totalInserted };
  } catch (err) {
    console.error(`  ❌ Exception restoring ${tableName}:`, err.message);
    return { success: false, error: err.message };
  }
}

async function clearTable(tableName) {
  console.log(`Clearing table: ${tableName}`);
  
  try {
    const { error } = await supabase
      .from(tableName)
      .delete()
      .neq('id', '00000000-0000-0000-0000-000000000000'); // Delete all records
    
    if (error) {
      console.error(`  ❌ Error clearing ${tableName}:`, error);
      return false;
    }
    
    console.log(`  ✓ Cleared ${tableName}`);
    return true;
  } catch (err) {
    console.error(`  ❌ Exception clearing ${tableName}:`, err.message);
    return false;
  }
}

async function restoreFromBackup(backupFilePath, options = {}) {
  const { clearTables = false, specificTables = null } = options;
  
  console.log('Starting database restore...');
  console.log(`📁 Reading backup from: ${backupFilePath}`);
  
  if (!fs.existsSync(backupFilePath)) {
    console.error('❌ Backup file not found!');
    return;
  }
  
  const backup = JSON.parse(fs.readFileSync(backupFilePath, 'utf8'));
  
  console.log(`📊 Backup timestamp: ${backup.timestamp}`);
  console.log(`📊 Total tables in backup: ${Object.keys(backup.tables).length}`);
  
  const results = {
    success: [],
    failed: [],
    skipped: []
  };
  
  // Determine which tables to restore
  const tablesToRestore = specificTables || Object.keys(backup.tables);
  
  for (const tableName of tablesToRestore) {
    if (!backup.tables[tableName]) {
      console.log(`⚠️  Table ${tableName} not found in backup, skipping`);
      results.skipped.push(tableName);
      continue;
    }
    
    const tableData = backup.tables[tableName].data;
    
    // Clear table if requested
    if (clearTables) {
      const cleared = await clearTable(tableName);
      if (!cleared) {
        results.failed.push({ table: tableName, error: 'Failed to clear table' });
        continue;
      }
    }
    
    // Restore table data
    const result = await restoreTable(tableName, tableData);
    
    if (result.success) {
      results.success.push({ table: tableName, count: result.count });
    } else {
      results.failed.push({ table: tableName, error: result.error });
    }
  }
  
  // Print summary
  console.log('\n🎉 Restore completed!');
  console.log(`✅ Successfully restored: ${results.success.length} tables`);
  console.log(`❌ Failed to restore: ${results.failed.length} tables`);
  console.log(`⚠️  Skipped: ${results.skipped.length} tables`);
  
  if (results.success.length > 0) {
    console.log('\n✅ Successfully restored tables:');
    results.success.forEach(({ table, count }) => {
      console.log(`  - ${table}: ${count} records`);
    });
  }
  
  if (results.failed.length > 0) {
    console.log('\n❌ Failed to restore tables:');
    results.failed.forEach(({ table, error }) => {
      console.log(`  - ${table}: ${error}`);
    });
  }
  
  return results;
}

// Command line usage
const args = process.argv.slice(2);

if (args.length === 0) {
  console.log('Usage: node restore_backup.js <backup_file> [options]');
  console.log('Options:');
  console.log('  --clear-tables    Clear existing data before restore');
  console.log('  --tables table1,table2,table3    Restore only specific tables');
  console.log('');
  console.log('Example:');
  console.log('  node restore_backup.js complete_backup_2025-01-09T12-00-00-000Z.json');
  console.log('  node restore_backup.js complete_backup_2025-01-09T12-00-00-000Z.json --clear-tables');
  console.log('  node restore_backup.js complete_backup_2025-01-09T12-00-00-000Z.json --tables users,ads');
  process.exit(1);
}

const backupFile = args[0];
const clearTables = args.includes('--clear-tables');
const tablesArg = args.find(arg => arg.startsWith('--tables'));
const specificTables = tablesArg ? tablesArg.split('=')[1]?.split(',') : null;

restoreFromBackup(backupFile, { clearTables, specificTables }).catch(console.error);
