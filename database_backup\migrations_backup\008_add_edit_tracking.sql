-- Add edit tracking fields to ads table
ALTER TABLE ads ADD COLUMN IF NOT EXISTS is_edited BOOLEAN DEFAULT FALSE;
ALTER TABLE ads ADD COLUMN IF NOT EXISTS edit_reason TEXT;
ALTER TABLE ads ADD COLUMN IF NOT EXISTS original_data JSONB;

-- Create index for edited ads
CREATE INDEX IF NOT EXISTS idx_ads_is_edited ON ads(is_edited);

-- Update RLS policies to allow users to update their own ads
-- (This should already exist but ensuring it's correct)
DROP POLICY IF EXISTS "Users can update their own ads" ON ads;
CREATE POLICY "Users can update their own ads" ON ads
    FOR UPDATE USING (auth.uid() = user_id);
